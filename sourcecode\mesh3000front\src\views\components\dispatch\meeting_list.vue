<template>
    <!-- 会议列表 -->
    <div v-if="selectShowMeeting==1">
        <dv-border-box-12 style="padding:1%;">
        <a-button @click="handleAdd" style="margin-bottom: 1%;margin-top: 1%;margin-left: 1%">
        <PlusOutlined style="color:rgb(122, 242, 170)" />
        创建
        </a-button>
        <a-table bordered :data-source="meeting_dataSource" :columns="columns" class="meet-list-table">
        <template #emptyText>
            <empty-table />
        </template>
        <template #operation="{ record }">
            <a-row :span="24">
            <a-col :span="6">
                <a-button @click="details(record.key)">
                <EyeOutlined style="color:rgb(122, 242, 170)" />
                详情
                </a-button>
            </a-col>
            <a-col :span="6">
                <a-button @click="enterMeeting(record.key)">
                <LoginOutlined style="color:rgb(122, 242, 170)" />
                进入
                </a-button>
            </a-col>
            <a-col :span="6">
                <a-popconfirm v-if="meeting_dataSource.length" title="确定结束此会议？" ok-text="确定" cancel-text="取消"  @confirm="onStopMeeting(record.key)">
                <a-button danger>
                    <DeleteOutlined style="color:red" />
                    结束会议
                </a-button>
                </a-popconfirm>
            </a-col>
            </a-row>
        </template>
        </a-table>
        </dv-border-box-12>
    </div>

     <!-- 当前会议 -->
    <div v-if="selectShowMeeting==2 && !showAllMeeting" style="overflow: auto;">
        <dv-border-box-12 style="padding:1%;">
        <div  v-if="meeting_dataSource.length == 0" style="top:45%;left: 45%; position: absolute;">
            <a-row justify="center" align="middle"><loding2 ></loding2></a-row>
        </div>
      <a-row :gutter="12">
            <a-col v-for="(item,index) in meeting_dataSource" v-bind:key="index" :span="6" style="padding-top: 10px;height: 40%">
                <div :id="index" @mouseenter="mouseenterCard(index)" @mouseleave="mouseleaveCard(index)" @click="enterMeeting(index)">
                <dv-border-box-10 :id="index+'-meet'" :color="meeting_dataSource[index].color" >
                    <a-card v-if="name!==null&&name!==''" class="a-card"  :bordered="false" >
                        <template #title>
                            <a-row span="24">
                                <a-col span="20">
                                    <img class="card-img" src="../../../assets/img/meeting.png"/>
                                    会议名称：
                                    {{meeting_dataSource[index].name}}
                                </a-col>
                            </a-row>
                        </template>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/info.png"/></a-col>
                            <a-col span="8" align="center"> 会议描述：</a-col>
                            <a-col span="11" align="start">{{meeting_dataSource[index].description}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/host-card/number.png"/></a-col>
                            <a-col span="8" align="center">会议号码：</a-col>
                            <a-col span="11" align="start">{{meeting_dataSource[index].number}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/master.png"/></a-col>
                            <a-col span="8" align="center"> 主持人：</a-col>
                            <a-col span="11" align="start">{{meeting_dataSource[index].master}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/sum_people.png"/></a-col>
                            <a-col span="8" align="center"> 参会人数：</a-col>
                            <a-col span="11" align="start">{{meeting_dataSource[index].people_number}}</a-col>
                        </a-row>
                    </a-card>
                </dv-border-box-10>
                </div>
            </a-col>
      </a-row>
    </dv-border-box-12>
    </div>

      <!-- 当前会议，会议内 -->
    <div v-if="selectShowMeeting==2 && showAllMeeting" style="overflow: auto; height: 100%;">
        <dv-border-box-12 style="padding:1%;height: 100%;">
        <div  v-if="meetMember.length == 0" style="top:45%;left: 45%; position: absolute;">
            <a-row justify="center" align="middle"><loding2 ></loding2></a-row>
        </div>
      <a-row :gutter="12">
            <a-col v-for="(item,index) in meetMember" v-bind:key="index" :span="6" style="padding-top: 10px;height: 40%">
                <div :id="index" @mouseenter="mouseenterCardForPersonnel(index)" @mouseleave="mouseleaveCardForPersonnel(index)" @click="selectedMember(index)">
                <dv-border-box-10 :id="index+'-meet'" :color="meetMember[index].color" >
                    <a-card class="a-card"  :bordered="false" >
                        <template #title>
                            <a-row span="24">
                            <!-- <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/meeting.png"/></a-col> -->

                            <a-col span="8" align="center">姓名：</a-col>
                            <a-col span="10" align="start">{{meetMember[index].name}}</a-col>
                            <a-col span="2" align="start" ><img  v-if="!item.can_speak " class="card-img" src="../../../assets/img/no_speak.png"/></a-col>
                            <a-col span="2" align="start" ><img  v-if="item.isolatestatus" class="card-img" src="../../../assets/img/gl.png"/></a-col>
                            </a-row>
                        </template>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/host-card/number.png"/></a-col>
                            <a-col span="8" align="center">号码：</a-col>
                            <a-col span="11" align="start">{{meetMember[index].callerIdNumber}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/master.png"/></a-col>
                            <a-col span="8" align="center"> 部门：</a-col>
                            <a-col span="11" align="start">{{meetMember[index].department}}</a-col>
                        </a-row>
                    </a-card>
                </dv-border-box-10>
                </div>
            </a-col>
      </a-row>
    </dv-border-box-12>
    <!-- 底下控制条 -->
    <div  class="ctrl-bar" style="position: absolute;bottom: 0px;" >
        <dv-border-box-12>
            <a-row justify="center" align="middle" style="height: 100%;">
                <a-col :span="2.66" align="middle">
                    <div id="j-y" @click="ctrlBarClick($event)">
                        <button2 button_name="禁言" ></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="dis-j-y" @click="ctrlBarClick($event)">
                        <button2 button_name="解除禁言" ></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="g-l"  @click="ctrlBarClick($event)">
                        <button2 button_name="隔离"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="dis-g-l"  @click="ctrlBarClick($event)">
                        <button2 button_name="解除隔离"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="y-q"  @click="show_invite()">
                        <button2 button_name="邀请"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="t-c"  @click="ctrlBarClick($event)">
                        <button2 button_name="踢出"></button2>
                    </div>
                </a-col>
            </a-row>
        </dv-border-box-12>
    </div>
    </div>

        <!-- 历史会议 -->
    <div v-if="selectShowMeeting==3">
        <dv-border-box-12 style="padding:1%;">
            <div class="meet-list-table">
        <a-table bordered :data-source="his_meeting_dataSource_display" :columns="columnsHis" class="meet-list-table">
        <template #emptyText>
            <empty-table />
        </template>
        <template #operation="{ record }">
            <a-row justify="space-around">
            <a-col @click="his_details(record.key)">
                <a-button @click="his_details(record.key)">
                <EyeOutlined style="color:rgb(122, 242, 170)" />
                详情
                </a-button>
            </a-col>
            <a-col>
                <a-button @click="his_restartMeeting(record.key)">
                <LoginOutlined style="color:rgb(122, 242, 170)" />
                重新召开
                </a-button>
            </a-col>
            <!-- <a-col >
                <a-popconfirm v-if="his_meeting_dataSource_display.length" title="确定删除此条记录?" @confirm="his_delete(record.key)">
                <a-button danger>
                    <DeleteOutlined style="color:red" />
                    删除
                </a-button>
                </a-popconfirm>
            </a-col> -->
            </a-row>
        </template>
        </a-table>
    </div>
    </dv-border-box-12>
    </div>
    <!-- 创建会议modal -->
  <a-modal v-model:visible="showAddMeetingModal" title="创建会议室" @ok="handleCreateSubmit" ok-text="确认"
  cancel-text="取消" style="width: 50%;">
    <a-form :model="formState" ref="createFormRef">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <apple-outlined />
              基本设置
            </span>
          </template>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议号码：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetNumner"
                :rules="[
                  { required: true, message: '请输入会议号码' },
                  { pattern: /^\d{8,13}$/, message: '只能输入8-13位数字' }
                ]"
              >
                <a-input
                  v-model:value="meetNumner"
                  placeholder="请输入会议号码"
                  maxlength="13"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议名称：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetName"
                :rules="[
                  { required: true, message: '请输入会议名称' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetName" placeholder="请输入会议名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议主题：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetTheme"
                :rules="[
                  { required: true, message: '请输入会议主题' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetTheme" placeholder="请输入会议主题"/>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <android-outlined />
              人员设置
            </span>
          </template>
          <a-transfer
              :data-source="mockData"
              show-search :filter-option="filterOption"
              :target-keys="targetKeys"
              :render="renderTransferItem"
              :locale="{
                          itemUnit: '项',
                          itemsUnit: '项',
                          notFoundContent: '列表为空',
                          searchPlaceholder: '请输入搜索内容'
                      }"
              :titles="['待选成员','已选成员']"
              @change="handleChange"
              @search="handleSearch"
              :list-style="{
                              height: '30%',
                          }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
  <!-- 重新召开会议modal -->
  <a-modal v-model:visible="showRestartMeetingModal" title="创建会议室" @ok="handleRestartSubmit" ok-text="确定召开"
  cancel-text="取消" style="width: 50%;">
    <a-form :model="formState" ref="restartFormRef">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <apple-outlined />
              基本设置
            </span>
          </template>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议号码：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetNumner"
                :rules="[
                  { required: true, message: '请输入会议号码' },
                  { type: 'number', message: '只能输入数字' },
                  { min: 8, max: 13, message: '长度必须在8-13位之间', type: 'string' }
                ]"
              >
                <a-input
                  v-model:value="meetNumner"
                  placeholder="请输入会议号码"
                  maxlength="13"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议名称：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetName"
                :rules="[
                  { required: true, message: '请输入会议名称' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetName" placeholder="请输入会议名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议主题：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetTheme"
                :rules="[
                  { required: true, message: '请输入会议主题' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetTheme" placeholder="请输入会议主题"/>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <android-outlined />
              人员设置
            </span>
          </template>
          <a-transfer
              :data-source="mockData"
              show-search :filter-option="filterOption"
              :target-keys="targetKeys"
              :render="renderTransferItem"
              :locale="{
                          itemUnit: '项',
                          itemsUnit: '项',
                          notFoundContent: '列表为空',
                          searchPlaceholder: '请输入搜索内容'
                      }"
              :titles="['待选成员','已选成员']"
              @change="handleChange"
              @search="handleSearch"
              :list-style="{
                              height: '30%',
                          }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>

  <!-- 会议详情modal -->
  <a-modal v-model:visible="showdetailsMeetingModal" title="会议详情" ok-text="确认"
  cancel-text="取消" style="width: 50%;">
    <a-form :model="formState" ref="detailFormRef">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <apple-outlined />
              基本信息
            </span>
          </template>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议号码：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetNumner"
                :rules="[
                  { required: true, message: '请输入会议号码' },
                  { type: 'number', message: '只能输入数字' },
                  { min: 8, max: 13, message: '长度必须在8-13位之间', type: 'string' }
                ]"
              >
                <a-input
                  v-model:value="meetNumner"
                  placeholder="请输入会议号码"
                  maxlength="13"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议名称：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetName"
                :rules="[
                  { required: true, message: '请输入会议名称' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetName" placeholder="请输入会议名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议主题：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetTheme"
                :rules="[
                  { required: true, message: '请输入会议主题' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetTheme" placeholder="请输入会议主题"/>
              </a-form-item>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <android-outlined />
              参会人员
            </span>
          </template>
            <div>
                <a-row>
                  <a-col v-for="member in meetMember" :key="member">
                    <a-tag color="#108ee9">{{member.name}}</a-tag>
                  </a-col>
                </a-row>
            </div>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>

    <!-- 历史会议modal -->
    <a-modal v-model:visible="showhisdetailsMeetingModal" title="历史会议详情" ok-text="确认"
    cancel-text="取消" style="width: 50%;">
    <a-form :model="formState" ref="historyFormRef">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <apple-outlined />
              基本信息
            </span>
          </template>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议号码：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetNumner"
                :rules="[
                  { required: true, message: '请输入会议号码' },
                  { type: 'number', message: '只能输入数字' },
                  { min: 8, max: 13, message: '长度必须在8-13位之间', type: 'string' }
                ]"
              >
                <a-input
                  v-model:value="meetNumner"
                  placeholder="请输入会议号码"
                  maxlength="13"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议名称：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetName"
                :rules="[
                  { required: true, message: '请输入会议名称' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetName" placeholder="请输入会议名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议主题：</a-col>
            <a-col :span="12">
              <a-form-item
                name="meetTheme"
                :rules="[
                  { required: true, message: '请输入会议主题' },
                  { min: 3, max: 16, message: '长度必须在3-16个字符之间', type: 'string' }
                ]"
              >
                <a-input v-model:value="meetTheme" placeholder="请输入会议主题"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top: 3%;">
            <a-col :span="12">会议时间：</a-col>
            <a-col :span="12"><a-input v-model:value="startTime"/></a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <android-outlined />
              参会人员
            </span>
          </template>
          <div>
            <a-row>
              <a-col>参会人员：</a-col>
              <a-col>{{meetMemberNumber}}人</a-col>
            </a-row>
            <a-row>
              <a-divider/>
            </a-row>
              <a-row>
                <a-col v-for="member in meetMember" :key="member">
                  <a-tag color="#108ee9">{{member.name}}</a-tag>
                </a-col>
              </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>

      <!-- 邀请成员modal -->
  <a-modal v-model:visible="showInviteModal" title="邀请成员" @ok="sendInvite"  ok-text="邀请"
    cancel-text="取消" style="width: 50%;">
          <!-- 当前会议，会议内 -->
          <div style="overflow: auto; height: 100%;">
        <dv-border-box-12 style="padding:1%;height: 100%;">
      <a-row :gutter="12">
            <a-col v-for="(item,index) in allUser" v-bind:key="index" :span="6" style="padding-top: 10px;height: 40%">
                <div :id="index" @mouseenter="mouseenterCardForInvite(index)" @mouseleave="mouseleaveCardForInvite(index)" @click="selectedInvite(index)">
                <dv-border-box-10 :id="index+'-meet'" :color="allUser[index].color" >
                    <a-card class="a-card"  :bordered="false" >
                        <template #title>
                            <a-row span="24">
                            <a-col span="24" align="center">{{item.name}}</a-col>
                            </a-row>
                        </template>
                    </a-card>
                </dv-border-box-10>
                </div>
            </a-col>
      </a-row>
    </dv-border-box-12>
    </div>
  </a-modal>
</template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { computed, defineComponent, ref, onMounted, watch, reactive, h } from 'vue'
import button2 from '@/views/components/dispatch/button2.vue'
import EmptyTable from '@/components/EmptyTable.vue'
// 图标
import {
  EyeOutlined,
  LoginOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  // 传入组件的参数【会议列表、、人员列表、历史会议列表,禁言,解除禁言,隔离，解除隔离，踢出】
  props: [
    'meetingLsit',
    'currMeetList',
    'personalList',
    'his_meeting_dataSource',
    'forbiddenSpeech',
    'disForbiddenSpeech',
    'quarantine',
    'disQuarantine',
    'kickOut',
    'addMember',
    'delMember',
    'memberStatus'
  ],
  // 组件
  components: {
    button2,
    EyeOutlined,
    PlusOutlined,
    DeleteOutlined,
    LoginOutlined,
    EmptyTable
  },
  setup (props, { emit }) {
    // 调度会议的列表列项
    const columns = [
      {
        title: '会议号码',
        dataIndex: 'number',
        slots: {
          customRender: 'number'
        }
      },
      {
        title: '会议室名称',
        dataIndex: 'name'
      },
      {
        title: '会议备注',
        dataIndex: 'description'
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '30%',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 历史会议的列表列项
    const columnsHis = [
      {
        title: '会议号码',
        dataIndex: 'number',
        slots: {
          customRender: 'number'
        }
      },
      {
        title: '会议室名称',
        dataIndex: 'name'
      },
      {
        title: '会议备注',
        dataIndex: 'description'
      },
      {
        title: '开始时间',
        dataIndex: 'start'
      },
      // {
      //   title: '结束时间',
      //   dataIndex: 'end'
      // },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '30%',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    // 调度会议的添加会议标志
    const showAddMeetingModal = ref(false)

    // 调度会议重新召开会议弹窗标志
    const showRestartMeetingModal = ref(false)
    // 调度会议当前会议的详情modal
    const showdetailsMeetingModal = ref(false)

    // 调度会议历史会议的详情modal
    const showhisdetailsMeetingModal = ref(false)

    // 调度会议进入会议内详情标志，当此标志为false且selectShowMeeting=2的时候才可以显示
    const showAllMeeting = ref(false)

    const srcEnterCard = require('@/assets/sound/select.wav')
    const srcSelectMeeting = require('@/assets/sound/enterMeeting.wav')

    const soundEnterCard = new Audio(srcEnterCard)
    const soundEnterMeeting = new Audio(srcSelectMeeting)

    const selectedMeetMembers = ref([])
    const allUser = ref([])

    const meetNumner = ref('') // 创建会议及编辑会议的会议号码
    const meetName = ref('') // 创建会议及编辑会议的会议名称
    const meetTheme = ref('') // 创建会议及编辑会议的会议主题
    const meetMember = ref([]) // 会议成员列表
    const meetTitle = ref('') // 会议名称

    // 表单状态对象，用于表单验证
    const formState = reactive({
      meetNumner: meetNumner,
      meetName: meetName,
      meetTheme: meetTheme
    })

    // 表单引用
    const createFormRef = ref()
    const restartFormRef = ref()
    const detailFormRef = ref()
    const historyFormRef = ref()

    const showInviteModal = ref(false) // 显示邀请
    let meetInviteSelect = []

    let meetMemberSelect = []

    const meetMemberNumber = ref('') // 会议人员数量
    const startTime = ref('') // 会议开始时间
    const endTime = ref('') // 会议结束时间

    let masterId = '' // 会议主持人ID，目前取坐席ID为主持人ID

    // 调度会议列表的默认数据
    const meeting_dataSource = ref([
      {
        key: '0',
        number: 998800,
        description: '硬件元件选型',
        name: '绝密会议室1',
        master: '长江',
        people_number: '3',
        meetingBordColor: "['red','green']"
      },
      {
        key: '1',
        number: 998801,
        description: '开发语言选型',
        name: '绝密会议室2',
        master: '黄河',
        people_number: '3'
      },
      {
        key: '2',
        number: 998803,
        description: '开发方案评审',
        name: '会议室3',
        master: '张三',
        people_number: '13'
      },
      {
        key: '3',
        number: 998804,
        description: '部门例会',
        name: '会议室2',
        master: '李四',
        people_number: '22'
      }
    ])

    // 历史会议列表的默认数据
    const his_meeting_dataSource_display = ref([
      {
        key: '0',
        number: 998800,
        description: '探寻外星人',
        name: '绝密会议室1',
        master: '长江',
        people_number: '23',
        start: '2024-12-2 14:30',
        end: '2024-12-2 14:50'
      },
      {
        key: '1',
        number: 998801,
        description: '探寻奥特曼',
        name: '绝密会议室2',
        master: '黄河',
        people_number: '23',
        start: '2024-12-2 14:30',
        end: '2024-12-2 14:50'
      },
      {
        key: '2',
        number: 998803,
        description: '天线宝宝找妈妈',
        name: '宝宝花园',
        master: '老狐狸',
        people_number: '23',
        start: '2024-12-2 14:30',
        end: '2024-12-2 14:50'
      },
      {
        key: '3',
        number: 998804,
        description: '秦始皇大战雷欧',
        name: '皇后的御花园',
        master: '灰狼',
        people_number: '23',
        start: '2024-12-2 14:30',
        end: '2024-12-2 14:50'
      }
    ])

    // 调度会议添加会议的人员设置穿梭框
    const mockData = ref([]) // 所有数据
    const targetKeys = ref([]) // 选择的数据

    // 选择显示对应会议功能页面
    const selectShowMeeting = ref(1)

    // 调度会议选择人员默认生成的数据
    const getMock = (personList) => {
      const mData = []
      personList.forEach((element, index) => {
        // 根据call_status决定显示的状态文本
        let statusText = ''
        let isOnline = true

        if (element.call_status === '已注册' || element.call_status === '空闲') {
          statusText = '在线'
          isOnline = true
        } else if (element.call_status === '未注册') {
          statusText = '离线'
          isOnline = false
        } else {
          statusText = element.call_status
          isOnline = !element.call_status.includes('离线')
        }

        const data = {
          key: index.toString(),
          title: element.name,
          status: statusText,
          isOnline: isOnline,
          originalData: element
        }
        mData.push(data)
        mockData.value = mData
      })
    }

    const filterOption = (inputValue, option) => {
      console.log(option)
      return option.title.indexOf(inputValue) > -1
    }

    // 自定义穿梭框项目渲染
    const renderTransferItem = (item) => {
      return h('div', {
        class: ['transfer-item-wrapper', !item.isOnline ? 'offline-wrapper' : ''],
        style: { position: 'relative' }
      }, [
        h('div', { class: 'user-info' }, [
          h('span', { class: 'user-name' }, item.title),
          h('span', {
            class: ['user-status', item.isOnline ? 'online' : 'offline']
          }, item.status)
        ]),
        !item.isOnline
          ? h('div', {
            class: 'offline-overlay'
          }, [
            h('span', { class: 'offline-text' }, '离线')
          ])
          : null
      ])
    }
    const handleChange = (keys, direction, moveKeys) => {
      selectedMeetMembers.value = []
      targetKeys.value = keys

      // 通过选择的KEY来将用户信息添加进来
      targetKeys.value.forEach(element => {
        allUser.value.forEach(user => {
          if (mockData.value[element].title === user.name) {
            console.log(user)
            // 将找到的用户放在选择的数组内
            selectedMeetMembers.value.push(user)
            console.log(selectedMeetMembers.value)
          }
        })
      })
    }
    const handleSearch = (dir, value) => {
      console.log('search:', dir, value)
    }

    const count = computed(() => meeting_dataSource.value.length + 1)
    const editableData = reactive({})
    /**
     * @description 结束会议
     * @param key 需要结束的会议号码
     */
    const onStopMeeting = key => {
      // 获取要结束会议的会议号码
      let stopMeetingNmuber = ''
      stopMeetingNmuber = meeting_dataSource.value[key].number

      // 将获取到的会议号通过emit传值到父组件
      emit('stopMeeting', stopMeetingNmuber)
    }

    /**
     * @description 会议列表表格后面详情按钮点击事件
     * @param i 表格Key
     */
    const details = key => {
      // 显示会议列表详情modal
      showdetailsMeetingModal.value = true
      meetName.value = ''
      meetNumner.value = ''
      meetTheme.value = ''
      meetMember.value = []
      // 从总会议表中取出第key条记录并进行显示
      let showMeetingByKey = ''
      showMeetingByKey = meeting_dataSource.value[key]

      meetName.value = showMeetingByKey.name // 会议名称
      meetNumner.value = showMeetingByKey.number // 会议号码
      meetTheme.value = showMeetingByKey.description // 会议主题
      meetMember.value = showMeetingByKey.member // 会议成员
    }

    /**
     * @description 历史会议列表表格后面详情按钮点击事件
     * @param i 表格Key
     */
    const his_details = key => {
      // 显示会议列表详情modal
      showhisdetailsMeetingModal.value = true
      meetName.value = ''
      meetNumner.value = ''
      meetTheme.value = ''
      meetMember.value = []
      // 从总会议表中取出第key条记录并进行显示
      let showMeetingByKey = ''
      showMeetingByKey = his_meeting_dataSource_display.value[key]

      meetName.value = showMeetingByKey.name // 会议名称
      meetNumner.value = showMeetingByKey.number // 会议号码
      meetTheme.value = showMeetingByKey.description // 会议主题
      meetMember.value = showMeetingByKey.member // 会议成员
      meetMemberNumber.value = showMeetingByKey.member.length // 参会人员数量
      startTime.value = showMeetingByKey.start // 会议开始时间
      endTime.value = showMeetingByKey.end_time // 会议结束时间
    }

    /**
     * @description 历史会议列表表格后面重新召开按钮点击事件
     * @description 首先需要查出来会议的详情，之后使用查找出来的会议信息进行创建会议
     * @name his_restartMeeting
     * @param key 表格Key
     */
    const his_restartMeeting = key => {
      // 显示会议列表详情modal
      showRestartMeetingModal.value = true
      meetName.value = ''
      meetNumner.value = ''
      meetTheme.value = ''
      meetMember.value = []
      // 从总会议表中取出第key条记录并进行显示
      let showMeetingByKey = ''
      showMeetingByKey = his_meeting_dataSource_display.value[key]

      meetName.value = showMeetingByKey.name // 会议名称
      meetNumner.value = showMeetingByKey.number // 会议号码
      meetTheme.value = showMeetingByKey.description // 会议主题
      meetMember.value = showMeetingByKey.member // 会议成员

      // 参会人员的key取出来，并且放到targetKeys中
      targetKeys.value = [] // 清空原有列表防止数据污染
      meetMember.value.forEach(element => {
        mockData.value.forEach(i => {
          if (i.title === element.name) {
            // 将获取到的key放入targetKeys
            targetKeys.value.push(i.key)
          }
        })
      })
      // 将查询到的用户放在选择的数组内
      selectedMeetMembers.value = (meetMember.value)
    }

    const handleAdd = () => {
      showAddMeetingModal.value = true
    }

    /**
     * @name 鼠标移动到卡片上的事件
     * @param i 鼠标放到卡片上的ID,通过这个ID就可以知道鼠标放到了那个卡片上
     */
    function mouseenterCard (i) {
      // 鼠标进入框中改变框的颜色
      meeting_dataSource.value[i].color = ['#00eaff', '#00eaaa']
      // 播放音效
      soundEnterCard.play()
    }

    /**
     * @name 当前会议鼠标移动到人员卡片上的事件
     * @param i 鼠标放到卡片上的ID,通过这个ID就可以知道鼠标放到了那个卡片上
     */
    function mouseenterCardForPersonnel (i) {
      // 鼠标进入框中改变框的颜色
      meetMember.value[i].color = ['#00eaff', '#00eaaa']
      // 播放音效
      soundEnterCard.play()
    }

    /**
     * @name 当前会议鼠标移动到人员卡片上的事件
     * @param i 鼠标放到卡片上的ID,通过这个ID就可以知道鼠标放到了那个卡片上
     */
    function mouseenterCardForInvite (i) {
      // 鼠标进入框中改变框的颜色
      allUser.value[i].color = ['#00eaff', '#00eaaa']
      // 播放音效
      soundEnterCard.play()
    }

    /**
     * @name 鼠标移出卡片的事件
     * @param i 鼠标移出卡片的ID，通过这个ID就可以知道鼠标从那个卡片上移出了。
     */
    function mouseleaveCard (i) {
      // 鼠标进入框中改变框的颜色
      meeting_dataSource.value[i].color = []
    }
    /**
     * @name 鼠标移出卡片的事件(人员)
     * @param i 鼠标移出卡片的ID，通过这个ID就可以知道鼠标从那个卡片上移出了。
     */
    function mouseleaveCardForPersonnel (i) {
      if (!meetMember.value[i].selected) {
        // 鼠标进入框中改变框的颜色
        meetMember.value[i].color = []
      }
    }
    /**
     * @name 鼠标移出卡片的事件(邀请)
     * @param i 鼠标移出卡片的ID，通过这个ID就可以知道鼠标从那个卡片上移出了。
     */
    function mouseleaveCardForInvite (i) {
      if (!allUser.value[i].selected) {
        // 鼠标进入框中改变框的颜色
        allUser.value[i].color = []
      }
    }

    /**
     * @name 鼠标点击卡片的事件
     * @param i 鼠标点击卡片的ID，通过这个ID就可以知道鼠标点击哪个卡片进而进入对应会议。
     */
    function enterMeeting (i) {
      // 鼠标进入框中改变框的颜色
      // 播放音效
      soundEnterMeeting.play()
      // 显示会议内的界面，隐藏全部卡片列表
      showAllMeeting.value = true
      // 隐藏会议列表
      selectShowMeeting.value = 2

      // 将这个会议的成员取出来
      meetMember.value = meeting_dataSource.value[i].member
      meetNumner.value = meeting_dataSource.value[i].number
      meetTitle.value = meeting_dataSource.value[i].name
      console.log('获取到的会议成员')
    }

    /**
     * @name 鼠标点击卡片的事件（人员）
     * @param i 鼠标点击卡片的ID，通过这个ID就可以知道鼠标点击哪个卡片进而进入对应会议。
     */
    function selectedMember (i) {
      // 鼠标进入框中改变框的颜色
      // 播放音效
      soundEnterMeeting.play()
      // alert(meetMember.value[i].name)
      meetMember.value[i].color = ['#00eaff', '#00eaaa']
      meetMember.value[i].selected = !meetMember.value[i].selected
      // 如果为选中状态则将其加入到选中成员的数组中
      if (meetMember.value[i].selected) {
        // 加入数据集
        meetMemberSelect.push(meetMember.value[i])
      } else {
        // 取消选中则需要将其从数组中剔除
        meetMemberSelect = meetMemberSelect.filter(item => item !== meetMember.value[i])
      }
      console.log(meetMemberSelect)
    }

    /**
     * @name 鼠标点击卡片的事件（邀请）
     * @param i 鼠标点击卡片的ID，通过这个ID就可以知道鼠标点击哪个卡片进而进入对应会议。
     */
    function selectedInvite (i) {
      // 鼠标进入框中改变框的颜色
      // 播放音效
      soundEnterMeeting.play()
      // alert(meetMember.value[i].name)
      allUser.value[i].color = ['#00eaff', '#00eaaa']
      allUser.value[i].selected = !allUser.value[i].selected
      // 如果为选中状态则将其加入到选中成员的数组中
      if (allUser.value[i].selected) {
        // 加入数据集
        meetInviteSelect.push(allUser.value[i])
      } else {
        // 取消选中则需要将其从数组中剔除
        meetInviteSelect = meetInviteSelect.filter(item => item !== allUser.value[i])
      }
    }

    /**
     * 处理表单提交
     * @param formRef 表单引用
     */
    const handleSubmit = (formRef) => {
      formRef.value.validate().then(() => {
        // 表单验证通过，执行创建会议逻辑
        // 创建变量临时获取session_config,目的：获取设定的坐席号码
        let session_config = null
        session_config = JSON.parse(sessionStorage.getItem('mesh_Config')) // 将json字符串转为json对象
        masterId = session_config.host_number // 获取坐席号码

        let createMeetInfo = ''
        createMeetInfo = {
          meetName: meetName.value,
          meetNumner: meetNumner.value,
          meetTheme: meetTheme.value,
          meetMembers: selectedMeetMembers.value,
          masterId: masterId
        }
        emit('creatMeeting', createMeetInfo)

        // 创建完成后关闭创建会议弹窗
        showAddMeetingModal.value = false
        showRestartMeetingModal.value = false
      }).catch(error => {
        console.error('表单验证失败:', error)
      })
    }

    /**
     * @name creatMeeting
    * @description 点击确定按钮后，将会议信息传给父组件
    */
    const creatMeeting = () => {
      // 根据当前打开的模态框确定使用哪个表单引用
      const formRef = showAddMeetingModal.value ? createFormRef.value : restartFormRef.value

      // 先验证表单
      formRef.validate().then(() => {
        // 表单验证通过，执行创建会议逻辑
        // 创建变量临时获取session_config,目的：获取设定的坐席号码
        let session_config = null
        session_config = JSON.parse(sessionStorage.getItem('mesh_Config')) // 将json字符串转为json对象
        masterId = session_config.host_number // 获取坐席号码

        let createMeetInfo = ''
        createMeetInfo = {
          meetName: meetName.value,
          meetNumner: meetNumner.value,
          meetTheme: meetTheme.value,
          meetMembers: selectedMeetMembers.value,
          masterId: masterId
        }
        emit('creatMeeting', createMeetInfo)

        // 创建完成后关闭创建会议弹窗
        showAddMeetingModal.value = false
        showRestartMeetingModal.value = false
      }).catch(error => {
        console.error('表单验证失败:', error)
      })
    }

    /**
     * @name handleCreateSubmit
     * @description 处理创建会议表单提交
     */
    const handleCreateSubmit = () => {
      if (!createFormRef.value) {
        console.error('创建表单引用未正确初始化')
        return
      }

      createFormRef.value.validate().then(() => {
        // 表单验证通过，执行创建会议逻辑
        // 创建变量临时获取session_config,目的：获取设定的坐席号码
        let session_config = null
        session_config = JSON.parse(sessionStorage.getItem('mesh_Config')) // 将json字符串转为json对象
        masterId = session_config.host_number // 获取坐席号码

        let createMeetInfo = ''
        createMeetInfo = {
          meetName: meetName.value,
          meetNumner: meetNumner.value,
          meetTheme: meetTheme.value,
          meetMembers: selectedMeetMembers.value,
          masterId: masterId
        }
        emit('creatMeeting', createMeetInfo)

        // 创建完成后关闭创建会议弹窗
        showAddMeetingModal.value = false
      }).catch(error => {
        console.error('表单验证失败:', error)
      })
    }

    /**
     * @name handleRestartSubmit
     * @description 处理重启会议表单提交
     */
    const handleRestartSubmit = () => {
      if (!restartFormRef.value) {
        console.error('重启表单引用未正确初始化')
        return
      }

      restartFormRef.value.validate().then(() => {
        // 表单验证通过，执行重启会议逻辑
        // 创建变量临时获取session_config,目的：获取设定的坐席号码
        let session_config = null
        session_config = JSON.parse(sessionStorage.getItem('mesh_Config')) // 将json字符串转为json对象
        masterId = session_config.host_number // 获取坐席号码

        let createMeetInfo = ''
        createMeetInfo = {
          meetName: meetName.value,
          meetNumner: meetNumner.value,
          meetTheme: meetTheme.value,
          meetMembers: selectedMeetMembers.value,
          masterId: masterId
        }
        emit('creatMeeting', createMeetInfo)

        // 创建完成后关闭重启会议弹窗
        showRestartMeetingModal.value = false
      }).catch(error => {
        console.error('表单验证失败:', error)
      })
    }

    /**
     * @description 底部按钮的点击事件
     * @param e 底部按钮传入的event
     */
    const ctrlBarClick = (e) => {
      let trandata = []
      trandata = [{
        action: '',
        meet_number: meetNumner.value,
        meeting_member: meetMemberSelect
      }]
      switch (e.currentTarget.id) {
        case 'j-y': // 禁言
          trandata[0].action = 'mute-member'
          emit('forbiddenSpeech', trandata)
          break
        case 'dis-j-y': // 解除禁言
          trandata[0].action = 'unmute-member'
          emit('disForbiddenSpeech', trandata)
          break
        case 'g-l': // 隔离
          trandata[0].action = 'isolate'
          emit('quarantine', trandata)
          break
        case 'dis-g-l': // 取消隔离
          trandata[0].action = 'no-isolate'
          emit('disQuarantine', trandata)
          break
        case 't-c': // 踢出
          trandata[0].action = 'kick-member'
          emit('kickOut', trandata)
          break
      }
      meetMemberSelect = []
      meetMember.value.forEach(element => {
        element.color = []
        element.selected = false
        console.log(element)
      })
    }

    /**
     * @description 显示邀请modal
     */
    const show_invite = () => {
      showInviteModal.value = true
    }

    const sendInvite = () => {
      let trandata = []
      trandata = [{
        action: 'add-member',
        title: meetTitle.value,
        meet_number: meetNumner.value,
        meeting_member: meetInviteSelect
      }]
      emit('kickOut', trandata)
      showInviteModal.value = false
    }

    onMounted(() => {
      // 监听 props.meetingLsit 的变化，当 props.meetingLsit 发生变化时，更新所有卡片状态。
      watch(() => props.meetingLsit, (newValue, oldValue) => {
        selectShowMeeting.value = newValue
        showAllMeeting.value = false
      })

      // 监听 props.currMeetList 的变化，当 props.currMeetList 发生变化时，更新会议数据。
      watch(() => props.currMeetList, (newValue, oldValue) => {
        console.log(newValue)
        meeting_dataSource.value = newValue
      },
      { immediate: true })

      // 监听 props.personalList 的变化，当 props.personalList 发生变化时，更新人员列表。
      watch(() => props.personalList, (newValue, oldValue) => {
        // console.log(newValue)
        // 将获取到的用户列表存在本地
        allUser.value = newValue
        console.log('获取到的全部用户')
        console.log(allUser.value)
        // 生成默认会议用户数据
        getMock(newValue)
        // meeting_dataSource.value = newValue
      },
      { immediate: true })

      // 监听 props.his_meeting_dataSource 的变化，当 props.his_meeting_dataSource 发生变化时，更新会议列表。
      watch(() => props.his_meeting_dataSource, (newValue, oldValue) => {
        // 将获取到的历史会议列表存在本地
        his_meeting_dataSource_display.value = newValue
        console.log(his_meeting_dataSource_display)
      },
      { immediate: true })

      // 监听 props.addMember 的变化，当发生变化时，更新对应人员列表，在对应人员列表加入（成员加入会议）。
      watch(() => props.addMember, (newValue, oldValue) => {
        console.log('=========成员加入会议=============')
        console.log(newValue)
        // meetMember.value.push(newValue)
        if (newValue.length !== 0) {
          let addMember = ''
          addMember = newValue
          addMember.can_speak = true
          addMember.isolatestatus = false
          addMember.callerIdNumber = newValue.number
          meetMember.value.push(addMember)
        }
      },
      { immediate: true })

      // 监听 props.delMember 的变化，当发生变化时，更新对应人员列表，在对应人员列表加入（成员离开会议）。
      watch(() => props.delMember, (newValue, oldValue) => {
        console.log('=========成员离开会议=============')
        console.log(newValue)

        if (newValue.length !== 0) {
          // 判断会议中是否存在该成员，如果存在则需要移除
          meetMember.value.forEach(element => {
            if (newValue.number === element.callerIdNumber) {
            // 将成员进行移除
            // 取消选中则需要将其从数组中剔除
              meetMember.value = meetMember.value.filter(item => item !== element)
            }
          })
        }
      },
      { immediate: true })

      // 监听 props.memberStatus 的变化，当发生变化时，更新会议人员列表。（人员状态）
      watch(() => props.memberStatus, (newValue, oldValue) => {
        console.log('=========成员状态发生变化=============')
        console.log(newValue)
        switch (newValue.action) {
          case 'mute-member':
            // 获取到用户，并操作用户其状态显示
            meetMember.value.forEach(element => {
              if (newValue.number === element.callerIdNumber) {
                element.can_speak = false
              }
            })
            break
          case 'unmute-member':
            // 获取到用户，并操作用户其状态显示
            meetMember.value.forEach(element => {
              if (newValue.number === element.callerIdNumber) {
                element.can_speak = true
              }
            })
            break
          case 'deaf-member':
            // 获取到用户，并操作用户其状态显示
            meetMember.value.forEach(element => {
              if (newValue.number === element.callerIdNumber) {
                element.can_speak = false
                element.isolatestatus = true
              }
            })
            break
          case 'undeaf-member':
            // 获取到用户，并操作用户其状态显示
            meetMember.value.forEach(element => {
              if (newValue.number === element.callerIdNumber) {
                element.can_speak = true
                element.isolatestatus = false
              }
            })
            break
        }
      },
      { immediate: true })
    })
    return {
    // 列表变量与方法
      columns,
      columnsHis,
      onStopMeeting,
      handleAdd,
      meeting_dataSource,
      his_meeting_dataSource_display,
      editableData,
      count,
      selectShowMeeting,
      mockData,
      targetKeys,
      filterOption,
      renderTransferItem,
      handleChange,
      handleSearch,
      showAddMeetingModal,
      showRestartMeetingModal,
      his_restartMeeting,

      // 表单验证
      formState,
      createFormRef,
      restartFormRef,
      detailFormRef,
      historyFormRef,
      handleSubmit,
      handleCreateSubmit,
      handleRestartSubmit,

      // 会议列表的卡片展示形式鼠标事件
      mouseenterCard, // 移入卡片
      mouseleaveCard, // 移出卡片
      enterMeeting, // 点击进入会议
      selectedMember,
      mouseleaveCardForPersonnel,
      mouseenterCardForPersonnel,

      // 图标
      EyeOutlined,
      PlusOutlined,
      EditOutlined,
      DeleteOutlined,
      LoginOutlined,
      allUser,

      // 会议编辑框
      meetName,
      meetNumner,
      meetTheme,

      details,
      his_details,
      meetMemberSelect,
      selectedInvite,

      // 禁言
      ctrlBarClick,

      // 显示邀请modal
      show_invite,
      showInviteModal,
      meetInviteSelect,
      sendInvite,

      // tab标签页标志
      activeKey: ref('1'),
      mouseenterCardForInvite,
      mouseleaveCardForInvite,

      // 创建会议的方法
      creatMeeting,
      // 列表会议详情
      showdetailsMeetingModal,
      showhisdetailsMeetingModal,
      meetMember,
      endTime,
      startTime,
      meetMemberNumber,
      button2,
      showAllMeeting
    }
  }

})
</script>
<style scoped>
    .a-card{
        width: 100%;
        height: 100%;
        text-align: start;
        padding: 0%;
        background-color: rgba(127, 255, 212, 0);
        title-color: #75ebff;
    }
    .a-card-text{
        text-align: 0%;
        margin-top: 2%;
    }
    .card-img{
        height: 20px;
        width: 20px;
    }
    .meet-list-table{
        height: 100%;
    }
    .ant-transfer-list-body-not-found {
    flex: none;
    width: 100%;
    margin: auto 0;
    color: rgb(25 214 255);
    text-align: center;
    }
    .at-transfer .ant-transfer-list:first-child .ant-transfer-list-empty-text {
      color:  rgb(25 214 255); /* 将#your-color-here替换为你想要的颜色代码 */
    }
    /* 去除数字输入框的上下箭头 */
    :deep(input::-webkit-outer-spin-button),
    :deep(input::-webkit-inner-spin-button) {
      -webkit-appearance: none !important;
      margin: 0;
    }
    :deep(input[type="number"]) {
      -moz-appearance: textfield;
    }
</style>
<style>
.ant-transfer-list-search .anticon-search {
    color: rgb(11 255 218);
}
.ant-transfer-list-body-not-found {
    flex: none;
    width: 100%;
    margin: auto 0;
    color: rgb(11 255 218);
    text-align: center;
}
.anticon.ant-input-clear-icon {
    margin: 0;
    color: rgb(11 255 218);
    font-size: 12px;
    vertical-align: -1px;
    cursor: pointer;
    transition: color 0.3s;
}
    .ctrl-bar{
        margin-left: 20px;
        margin: 20px;
    }

/* 穿梭框自定义样式 */
.transfer-item-wrapper {
  position: relative;
  padding: 8px 12px;
  width: 100%;
}

.offline-wrapper {
  opacity: 0.6;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.user-name {
  font-weight: 500;
  color: #ffffff !important;
}

.user-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: normal;
}

.user-status.online {
  background-color: #52c41a;
  color: white;
}

.user-status.offline {
  background-color: #ff4d4f;
  color: white;
}

.offline-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.offline-text {
  color: white;
  font-weight: bold;
  font-size: 14px;
}

/* 穿梭框全局样式 - 确保所有文字都是白色 */
:deep(.ant-transfer-list-content-item) {
  color: #ffffff !important;
}

:deep(.ant-transfer-list-content-item-text) {
  color: #ffffff !important;
}

/* 穿梭框项目文字颜色 */
:deep(.ant-transfer-list-content-item .ant-transfer-list-content-item-text) {
  color: #ffffff !important;
}

/* 穿梭框选中项目的文字颜色 */
:deep(.ant-transfer-list-content-item.ant-transfer-list-content-item-checked) {
  color: #ffffff !important;
}

:deep(.ant-transfer-list-content-item.ant-transfer-list-content-item-checked .ant-transfer-list-content-item-text) {
  color: #ffffff !important;
}

/* 穿梭框禁用项目的文字颜色 */
:deep(.ant-transfer-list-content-item.ant-transfer-list-content-item-disabled) {
  color: #ffffff !important;
  opacity: 0.6;
}

/* 自定义渲染项目的文字颜色 */
:deep(.transfer-item-wrapper .user-name) {
  color: #ffffff !important;
}
</style>
