<template>
  <a-card title="接入模式设置">
    <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
      <a-row justify="center" style="margin-top:2%">
        <a-form-item :label="LabelCaption.work_mode.label" :name="['configData', 'Mode']">
          <a-select v-model:value="model.configData.Mode" style="width:200px;margin-left:10%" v-bind:placeholder="LabelCaption.work_mode.label">
            <a-select-option v-for="option in WfModeOptions" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-row>
      <a-row>
        <a-divider />
      </a-row>
      <a-row justify="center">
        <a-col :span="2">
          <a-form-item>
            <a-button type="primary" html-type="submit" v-if="AuthUi.接入模式.statusW">保存</a-button>
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-form-item>
            <a-button type="primary" @click="getAccessMode">刷新</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

  </a-card>
</template>

<script>

import { onMounted, defineComponent, createVNode, reactive } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { AccessModeRequestAction } from '@/views/deviceinfo/action/accessModeAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { AccessMode } from '@/views/deviceinfo/model/AccessMode'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

import {
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({

  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const accessMode = new AccessMode()
    model.configData = accessMode

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const accessModeRequestAction = new AccessModeRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('接入模式设置成功!')
      } else {
        message.success(`接入模式设置失败!${data.error_code}`)
      }
      setAllData()
    }

    // 同步更新全局参数
    const setAllData = () => {
      model.wfmode = model.configData.Mode
      //   console.log('wfmode:' + model.wfmode)
      //   console.log('Mode:' + model.configData.Mode)
    }

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      accessModeRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前接入模式参数 *******************
    const getAccessMode = () => {
      accessModeRequestAction.query(baseRequestData, getAccessModeSuccess, callbackError, null)
    }

    const getAccessModeSuccess = (data) => {
      data.data.configData.Origin = 0
      model.configData = data.data.configData
      // this.model = data.data
      console.info('ok_1')
    }
    // ****************** 设备当前接入模式参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getAccessMode()
      // getDeviceinfoById()
    })

    const onFinish = (values) => {
      console.log('Success:', values)
    }

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      WfModeOptions,
      AuthUi,
      confirm,
      Pattern,
      onFinish,
      getAccessMode
    }
  }
})
</script>
