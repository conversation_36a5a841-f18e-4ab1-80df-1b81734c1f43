<template>
  <div>
    <a-row>
      <a-col :span="24">
        <a-card title="设备信息编辑">
          <a-row>
            <a-form
              :model="model"
              name="basic"

              layout="horizontal"
              @finish="onFinish"
              @finishFailed="onFinishFailed"
            >
              <a-row :gutter="24" wrap="false">

               <!--  <a-col :span="8"> -->
               <!--    <a-form-item -->
               <!--      :label="LabelCaption.wfmode.label" -->
               <!--      :name="LabelCaption.wfmode.name" -->
               <!--    > -->
               <!--      <a-select -->
               <!--        ref="select" -->
               <!--        v-model:value="model.wfmode" -->
               <!--        style="width: 100%" -->
               <!--      > -->
               <!--        <a-select-option -->
               <!--          v-for="option in WfModeOptions" -->
               <!--          v-bind:key="option.value" -->
               <!--          :value="value" -->
               <!--          >{{ option.name }}</a-select-option -->
               <!--        > -->
               <!--      </a-select> -->
               <!--    </a-form-item> -->
               <!--  </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.device_no.label" -->
                <!--     :name="LabelCaption.device_no.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.device_no" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.device_name.label"
                    :name="LabelCaption.device_name.name"
                    :rules="[{ required: true, message: '请输入设备名称!' }]"
                  >
                    <a-input v-model:value="model.device_name" />
                  </a-form-item>
                </a-col>

                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.device_type.label"
                    :name="LabelCaption.device_type.name"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.device_type"
                      style="width: 100%"
                    >
                      <a-select-option
                        v-for="option in DeviceTypeOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.net_id.label" -->
                <!--     :name="LabelCaption.net_id.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.net_id" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.paversion.label" -->
                <!--     :name="LabelCaption.paversion.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.paversion" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.apversion.label" -->
                <!--     :name="LabelCaption.apversion.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.apversion" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.cpversion_hls.label" -->
                <!--     :name="LabelCaption.cpversion_hls.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.cpversion_hls" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.mac_addr.label" -->
                <!--     :name="LabelCaption.mac_addr.name" -->
                <!--     :rules="[{ required: true, message: '请输入MAC!' }]" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.mac_addr" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.ip_addr.label"
                    :name="LabelCaption.ip_addr.name"
                    :rules="[{ required: true, message: '请输入IP!' },Pattern('IP')]"
                  >
                    <a-input v-model:value="model.ip_addr" />
                  </a-form-item>
                </a-col>

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.mask.label" -->
                <!--     :name="LabelCaption.mask.name" -->
                <!--     :rules="[{ required: true, message: '请输入Mesh设备子网掩码!' }]" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.mask" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.gate_way.label" -->
                <!--     :name="LabelCaption.gate_way.name" -->
                <!--     :rules="[{ required: true, message: '请输入网关地址!' }]" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.gate_way" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.dns.label" -->
                <!--     :name="LabelCaption.dns.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.dns" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.remaining_battery.label" -->
                <!--     :name="LabelCaption.remaining_battery.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.remaining_battery" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.voltage.label" -->
                <!--     :name="LabelCaption.voltage.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.voltage" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.interval.label" -->
                <!--     :name="LabelCaption.interval.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.interval" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.threshold.label" -->
                <!--     :name="LabelCaption.threshold.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.threshold" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.center_freq.label" -->
                <!--     :name="LabelCaption.center_freq.name" -->
                <!--   > -->
                <!--     <a-input v-model:value="model.center_freq" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.band_width.label" -->
                <!--     :name="LabelCaption.band_width.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.band_width" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.band_width.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in BandWidthOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.tx_power.label" -->
                <!--     :name="LabelCaption.tx_power.name" -->
                <!--   > -->
                <!--     <a-input type="number" v-model:value="model.tx_power" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.work_mode.label" -->
                <!--     :name="LabelCaption.work_mode.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.work_mode" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.work_mode.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in WorkModeOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.trunk.label" -->
                <!--     :name="LabelCaption.trunk.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.trunk" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.trunk.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in TrunkOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.register_period.label" -->
                <!--     :name="LabelCaption.register_period.name" -->
                <!--   > -->
                <!--     <a-input -->
                <!--       type="number" -->
                <!--       v-model:value="model.register_period" -->
                <!--     /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.routing_health_index.label" -->
                <!--     :name="LabelCaption.routing_health_index.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.routing_health_index" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder=" -->

                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in RoutingHealthIndexOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.in_network.label" -->
                <!--     :name="LabelCaption.in_network.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.in_network" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.in_network.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in InNetworkOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.distance.label" -->
                <!--     :name="LabelCaption.distance.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.distance" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.distance.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in DistanceOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.hdmi_in.label" -->
                <!--     :name="LabelCaption.hdmi_in.name" -->
                <!--   > -->
                <!--     <a-select -->
                <!--       ref="select" -->
                <!--       v-model:value="model.hdmi_in" -->
                <!--       style="width: 100%" -->
                <!--       v-bind:placeholder="LabelCaption.hdmi_in.label" -->
                <!--     > -->
                <!--       <a-select-option -->
                <!--         v-for="option in HDMIinOptions" -->
                <!--         v-bind:key="option.value" -->
                <!--         :value="value" -->
                <!--         >{{ option.name }}</a-select-option -->
                <!--       > -->
                <!--     </a-select> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.longitude.label" -->
                <!--     :name="LabelCaption.longitude.name" -->
                <!--   > -->
                <!--     <a-input type="number" v-model:value="model.longitude" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->

                <!-- <a-col :span="8"> -->
                <!--   <a-form-item -->
                <!--     :label="LabelCaption.latitude.label" -->
                <!--     :name="LabelCaption.latitude.name" -->
                <!--   > -->
                <!--     <a-input type="number" v-model:value="model.latitude" /> -->
                <!--   </a-form-item> -->
                <!-- </a-col> -->
              </a-row>

              <a-row justify="end">
                <a-col :span="4">
                  <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
                    <a-space>
                      <a-button type="primary" html-type="submit">保存</a-button>
                      <a-button
                        type="primary"
                        @click="pageDirectAction.goToIndex()"
                        >返回</a-button
                      >
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { onMounted, defineComponent, ref, createVNode } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'

import {
  BandWidthOptions,
  WorkModeOptions,
  TrunkOptions,
  RoutingHealthIndexOptions,
  InNetworkOptions,
  DistanceOptions,
  HDMIinOptions,
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  name: 'Deviceinfo-Add',
  setup () {
    // 对应后台数据表
    const model = ref(new Deviceinfo())
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // ****************** 新增 *******************
    const onFinish = (values) => {
      confirmAdd()
    }

    const onFinishFailed = (errorInfo) => {
      message.info('请填写带星的项目!')
    }

    const confirmAdd = (record) => {
      Modal.confirm({
        title: '编辑设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认保存本条设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            modDeviceinfo(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const modDeviceinfoSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('编辑本条设备信息成功!')
        pageDirectAction.goToIndex()
      } else {
        message.success(`编辑本条设备信息失败!${data.error_code}`)
      }
    }

    const modDeviceinfo = () => {
      requestAction.modOne(
        baseRequestData.value,
        modDeviceinfoSuccess,
        callbackError,
        null
      )
    }
    // ****************** 新增 *******************

    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model.value = data.data
      }
    }

    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }

    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData.value, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }
    // ****************** 根据主键查询数据 *******************

    onMounted(() => {
      model.value.id = pageDirectAction.getCurrentRouteValue()
      getDeviceinfoById()
    })

    return {
      model,
      baseParam,
      LabelCaption,
      BandWidthOptions,
      WorkModeOptions,
      TrunkOptions,
      RoutingHealthIndexOptions,
      InNetworkOptions,
      DistanceOptions,
      HDMIinOptions,
      DeviceTypeOptions,
      WfModeOptions,
      DeviceinfoColumns,
      pageDirectAction,
      onFinish,
      onFinishFailed,
      Pattern
    }
  }
})
</script>

<style scoped></style>
