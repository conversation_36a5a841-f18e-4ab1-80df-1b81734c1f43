import { ref } from 'vue'

/**
 * 视频布局扩展功能
 * 提供海康威视风格的多种视频矩阵布局和拖拽功能
 */
export const useVideoLayoutExtension = (videoClickHandler) => {
  // 当前激活（选中）的视频索引
  const activeVideoIndex = ref(null)

  // 当前布局模式
  const currentLayout = ref('default')

  // 视频拖拽相关变量
  const dragVideoIndex = ref(null)

  // 改变布局模式
  const changeLayout = (layout) => {
    currentLayout.value = layout

    // 获取所有视频包装器元素的父容器
    const container = document.querySelector('.ant-row > .ant-col')
    if (!container) return

    // 移除所有现有布局类
    container.classList.remove(
      'layout-main-left',
      'layout-main-right',
      'layout-main-top',
      'layout-main-bottom',
      'layout-main-center',
      'layout-2-1',
      'layout-1-2',
      'layout-t-shape'
    )

    // 根据选择的布局添加相应的类
    if (layout !== 'default') {
      container.classList.add(`layout-${layout}`)
    }

    // 如果从其他布局切换到默认布局，需要重排视频元素
    if (layout === 'default') {
      resetVideoOrder()
    }
  }

  // 重置视频排序
  const resetVideoOrder = () => {
    const container = document.querySelector('.ant-row > .ant-col')
    if (!container) return

    const wrappers = Array.from(container.querySelectorAll('.video-wrapper'))

    // 按照原始索引排序
    wrappers.sort((a, b) => {
      return parseInt(a.getAttribute('data-index')) - parseInt(b.getAttribute('data-index'))
    })

    // 重新添加到容器
    wrappers.forEach(wrapper => {
      container.appendChild(wrapper)
    })
  }

  // 拖拽开始
  const dragStart = (e, index) => {
    dragVideoIndex.value = index
    e.target.classList.add('dragging')

    // 设置拖拽效果和数据
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', index)
  }

  // 拖拽经过
  const dragOver = (e) => {
    // 阻止默认行为以允许放置
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  // 放置
  const drop = (e, targetIndex) => {
    e.preventDefault()

    // 如果不是拖到自己身上
    if (dragVideoIndex.value !== targetIndex) {
      // 交换两个视频元素的位置
      const container = document.querySelector('.ant-row > .ant-col')
      if (!container) return

      const sourceWrapper = document.getElementById(`wrapper-${dragVideoIndex.value + 1}`)
      const targetWrapper = document.getElementById(`wrapper-${targetIndex + 1}`)

      if (!sourceWrapper || !targetWrapper) return

      // 记住它们的下一个兄弟元素
      const sourceNext = sourceWrapper.nextElementSibling
      const targetNext = targetWrapper.nextElementSibling

      // 特殊情况处理：如果目标是源的下一个元素
      if (sourceWrapper.nextElementSibling === targetWrapper) {
        container.insertBefore(targetWrapper, sourceWrapper)
      } else if (targetWrapper.nextElementSibling === sourceWrapper) {
        container.insertBefore(sourceWrapper, targetWrapper)
      } else {
        // 一般情况：交换位置
        if (sourceNext) {
          container.insertBefore(sourceWrapper, targetNext)
        } else {
          container.appendChild(sourceWrapper)
        }

        if (targetNext) {
          container.insertBefore(targetWrapper, sourceNext)
        } else {
          container.appendChild(targetWrapper)
        }
      }

      // 如果在特殊布局中，设置当前激活的视频
      if (currentLayout.value !== 'default') {
        activeVideoIndex.value = targetIndex
      }
    }
  }

  // 拖拽结束
  const dragEnd = (e) => {
    e.target.classList.remove('dragging')
    dragVideoIndex.value = null
  }

  // 处理视频点击，增强原来的点击处理程序
  const handleVideoClick = (n) => {
    // 调用原来的处理程序
    if (typeof videoClickHandler === 'function') {
      videoClickHandler(n)
    }

    // 设置当前激活的视频索引
    activeVideoIndex.value = n - 1

    // 如果是在特殊布局模式下点击视频，可以将其设为主画面
    if (currentLayout.value !== 'default') {
      const container = document.querySelector('.ant-row > .ant-col')
      if (!container) return

      const clickedWrapper = document.getElementById(`wrapper-${n}`)
      const firstWrapper = container.querySelector('.video-wrapper')

      if (!clickedWrapper || !firstWrapper) return

      // 如果点击的不是第一个元素，则将其移到首位
      if (clickedWrapper !== firstWrapper) {
        container.insertBefore(clickedWrapper, firstWrapper)
      }
    }
  }

  return {
    activeVideoIndex,
    currentLayout,
    dragVideoIndex,
    changeLayout,
    resetVideoOrder,
    dragStart,
    dragOver,
    drop,
    dragEnd,
    handleVideoClick
  }
}
