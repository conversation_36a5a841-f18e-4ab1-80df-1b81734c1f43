module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: '@babel/eslint-parser',
    sourceType: 'module'
  },
  globals: {
    Loca: false,
    AMap: false,
    AMapUI: false
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 关闭tab检测
    'no-tabs': 'off',
    // 关闭vue组件驼峰命名检查
    'vue/multi-word-component-names': 0,
    // 关闭驼峰命名检测S
    camelcase: 'off',
    'vue/no-v-model-argument': 0
  }
}
