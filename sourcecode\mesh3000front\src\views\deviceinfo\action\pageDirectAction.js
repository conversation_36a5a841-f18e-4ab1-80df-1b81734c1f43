import Router from '../../../router/index'
class PageDirectAction {
  constructor () {
    this.pageIndexUrl = `${this.baseUrl}_index`
    this.pageDetailUrl = `${this.baseUrl}_detail`
    this.pageAddUrl = `${this.baseUrl}_add`
    this.pageModUrl = `${this.baseUrl}_mod`
    this.pageSetUrl = `${this.baseUrl}_set`
    this.pageDhcpUrl = 'dhcp_set'
    this.pageWifiUrl = 'wifi_set'
  }

  baseUrl = 'deviceinfo'
  pageIndexUrl = ''
  pageDetailUrl = ''
  pageAddUrl = ''
  pageModUrl = ''
  pageSetUrl = ''

  getCurrentRouteValue () {
    return Router.currentRoute.value.query.id
  }

  goToIndex () {
    Router.push({
      path: this.pageIndexUrl
    })
  }

  goToDetail (e) {
    Router.push({
      path: this.pageDetailUrl,
      query: {
        id: e.id
      }
    })
  }

  goToAdd () {
    Router.push({
      path: this.pageAddUrl
    })
  }

  goToMod (e) {
    Router.push({
      path: this.pageModUrl,
      query: {
        id: e.id
      }
    })
  }

  goToSet (e) {
    Router.push({
      path: this.pageSetUrl,
      query: {
        id: e.id
      }
    })
  }

  goToDhcp (e) {
    Router.push({
      path: this.pageDhcpUrl
    })
  }

  goToWifi (e) {
    Router.push({
      path: this.pageWifiUrl
    })
  }

  goTo (path, id) {
    Router.push({
      path: path,
      query: {
        id: id
      }
    })
  }
}

export { PageDirectAction }
