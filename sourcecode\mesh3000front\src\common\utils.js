/* eslint-disable no-useless-constructor */
class Utils {
  constructor () { }

  /**
     * JM卡
     * 10进制卡号转为16进制卡号
     * @param {10进制JM卡号} dec_jmcard
     * @returns
     */
  card_dec_2_hex (dec_jmcard) {
    const tmp_dec_jmcard = Number(dec_jmcard)
    const h1 = (tmp_dec_jmcard & 0xFF).toString(16)
    const h0 = ((tmp_dec_jmcard >> 8) & 0xFF).toString(16)
    const l1 = ((tmp_dec_jmcard >> 16) & 0xFF).toString(16)
    const l0 = ((tmp_dec_jmcard >> 24) & 0xFF).toString(16)

    return (`${(h1.length === 1) ? ('0' + h1) : h1}` +
            `${(h0.length === 1) ? ('0' + h0) : h0}` +
            `${(l1.length === 1) ? ('0' + l1) : l1}` +
            `${(l0.length === 1) ? ('0' + l0) : l0}`).toUpperCase()
  }

  /**
     * JM卡
     * 16进制卡号转为10进制卡号
     * @param {16进制JM卡号} hex_jmcard
     * @returns
     */
  card_hex_2_dec (hex_jmcard) {
    let tmpHexStr = ''

    let i = hex_jmcard.length
    while (i >= 0) {
      tmpHexStr += hex_jmcard.substr(i, 2)
      i = i - 2
    }
    return parseInt(tmpHexStr, 16)
  }

  /**
     * 是否有子节点
     * @param {title: 'wifi权限'}
     * @returns  node
     */

  ifHasChildren (title, tree) {
    let node = null
    const obj0 = tree
    Object.keys(obj0).forEach((key0) => {
      console.info(obj0[key0])
      const obj0_0 = obj0[key0].children
      Object.keys(obj0_0).forEach((key0_0) => {
        if (obj0_0[key0_0].title === title) {
          node = obj0_0[key0_0].children
        }
      })
    })
    return node
  }

  /**
     * 根据权限title返回相应的key
     * @param {title: 'wifi权限'}
     * @returns  key: '0-0-0'
     */

  getKey (title, tree) {
    const key = ''
    const obj0 = tree
    Object.keys(obj0).forEach((key0) => {
      console.info(obj0[key0])
      const obj1 = obj0[key0].children
      Object.keys(obj1).forEach((key1) => {
        if (obj1[key1].title === title) {
          return obj1[key1].key
        }
      })
    })
    return key
  }

  /**
     * 根据子权限title返回相应的子key
     * @param {title: '读权限'}
     * @returns  key: '0-0-0-0'
     */

  getChildKey (title, child) {
    let key = ''
    const obj1 = child
    Object.keys(obj1).forEach((key1) => {
      if (obj1[key1].title === title) {
        key = obj1[key1].key
      }
    })
    return key
  }

  /**
     * 根据子权限key返回相应的子权限title
     * @param {key: '0-0-0-0'}
     * @returns  title: '读权限'
     */

  getChildTitle (key, child) {
    let title = ''
    const obj1 = child
    Object.keys(obj1).forEach((key1) => {
      if (obj1[key1].key === key) {
        title = obj1[key1].title
      }
    })
    return title
  }

  /**
     * 根据子权限key返回相应的父权限title
     * @param {key: '0-0-0-0'}
     * @returns  title: 'wifi权限'
     */

  getTitle (key, tree) {
    let title = ''
    const obj1 = tree
    Object.keys(obj1).forEach((key1) => {
      if (obj1[key1].key === key) {
        title = obj1[key1].title
      }
    })
    return title
  }

  /**
     * 判断是否有读权限
     * @param node
     * @returns  true/false
     */

  ifHasReadAuth (node) {
    Object.keys(node).forEach((key) => {
      console.info(node[key])
      if (node[key].title === '读权限') {
        return true
      }
    })
    return false
  }

  /**
     * 判断是否有写权限
     * @param node
     * @returns  true/false
     */

  ifHasWriteAuth (node) {
    Object.keys(node).forEach((key) => {
      console.info(node[key])
      if (node[key].title === '写权限') {
        return true
      }
    })
    return false
  }

  /**
     * 根据key遍历tree目录，找到指定的节点
     * @param {key:'0-1-1'}
     * @returns  node
     */

  getTreeNode (key, tree) {
    const node = {}
    const obj0 = tree
    Object.keys(obj0).forEach((key) => {
      console.info(obj0[key])
      if (obj0[key].id != null) {
        // check_option.push((obj[key].authorityname))
        // plainOptions.value.push((obj[key].authorityname))
        // role_opts.value.push({
        //   value: obj[key].rolename,
        //   label: obj[key].rolename
        // })
      }
    })
    return node
  }

  /**
     * 根据key遍历tree目录，找到指定节点的父节点
     * @param {key:'0-1-1'}
     * @returns  p_node
     */

  /**
     * 根据key遍历tree目录，找到指定节点的子节点
     * @param {key:'0-1-1'}
     * @returns  c_node
     */

  formatDate (date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
}

export { Utils }
