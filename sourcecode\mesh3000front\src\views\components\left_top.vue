<template>
    <div id="left_top" style="width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0); position: relative;"></div>
  </template>

<script>
import { defineComponent, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['warningTotal'],
  setup (props) {
    const pieData = [
      {
        name: '告警总数',
        value: 80
      },
      {
        name: '一般告警',
        value: 60
      },
      {
        name: '严重告警',
        value: 20
      }
    ]

    const titleArr = []
    const seriesArr = []

    pieData.forEach(function (item, index) {
      titleArr.push({
        text: item.name,
        left: index * 35 + 14.5 + '%',
        top: '83%',
        textAlign: 'center',
        textStyle: {
          fontWeight: 'normal',
          fontSize: '15',
          color: 'white',
          textAlign: 'center'
        }
      })
      seriesArr.push(
        {
          type: 'pie',
          name: '外层细圆环',
          radius: ['66%', '47%'],
          center: [index * 35 + 14.5 + '%', '45%'],
          hoverAnimation: false,
          clockWise: false,
          itemStyle: {
            normal: {
              color: '#6e7175'
            }
          },
          label: {
            show: false
          },
          data: [100]
        },
        {
          type: 'pie',
          name: '内层层细圆环',
          radius: ['34%', '25%'],
          center: [index * 35 + 14.5 + '%', '45%'],
          hoverAnimation: false,
          clockWise: false,
          itemStyle: {
            normal: {
              color: '#6e7175'
            }
          },
          label: {
            show: false
          },
          data: [100]
        }
      )
    })

    seriesArr.push(
      {
        name: pieData[0].name,
        type: 'pie',
        clockWise: false,
        radius: ['38%', '53%'],
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#EE794B'
              },
              {
                offset: 1,
                color: '#EE794B'
              }
            ]),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        },
        hoverAnimation: false,
        center: [0 * 35 + 14.5 + '%', '45%'],
        data: [
          {
            value: pieData[0].value,
            label: {
              normal: {
                formatter: function (params) {
                  return params.value + '个'
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '15',
                  // fontWeight: 'bold',
                  color: '#EE794B'
                }
              }
            }
          },
          {
            value: pieData[0].value - pieData[0].value,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(255,255,255,0.2)'
              },
              emphasis: {
                color: 'rgba(0,0,0,0)'
              }
            }
          }
        ]
      },
      {
        name: pieData[1].name,
        type: 'pie',
        clockWise: false,
        radius: ['38%', '53%'],
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#65D3F3'
              },
              {
                offset: 1,
                color: '#65D3F3'
              }
            ]),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        },
        hoverAnimation: false,
        center: [1 * 35 + 14.5 + '%', '45%'],
        data: [
          {
            value: pieData[1].value,
            label: {
              normal: {
                formatter: function (params) {
                  return params.value + '个'
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '18',
                  // fontWeight: 'bold',
                  color: '#1cc7ff'
                }
              }
            }
          },
          {
            value: pieData[0].value - pieData[1].value,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(0,0,0,0)'
              },
              emphasis: {
                color: 'rgba(0,0,0,0)'
              }
            }
          }
        ]
      },
      {
        name: pieData[2].name,
        type: 'pie',
        clockWise: false,
        radius: ['38%', '53%'],
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'red'
              },
              {
                offset: 1,
                color: 'red'
              }
            ]),
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        },
        hoverAnimation: false,
        center: [2 * 35 + 14.5 + '%', '45%'],
        data: [
          {
            value: pieData[2].value,
            label: {
              normal: {
                formatter: function (params) {
                  return params.value + '个'
                },
                position: 'center',
                show: true,
                textStyle: {
                  fontSize: '15',
                  // fontWeight: 'bold',
                  color: 'red'
                }
              }
            }
          },
          {
            value: pieData[0].value - pieData[2].value,
            name: 'invisible',
            itemStyle: {
              normal: {
                color: 'rgba(0,0,0,0)'
              },
              emphasis: {
                color: 'rgba(0,0,0,0)'
              }
            }
          }
        ]
      }
    )
    const option = {
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true
      },
      backgroundColor: '',
      title: titleArr,
      series: seriesArr
    }

    // 更新数据
    const getCharData = () => {
      pieData.forEach(function (item, index) {
        titleArr.push({
          text: item.name,
          left: index * 35 + 14.5 + '%',
          top: '83%',
          textAlign: 'center',
          textStyle: {
            fontWeight: 'normal',
            fontSize: '15',
            color: 'white',
            textAlign: 'center'
          }
        })
        seriesArr.push(
          {
            type: 'pie',
            name: '外层细圆环',
            radius: ['66%', '47%'],
            center: [index * 35 + 14.5 + '%', '45%'],
            hoverAnimation: false,
            clockWise: false,
            itemStyle: {
              normal: {
                color: '#6e7175'
              }
            },
            label: {
              show: false
            },
            data: [100]
          },
          {
            type: 'pie',
            name: '内层层细圆环',
            radius: ['34%', '25%'],
            center: [index * 35 + 14.5 + '%', '45%'],
            hoverAnimation: false,
            clockWise: false,
            itemStyle: {
              normal: {
                color: '#6e7175'
              }
            },
            label: {
              show: false
            },
            data: [100]
          }
        )
      })

      seriesArr.push(
        {
          name: pieData[0].name,
          type: 'pie',
          clockWise: false,
          radius: ['38%', '53%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#EE794B'
                },
                {
                  offset: 1,
                  color: '#EE794B'
                }
              ]),
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          center: [0 * 35 + 14.5 + '%', '45%'],
          data: [
            {
              value: pieData[0].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '个'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '15',
                    // fontWeight: 'bold',
                    color: '#EE794B'
                  }
                }
              }
            },
            {
              value: pieData[0].value - pieData[0].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: 'rgba(255,255,255,0.2)'
                },
                emphasis: {
                  color: 'rgba(0,0,0,0)'
                }
              }
            }
          ]
        },
        {
          name: pieData[1].name,
          type: 'pie',
          clockWise: false,
          radius: ['38%', '53%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#65D3F3'
                },
                {
                  offset: 1,
                  color: '#65D3F3'
                }
              ]),
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          center: [1 * 35 + 14.5 + '%', '45%'],
          data: [
            {
              value: pieData[1].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '个'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '18',
                    // fontWeight: 'bold',
                    color: '#1cc7ff'
                  }
                }
              }
            },
            {
              value: pieData[0].value - pieData[1].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: 'rgba(0,0,0,0)'
                },
                emphasis: {
                  color: 'rgba(0,0,0,0)'
                }
              }
            }
          ]
        },
        {
          name: pieData[2].name,
          type: 'pie',
          clockWise: false,
          radius: ['38%', '53%'],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'red'
                },
                {
                  offset: 1,
                  color: 'red'
                }
              ]),
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          center: [2 * 35 + 14.5 + '%', '45%'],
          data: [
            {
              value: pieData[2].value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '个'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '15',
                    // fontWeight: 'bold',
                    color: 'red'
                  }
                }
              }
            },
            {
              value: pieData[0].value - pieData[2].value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: 'rgba(0,0,0,0)'
                },
                emphasis: {
                  color: 'rgba(0,0,0,0)'
                }
              }
            }
          ]
        }
      )

      option.value = {
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: '0%',
          containLabel: true
        },
        backgroundColor: '',
        title: titleArr,
        series: seriesArr
      }
    }

    onMounted(() => {
      const chartDom2 = document.getElementById('left_top')
      const myChart2 = echarts.init(chartDom2, 'dark')

      // 初始化图表
      myChart2.setOption(option)

      // 监听 props.a 的变化，当 props.a 发生变化时，更新柱状图数据
      watch(() => props.warningTotal, (newValue, oldValue) => {
        console.log(newValue)
        pieData[0].value = newValue.type1
        pieData[1].value = newValue.type2
        pieData[2].value = newValue.type3
        // 更新数据
        seriesArr.length = 0
        getCharData()

        // 更新图表配置
        myChart2.setOption(option)
      })
    })
  }
})
</script>
