<template>
    <v-card class="ma-2">
      <v-container>
        <v-layout>
          {{ message }}
          <div id="meet" style="position:absolute; width:86%; height:89%"></div>
          <!-- missing -->
        </v-layout>
      </v-container>
    </v-card>

    <a-button v-if = "if_button_notice" type="primary"  size="large" @click="ShowCard()" style="margin-left: 1%; margin-top: 1%;z-index: 999999 !important;">
        <MenuUnfoldOutlined/> 通知开会
    </a-button>
    <a-spin  id="spinMeet" class="box" tip="正在进入会议..." :spinning="ifSpinning"></a-spin>

    <a-card v-if="ifshowCard" hoverable style="width: 600px;background-color: #162130ce;font-size: large;" >
      <a-form
              ref="formRef"
              :model="nociceMeet"
              name="basic"
              autocomplete="off"
              @finish="onFinish"
              @finishFailed="onFinishFailed"
              >
        <a-form-item
          label="会议名称"
          name="meet_title"
          :rules="[{ required: true, message: '会议名称不能为空' }]" >
               <a-input  v-model:value="nociceMeet.meet_title" />
        </a-form-item>
        <a-form-item
          label="会议主题"
          name="meet_topic"
          :rules="[{ required: true, message: '会议主题不能为空' }]" >
               <a-input  v-model:value="nociceMeet.meet_topic" />
        </a-form-item>
        <a-form-item
          label="会议号码"
          name="meet_number"
          :rules="[{ required: true, message: '会议号码不能为空' },Pattern('Number')]" >
               <a-input  v-model:value="nociceMeet.meet_number" />
        </a-form-item>

      <!-- <a-switch class="check_" v-model:checked="checked_date" checked-children="立刻" un-checked-children="预约" @change="switchCheck_date()" /> -->
        <a-checkbox
          v-model:checked="checked_date"
          @change="onCheckChange"
        >是否预约会议
        </a-checkbox>
        <a-date-picker show-time id="data-picker" v-if = "if_show_timepick" placeholder="选择时间" @change="onChangeTime" @ok="onChangeTimeOk" />
          <a-checkbox
            v-model:checked="meetUser_state.checkAll"
            :indeterminate="meetUser_state.indeterminate"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        <a-divider />
        <a-checkbox-group v-model:value="meetUser_state.checkedList" :options="userOptions" />
        <a-divider />
        <a-button type="primary" @click="onSubmit()">确定</a-button>
        <a-button type="primary" @click="cancel_notice()">取消</a-button>
      </a-form>
    </a-card>

    <a-tabs  v-if = "ifShowMeetList" @change="changeTable(activeKey)" v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <EnvironmentOutlined />
              当前会议
            </span>
          </template>
          <a-table
            style="margin-top:2%;background-color: #ffffff00;"
            :pagination="pagination"
            class="ant-table-striped"
            size="middle"
            :columns="curr_columns"
            :data-source="currList"
            bordered>
            <template #operation="{ record}">
                  <div class="editable-row-operations">
                    <span>
                      <a-button  :id= "`btn-curr-enter${record.key}`" :disabled="enter_button_delay" @click="enterMeeting(record.meet_number)" type="primary">进入</a-button>
                    </span>
                    <!-- <span>
                      <a-button  :id= "`btn-curr-enter${record.key}`" @click="getRoomInfo(record.meet_number)" type="primary" >详细</a-button>
                    </span> -->
                  </div>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <NodeIndexOutlined />
              预约会议
            </span>
          </template>
          <a-table
              style="margin-top:2%;background-color: #ffffff00;"
              :pagination="pagination"
              class="ant-table-striped"
              size="middle"
              :columns="book_columns"
              :data-source="bookList"
              bordered>
              <template #operation="{ record}">
                  <div class="editable-row-operations">
                    <span>
                      <a-button  :id= "`btn-book-wait${record.key}`" @click="cancelMeeting(record.key)" type="primary" >取消</a-button>
                    </span>
                  </div>
              </template>
            </a-table>
        </a-tab-pane>
        <a-tab-pane key="3">
          <template #tab>
            <span>
              <NodeIndexOutlined />
              历史会议
            </span>
          </template>
          <a-table
              style="margin-top:2%;background-color: #ffffff00;"
              :pagination="pagination"
              class="ant-table-striped"
              size="middle"
              :columns="his_columns"
              :data-source="hisList"
              bordered>
              <template #operation="{ record}">
                  <div class="editable-row-operations">
                    <span>
                      <a-button  :id= "`btn-book-wait${record.key}`" @click="reOpenMeeting(record.key)" type="primary" >重新召开</a-button>
                    </span>
                  </div>
              </template>
            </a-table>
        </a-tab-pane>
      </a-tabs>

</template>

<script>

import { onMounted, defineComponent, ref, watch, reactive, toRefs, onUnmounted } from 'vue'
import { notification } from 'ant-design-vue'
import { MenuUnfoldOutlined } from '@ant-design/icons-vue'
import { MeetingAction } from '@/views/deviceinfo/action/meetingAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { BaseRequestData } from '@/common/baseRequestData'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { UserSet } from '@/views/deviceinfo/model/UserSet'
import { BaseParam } from '@/common/baseParam'
import bus from '@/libs/bus'
import Router from '@/router/index'
import Pattern from '@/common/pattern'

import {
  DebugModeOption
} from '@/views/deviceinfo/constant/options'

// import { Client } from '@stomp/stompjs'

export default defineComponent({
  components: {
    // EnvironmentOutlined,
    // NodeIndexOutlined,
    // SettingOutlined,
    MenuUnfoldOutlined
    // PlusOutlined
    /* MenuUnfoldOutlined, */
    /* MenuFoldOutlined */
  },
  setup () {
    let api = null
    let meetDomain
    let if_inRoom = false
    const model = reactive(new Deviceinfo())
    const user = new UserSet()
    model.configData = user
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))

    const pageDirectAction = new PageDirectAction()

    let room = 'dtt'
    const currRoom = ref()
    const if_button_notice = ref(false)
    const if_show_timepick = ref(false)
    const ifShowMeetList = ref(true)
    const enter_button_delay = ref(false)
    const state = reactive({
      checked_date: false,
      checked2: false,
      checked3: false
    })
    let username = ''

    let current_user = null
    // 显示卡片
    const ifshowCard = ref(false)
    const ifSpinning = ref(false)
    const spinWaitTime = 3000
    let preMeetStr = ''
    let enterMeetCount = 0

    const nociceMeet = ref({})
    // nociceMeet.value.meet_title = '未定义'

    const holdMeet = ref({})
    holdMeet.value = { user_list: [] }
    const formRef = ref()

    const ifDisable = ref({
      enter_button: false // 是否使会议列表中的进入按钮失效，防止重复进入
    })

    // 用户columns
    const columns = [
      {
        title: '用户名称',
        dataIndex: 'user_name',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'user_name'
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'status'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 当前会议curr_columns
    const curr_columns = [
      {
        title: '会议号码',
        dataIndex: 'meet_number',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_number'
        }
      },
      {
        title: '会议主持',
        dataIndex: 'meet_master_id',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_master_id'
        }
      },
      {
        title: '会议名称',
        dataIndex: 'meet_title',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_title'
        }
      },
      {
        title: '会议主题',
        dataIndex: 'meet_topic',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_topic'
        }
      },
      {
        title: '会议时间',
        dataIndex: 'meet_time',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_time'
        }
      },
      {
        title: '参会用户',
        dataIndex: 'user_list',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'user_list'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 预约会议book_columns
    const book_columns = [
      {
        title: '会议号码',
        dataIndex: 'meet_number',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_number'
        }
      },
      {
        title: '会议主持',
        dataIndex: 'meet_master_id',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_master_id'
        }
      },
      {
        title: '会议名称',
        dataIndex: 'meet_title',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_title'
        }
      },
      {
        title: '会议主题',
        dataIndex: 'meet_topic',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'meet_topic'
        }
      },
      {
        title: '会议时间',
        dataIndex: 'meet_time',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_time'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 历史会议book_columns
    const his_columns = [
      {
        title: '会议号码',
        dataIndex: 'meet_number',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'meet_number'
        }
      },
      {
        title: '会议主持',
        dataIndex: 'meet_master_id',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'meet_master_id'
        }
      },
      {
        title: '会议名称',
        dataIndex: 'meet_title',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_title'
        }
      },
      {
        title: '会议主题',
        dataIndex: 'meet_topic',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_topic'
        }
      },
      {
        title: '会议时间',
        dataIndex: 'meet_time',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'meet_time'
        }
      },
      {
        title: '参会用户',
        dataIndex: 'user_list',
        width: '20%',
        align: 'center',
        resizable: false,
        ellipsis: true,
        slots: {
          customRender: 'user_list'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    const dataMeet = ref(window.dataMeet)
    const mockData = ref([])
    const targetKeys = ref()
    const selectedKeys = ref(['1', '4'])
    // 预约会议列表
    const bookList = ref([])
    // 当前会议列表
    const currList = ref([])
    // 历史会议列表
    const hisList = ref([])
    // 用户列表
    const userList = ref([])
    const user_obj = {} // 当前用户姓名和ID

    const meetingAction = new MeetingAction()
    let meetinfotimes = 0
    const userOptions = []
    const meetUser_state = reactive({
      indeterminate: true,
      checkAll: false,
      checkedList: []
    })

    watch(
      () => meetUser_state.checkedList,
      val => {
        meetUser_state.indeterminate = !!val.length && val.length < userOptions.length
        meetUser_state.checkAll = val.length === userOptions.length
      }
    )
    const onCheckAllChange = e => {
      Object.assign(meetUser_state, {
        checkedList: e.target.checked ? userOptions : [],
        indeterminate: false
      })
    }
    bus.$on('meetinfo', (data) => {
      const str = data
      meetinfotimes++
      console.log('收到 meetinfo 消息 ' + meetinfotimes)
      if (enterMeetCount > 0) {
        if (preMeetStr === str) {
          console.log('Repeat str [' + enterMeetCount + '] times: ' + str)
          return
        }
      }
      enterMeetCount++
      preMeetStr = str
      ifSpinning.value = true
      checkRoom()
      setTimeout(() => {
        if (if_inRoom === false) {
          meetByNotice(str, 'bus')
        }
      }, spinWaitTime)
    })

    bus.$on('bookmeetinfo', (data) => {
      console.info('in meetview，bookmeetinfo：')
      console.log(data)
      let usrlist = []
      bookList.value = []
      let meet_master_name

      data.forEach((item, index, arr) => {
        // console.info('item:' + JSON.stringify(item))
        // console.info(JSON.parse(item.user_list))
        usrlist = []
        JSON.parse(item.user_list).forEach((item1, index1, arr1) => {
          // console.info('item1:' + JSON.stringify(item1))
          usrlist += item1.user_name + ', '
        })
        userList.value.forEach((item1, index1, arr1) => {
          if (item1.user_id === item.meet_master_id) {
            meet_master_name = item1.user_name
          }
        })
        bookList.value.push({
          key: index,
          meet_number: item.meet_number,
          meet_master_id: meet_master_name,
          meet_title: item.meet_title,
          meet_topic: item.meet_topic,
          meet_time: getDataTime(item.meet_time).toLocaleString(),
          user_list: usrlist.slice(0, -2),
          meet_master_id_id: item.meet_master_id,
          meet_master_id_time: item.meet_time
        })
      })
    })

    bus.$on('currmeetinfo', (data) => {
      console.info('in meetview，currmeetinfo：')
      console.log(data)
      let usrlist
      currList.value = []
      let meet_master_name

      data.forEach((item, index, arr) => {
        // console.info('item:' + JSON.stringify(item))
        // console.info(JSON.parse(item.user_list))
        usrlist = []
        JSON.parse(item.user_list).forEach((item1, index1, arr1) => {
          // console.info('item1:' + JSON.stringify(item1))
          usrlist += item1.user_name + ', '
        })
        userList.value.forEach((item1, index1, arr1) => {
          if (item1.user_id === item.meet_master_id) {
            meet_master_name = item1.user_name
          }
        })
        currList.value.push({
          key: index,
          meet_number: item.meet_number,
          meet_master_id: meet_master_name,
          meet_title: item.meet_title,
          meet_topic: item.meet_topic,
          meet_time: getDataTime(item.meet_time).toLocaleString(),
          meet_master_id_id: item.meet_master_id,
          meet_master_id_time: item.meet_time,
          user_list: usrlist.slice(0, -2)
        })
      })
    })

    bus.$on('hismeetinfo', (data) => {
      console.info('in meetview，hismeetinfo：')
      console.log(data)
      let usrlist = []
      hisList.value = []
      let meet_master_name

      data.forEach((item, index, arr) => {
        // console.info('item:' + JSON.stringify(item))
        // console.info(JSON.parse(item.user_list))
        usrlist = []
        JSON.parse(item.user_list).forEach((item1, index1, arr1) => {
          // console.info('item1:' + JSON.stringify(item1))
          usrlist += item1.user_name + ', '
        })
        userList.value.forEach((item1, index1, arr1) => {
          if (item1.user_id === item.meet_master_id) {
            meet_master_name = item1.user_name
          }
        })
        hisList.value.push({
          key: index,
          meet_number: item.meet_number,
          meet_master_id: meet_master_name,
          meet_title: item.meet_title,
          meet_topic: item.meet_topic,
          meet_time: getDataTime(item.meet_time).toLocaleString(),
          user_list: usrlist.slice(0, -2)
        })
      })
    })

    const getDataTime = (n) => {
      const now = new Date(n)
      const y = now.getFullYear()
      const m = now.getMonth() + 1
      const d = now.getDate()
      return y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8)
    }

    const onChangeTime = (value, dateString) => {
      console.log('Selected Time: ', value)
      console.log('Formatted Selected Time: ', dateString)
      holdMeet.value.meet_time = Date.parse(dateString)
      const currentTime = new Date()
      if (holdMeet.value.meet_time > currentTime) {
        const val = holdMeet.value.meet_time - currentTime
        console.log('val' + val)
        if (val < 600000) {
          notification.error({
            message: ` 预约会议需要间隔十分钟以上！${''}`,
            description: ''
          })
          if_show_timepick.value = false
          holdMeet.value.meet_time = ''
        }
      } else {
        notification.error({
          message: ` 预约时间小于当前时间！${''}`,
          description: ''
        })
        if_show_timepick.value = false
        holdMeet.value.meet_time = ''
      }
    }
    const onChangeTimeOk = value => {
      console.log('onOk: ', value)
    }

    const onCheckChange = () => {
      if (state.checked_date === true) {
        console.log('预约会议')
        holdMeet.value.meet_method = 1
        if_show_timepick.value = true
      } else {
        console.log('立即会议')
        holdMeet.value.meet_method = 0
        if_show_timepick.value = false
      }
    }

    // 显示卡片
    const ShowCard = () => {
      if_button_notice.value = false
      if_show_timepick.value = false
      ifShowMeetList.value = false
      holdMeet.value = {}
      holdMeet.value.user_list = []
      /*
      dataMeet.value.forEach(function (item, index, arr) {
        item.lable = '未选中'
        item.checked = false
      })
    */
      ifshowCard.value = !ifshowCard.value
      setTimeout(() => {
      }, 1000)
    }

    const selectCheck = (key) => {
      const btn = document.getElementById('btn-card' + key)
      console.log(key)
      console.log(btn)
      dataMeet.value[key].checked = !dataMeet.value[key].checked
      if (dataMeet.value[key].checked === true) {
        btn.style.backgroundColor = 'red'
        dataMeet.value[key].lable = '选中'
        holdMeet.value.user_list.push({
          user_name: dataMeet.value[key].user_name,
          user_id: dataMeet.value[key].user_id
        })
      } else {
        btn.style.backgroundColor = '#00ffdaad'
        dataMeet.value[key].lable = '未选中'
        holdMeet.value.user_list.forEach(function (item, index, arr) {
          if (dataMeet.value[key].user_name === item.user_name) {
            holdMeet.value.user_list.splice(index, 1)
          }
        })
      }
      console.log(holdMeet.value.user_list)
    }

    const cancel_notice = () => {
      if_button_notice.value = true
      ifShowMeetList.value = true
      if (ifshowCard.value === true) ifshowCard.value = false
    }

    const onSubmit = () => {
      formRef.value
        .validate()
        .then(() => {
          console.log('values', nociceMeet, JSON.stringify(nociceMeet))
          send_notice()
        })
        .catch(error => {
          console.log('error', error)
        })
    }

    const send_notice = () => {
      // window.mq_client.publish(window.topic, str, { qos: 1 }, error => {
      //   if (error) {
      //     console.log('Publish error', error)
      //   }
      // })
      holdMeet.value.user_list = []

      mockData.value.forEach((item, index, arr) => {
        meetUser_state.checkedList.forEach((item1, index1, arr1) => {
          if (item1 === item.title) {
            holdMeet.value.user_list.push({
              user_name: item.title,
              user_id: item.user_id
            })
          }
        })
      })

      if (holdMeet.value.user_list.length === 0) {
        alert('请选择用户！')
        return
      }

      ifshowCard.value = false
      if_button_notice.value = true
      ifShowMeetList.value = false

      // 发起会议
      if (state.checked_date === true) {
        holdMeet.value.meet_method = 1
        if (holdMeet.value.meet_time === '') {
          notification.error({
            message: ` 未选择预约时间！${''}`,
            description: ''
          })
          return
        }
      } else {
        holdMeet.value.meet_method = 0
        holdMeet.value.meet_time = Date.now()
      }

      holdMeet.value.meet_number = nociceMeet.value.meet_number
      holdMeet.value.meet_topic = nociceMeet.value.meet_topic
      holdMeet.value.meet_title = nociceMeet.value.meet_title
      holdMeet.value.meet_master_id = window.meet_master_id

      console.log(holdMeet.value)

      holdMeeting()
    }

    const getUser = () => {
      const json_str = sessionStorage.getItem('login_state')
      if (json_str != null) {
        console.log(json_str)
        const json_obj = JSON.parse(json_str)
        current_user = json_obj.user
        console.log(current_user)
        username = current_user
      }
    }

    const openRoom = () => {
      // verify the JitsiMeetExternalAPI constructor is added to the global..
      if (window.JitsiMeetExternalAPI) {
        // const person = prompt('Please enter your name:', current_user)
        // if (person !== null || person !== '') username = person
        const tmproom = prompt('请输入房间号:', 'dtt')
        if (tmproom !== null || tmproom !== '') room = tmproom
        else room = null
      } else alert('Jitsi Meet API script not loaded')
    }

    const enterMeeting = (number) => {
      enter_button_delay.value = true
      console.log('number:' + number)
      hideNoticeCard()
      checkRoom()
      ifSpinning.value = true
      setTimeout(() => {
        if (if_inRoom === true) {
          enter_button_delay.value = false
          return
        }
        if (number !== undefined) {
          room = number
        } else {
          openRoom()
        }
        ifSpinning.value = false
        enter_button_delay.value = false
        if (room !== null) { startConference() }
      }, spinWaitTime)
    }

    const checkRoom = () => {
      let obj
      if_inRoom = false
      if (api === null) {
        console.log('api:' + api)
        return
      }
      api.getRoomsInfo().then(rooms => {
        // see response example structure
        console.log('roomInfo:' + JSON.stringify(rooms))
        obj = rooms.rooms
        console.log('list:' + JSON.stringify(obj))

        Object.keys(obj).forEach((key) => {
          console.info(obj[key])
          const str = obj[key].id
          const ret = str.indexOf('@')
          if (ret !== -1) {
            currRoom.value = str.slice(0, ret)
          } else {
            return
          }
          if (str.indexOf(room) !== -1) {
            const obj1 = obj[key].participants
            Object.keys(obj1).forEach((key1) => {
              console.info(obj1[key1])
              if (username === obj1[key1].displayName) {
                console.log(username + ' is already in room:' + str + '!!!')
                if_inRoom = true
              }
            })
          }
        })
      })
    }

    const getRoomInfo = () => {

    }

    const reOpenMeeting = (key) => {
      console.log('key: ', key)
      nociceMeet.value = []
      targetKeys.value = []
      meetUser_state.checkedList = []
      getUserList()

      hisList.value.forEach((item, index, arr) => {
        if (item.key === key) {
          console.log('reopen item: ', item)
          nociceMeet.value.meet_title = item.meet_title
          nociceMeet.value.meet_topic = item.meet_topic
          nociceMeet.value.meet_number = item.meet_number
          state.checked_date = false

          meetUser_state.checkedList = item.user_list.split(', ')

          // getUserList()

          // mockData.value.forEach((item1, index1, arr1) => {
          //   dataMeet.value.forEach((item2, index2, arr2) => {
          //     if (item2.user_id === item1.user_id) {
          //       meetUser_state.checkedList.push(
          //         item1.title
          //       )
          //     }
          //   })
          // })
        }
      })

      ifShowMeetList.value = false
      ifshowCard.value = true
    }

    const startConference = () => {
    //   const _this = this
      try {
        // const domain = 'meet.jit.si'
        hideAll()
        // const domain = 'jitsi.meet.dtt.com'
        const domain = meetDomain
        const options = {
          roomName: room,
          width: '100%',
          height: '100%',
          parentNode: document.querySelector('#meet'),
          interfaceConfigOverwrite: {
            filmStripOnly: false,
            SHOW_JITSI_WATERMARK: false,
            SHOW_WATERMARK_FOR_GUESTS: false,
            JITSI_WATERMARK_LINK: '',
            SHOW_BRAND_WATERMARK: false,
            enableWelcomePage: false
          },
          configOverwrite: {
            disableSimulcast: false
          }
        }

        // eslint-disable-next-line no-undef
        api = new JitsiMeetExternalAPI(domain, options)
        api.addEventListener('videoConferenceJoined', () => {
          console.log('Local User Joined')
          ifSpinning.value = false

          api.executeCommand('displayName', username)
          checkRoom()
          // api.executeCommand('startRecording', {
          //   mode: 'file', // recording mode, either `local`, `file` or `stream`.
          //   extraMetada: Object // any extra metada for file recording.
          // })
        })
        api.addEventListener('readyToClose', () => {
          console.log('readyToClose')
        })
        api.addEventListener('videoConferenceLeft', () => {
          console.log('Local User left')

          const obj = {}

          currList.value.forEach((item, index, arr1) => {
            if (item.meet_number === currRoom.value) {
              obj.master_id = item.meet_master_id_id
              obj.meet_id = item.meet_master_id_time
            }
          })
          const MEET_STATUS = api.getNumberOfParticipants()
          if (MEET_STATUS !== 0) {
            setTimeout(() => {
              console.info(MEET_STATUS)
            }, 3000)
          }

          if (MEET_STATUS === 0) {
            obj.description = '结束会议'
            obj.action = 0

            window.mq_client.publish('/meeting/manager', JSON.stringify(obj), { qos: 1 }, function (err) {
              if (!err) {
                console.log('发送结束会议消息成功')
              }
            })
          }
          // 返回会议初始页面
          Router.push({
            path: 'closeMeet'
          })
        })
        if_button_notice.value = false
      } catch (error) {
        console.error('Failed to load Jitsi API', error)
      }
    }

    const holdMeeting = () => {
      // baseRequestData.entity.data = JSON.parse(JSON.stringify(holdMeet.value))
      meetingAction.hold_meeting(JSON.stringify(holdMeet.value), holdMeetingSuccess, callbackError, null)
    }

    const holdMeetingSuccess = (data) => {
      console.log(data)
      if (data.error_code === 0) {
        notification.success({
          message: `已发送召开会议请求 ${''}`,
          description: ''
        })
        ifShowMeetList.value = true
      } else {
        notification.error({
          message: `发送召开会议请求失败 ${''}`,
          description: ''
        })
      }
      ifShowMeetList.value = true
    }

    const callbackError = (data) => {
      console.log(data)
    }

    const getUserList = () => {
      ifShowMeetList.value = true
      meetingAction.get_user_list(baseRequestData, getUserListSuccess, callbackError, null)
    }

    const getUserListSuccess = (data) => {
      const obj = data.data
      console.log('In getUserListSuccess obj: ' + obj)
      mockData.value = []
      userOptions.length = 0
      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        userOptions.push(obj[key].username)
        userList.value.push({
          user_name: obj[key].username,
          user_id: obj[key].id
        })
        mockData.value.push({
          key: key.toString(),
          title: obj[key].username,
          user_id: obj[key].id
        })
        if (obj[key].username === current_user) {
          user_obj.user_name = current_user
          user_obj.user_id = obj[key].id
        }
      })
      console.log('userOptions: ' + userOptions)
      if_button_notice.value = true
    }

    const getMeetingList = () => {
      console.log('In getMeetingList user_obj: ' + JSON.stringify(user_obj))
      window.mq_client.publish('/meeting/manager/get_meet_list', JSON.stringify(user_obj), { qos: 1 }, function (err) {
        if (!err) {
          console.log('发送获取会议列表成功')
        }
      })
    }

    const meetByNotice = (data, type) => {
      const str = data
      const obj = ref()

      checkRoom()
      ifSpinning.value = true
      enter_button_delay.value = true
      setTimeout(() => {
        if (if_inRoom === true) return
        console.info('进入会议方式：' + type)
        if ((str !== undefined) && (str !== null) && (str !== '')) {
          obj.value = JSON.parse(str)
          if ((obj.value.msg_type === 0) || (obj.value.msg_type === 1)) {
            console.info('确认参加会议：')
            nociceMeet.value = obj.value.list[0]
            console.info(nociceMeet.value)
            room = obj.value.list[0].meet_number
            ifSpinning.value = false
            enter_button_delay.value = false
            if (room !== undefined) { startConference() }
          }
        }
      }, spinWaitTime)
    }

    // 切换标签页触发
    const changeTable = (activeKey) => {
      if_button_notice.value = true
      switch (activeKey) {
        case '1':
          getMeetingList()
          break
        case '2':
          getMeetingList()
          break
        case '3':
          getMeetingList()
          break
      }
    }

    const hideAll = () => {
      ifshowCard.value = false
      if_button_notice.value = true
      ifShowMeetList.value = false
    }

    const hideNoticeCard = () => {
      bus.$emit('hide_notice_card', '1')
    }

    const cancelMeeting = (key) => {
      console.log('readyToCancel')
      const obj = {}
      let boolFlag = false

      // obj.master_id = bookList.value[key].meet_master_id_id
      obj.master_id = bookList.value[key].meet_master_id_id
      obj.meet_id = bookList.value[key].meet_master_id_time
      obj.description = '取消会议'
      obj.action = 1
      window.mq_client.publish('/meeting/manager', JSON.stringify(obj), { qos: 1 }, function (err) {
        if (!err) {
          console.log('发送取消会议消息成功')
        }
      })

      setTimeout(() => {
        bookList.value.forEach((item, index, arr) => {
          if ((obj.master_id === item.meet_master_id_id) && (obj.meet_id === item.meet_master_id_time)) {
            boolFlag = true
          }
        })
        if (boolFlag === true) {
          notification.error({
            message: ` 删除预约会议失败！${''}`,
            description: ''
          })
        } else {
          notification.success({
            message: ` 删除预约会议成功！${''}`,
            description: ''
          })
        }
      }, 1000)
    }

    const handleTransferChange = (nextTargetKeys, direction, moveKeys) => {
      console.log('targetKeys: ', nextTargetKeys)
      console.log('direction: ', direction)
      console.log('moveKeys: ', moveKeys)

      holdMeet.value.user_list = []

      mockData.value.forEach((item, index, arr) => {
        meetUser_state.checkedList.forEach((item1, index1, arr1) => {
          if (item1 === item.title) {
            holdMeet.value.user_list.push({
              user_name: item.title,
              user_id: item.user_id
            })
          }
        })
      })
      console.log(holdMeet.value.user_list)
    }

    onMounted(() => {
      const jsonconfig_str = sessionStorage.getItem('mesh_Config')
      if (jsonconfig_str != null) {
        console.log(jsonconfig_str)
        const json_obj = JSON.parse(jsonconfig_str)
        meetDomain = json_obj.jitsi_server
      }
      // 获取当前用户
      getUser()

      setTimeout(() => {
      // 获取用户列表
        getUserList()
      }, 1000)

      let str = ''
      str = pageDirectAction.getCurrentRouteValue()
      if (str === 'joinMeetingByRoute') {
        const meetData = sessionStorage.getItem('joinMeetingByRoute')
        if (meetData !== null) {
          console.info('页面跳转进入会议：')
          console.info(str)
          meetByNotice(meetData, 'mount')
        }
      }
      setTimeout(() => {
      // 获取会议列表
        getMeetingList()
      }, 2000)
    })

    onUnmounted(() => {
      console.log('会议组件被销毁')
      if (DebugModeOption.value === 0) {
        api.dispose()
        api = null
        const meet_box = document.getElementById('meet')
        meet_box.remove()
      }
    })

    return {
      activeKey: ref('1'),
      getUser,
      openRoom,
      ifshowCard,
      ShowCard,
      enterMeeting,
      startConference,
      columns,
      book_columns,
      curr_columns,
      his_columns,
      dataMeet,
      bookList,
      currList,
      hisList,
      selectCheck,
      send_notice,
      callbackError,
      holdMeeting,
      holdMeetingSuccess,
      getUserList,
      getMeetingList,
      cancel_notice,
      meetByNotice,
      nociceMeet,
      if_button_notice,
      onCheckChange,
      onChangeTimeOk,
      onChangeTime,
      if_show_timepick,
      ifShowMeetList,
      changeTable,
      reOpenMeeting,
      getDataTime,
      getRoomInfo,
      checkRoom,
      ifSpinning,
      hideAll,
      hideNoticeCard,
      cancelMeeting,
      handleTransferChange,
      targetKeys,
      mockData,
      selectedKeys,
      meetUser_state,
      onCheckAllChange,
      userOptions,
      Pattern,
      enter_button_delay,
      DebugModeOption,
      formRef,
      onSubmit,
      ifDisable,
      ...toRefs(state)
    }
  }

})

/*

export default1 {
  name: 'AtendimentoMedico',
  data: () => ({
    search: '',
    message: '',
    api: null,
    room: 'dtt',
    username: 'msy',
    meet_status: false,
    client: null,
    current_user: null
  }),

  mounted () {
    this.getUser()
    if (this.meet_status === false) {
      this.openRoom()
    }
  },

  methods: {
    getUser () {
      const json_str = sessionStorage.getItem('login_state')
      if (json_str != null) {
        console.log(json_str)
        const json_obj = JSON.parse(json_str)
        this.current_user = json_obj.user
        console.log(this.current_user)
      }
    },

    startConference () {
      const _this = this
      try {
        // const domain = 'meet.jit.si'
        const domain = 'jitsi.meet.dtt.com'
        const options = {
          roomName: this.room,
          width: '100%',
          height: 750,
          parentNode: document.querySelector('#meet'),
          interfaceConfigOverwrite: {
            filmStripOnly: false,
            SHOW_JITSI_WATERMARK: false,
            SHOW_WATERMARK_FOR_GUESTS: false,
            JITSI_WATERMARK_LINK: '',
            SHOW_BRAND_WATERMARK: false,
            enableWelcomePage: false
          },
          configOverwrite: {
            disableSimulcast: false
          }
        }

        // eslint-disable-next-line no-undef
        this.api = new JitsiMeetExternalAPI(domain, options)
        this.api.addEventListener('videoConferenceJoined', () => {
          console.log('Local User Joined')

          _this.api.executeCommand('displayName', _this.username)
        })
      } catch (error) {
        console.error('Failed to load Jitsi API', error)
      }
    },
    openRoom () {
      // verify the JitsiMeetExternalAPI constructor is added to the global..
      if (window.JitsiMeetExternalAPI) {
        const person = prompt('Please enter your name:', this.current_user)
        if (person !== null || person !== '') this.username = person
        const room = prompt('Please enter your room:', 'dtt')
        if (room !== null || room !== '') this.room = room
        this.startConference()
      } else alert('Jitsi Meet API script not loaded')
    }
  }
}
*/
</script>
<style  scoped>
#container {
position: absolute;
height: 100%;
width:100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.editable-row-operations a {
  margin-right: 8px;
}
.box {
position: flex;
display: flex;
justify-content: center;
align-items: center;
}
</style>
