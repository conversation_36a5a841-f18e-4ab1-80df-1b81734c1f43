class VoiceMode {
  constructor () {
    // Mode: 当前语音工作模式
    //     0 : 关闭语音服务
    //     1 : 语音对讲
    //     2 : 语音会议
    this.Mode = null

    //   扬声器增益（0-15）
    this.Speakergain = null

    //  Mic增益（0-125）
    this.Micgain = null

    //   采样率
    //     0：8000
    //     1：16000
    this.Samplerate = null

    //   背景音量
    this.Backlevel = null

    // 当前选择组播地址
    this.VoicegroupChose = null

    //   语音组1组播地址
    this.Voicegroup1 = null

    //   语音组2组播地址
    this.Voicegroup2 = null

    //   语音组3组播地址
    this.Voicegroup3 = null
  }

  init () {
    // Mode: 当前语音工作模式
    //     0 : 关闭语音服务
    //     1 : 语音对讲
    //     2 : 语音会议
    this.Mode = null

    //   扬声器增益（0-15）
    this.Speakergain = null

    //  Mic增益（0-125）
    this.Micgain = null

    //   采样率
    //     0：8000
    //     1：16000
    this.Samplerate = null

    //   背景音量
    this.Backlevel = null

    // 当前选择组播地址
    this.VoicegroupChose = null

    //   语音组1组播地址
    this.Voicegroup1 = null

    //   语音组2组播地址
    this.Voicegroup2 = null

    //   语音组3组播地址
    this.Voicegroup3 = null
  }
}

export { VoiceMode }
