<template>
    <div  class="ctrl-bar" >
        <dv-border-box-12>
            <a-row justify="center" align="middle" style="height: 100%;">
                <a-col :span="2.66" align="middle">
                    <div id="h-j" @click="btClick($event)">
                        <button2 button_name="呼叫" ></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="j-t" @click="btClick($event)">
                        <button2 button_name="监听"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="z-j" @click="btClick($event)">
                        <button2 button_name="转接"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="d-d" @click="btClick($event)">
                        <button2 button_name="代答"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="q-chai" @click="btClick($event)">
                        <button2 button_name="强拆"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="q-cha" @click="btClick($event)">
                        <button2 button_name="强插"></button2>
                    </div>
                </a-col>
                <!-- <a-col :span="2.66" align="middle">
                    <div id="z-h" @click="btClick($event)">
                        <button2 button_name="组呼"></button2>
                    </div>
                </a-col> -->
                <a-col :span="2.66" align="middle">
                    <div id="q-d" @click="btClick($event)">
                        <button2 button_name="群答"></button2>
                    </div>
                </a-col>
                <a-col :span="2.66" align="middle">
                    <div id="g-d" @click="btClick($event)">
                        <button2 button_name="挂断"></button2>
                    </div>
                </a-col>
            </a-row>
        </dv-border-box-12>
    </div>
  </template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { defineComponent, ref, onMounted } from 'vue'
import button2 from '@/views/components/dispatch/button2.vue'

export default defineComponent({
  components: {
    button2
  },
  setup (props, { emit }) {
    const hostUserInfo = ref([])
    const name = ref('')
    const number = ref('')
    const status = ref('')
    const department = ref('')
    const hasBennSetHostUser = ref(false)

    /******************************************************************************************
     * ****************************************************************************************
     * @name btClick 按钮的点击响应事件。
     * @param event 由不同按钮绑定的不同事件，目的用于找到按钮上的ID
     *
     * @description 组件内不同按钮的点击事件，点击不同按钮获取到按钮的事件，再通过emit将id传出。
     *
     * 【已存在的已知问题】：无              【可能存在的问题】：无 【是否验证】：已经验证
     *
     *  编写人：孟家宝            日期：2024年12月11日
     *
     * ***************************************************************************************
     *****************************************************************************************/
    const btClick = (event) => {
      emit('bt-click', event.currentTarget.id)
    }

    onMounted(() => {

    })
    return {
      hostUserInfo,
      name,
      number,
      status,
      department,
      hasBennSetHostUser,
      btClick,
      button2
    }
  }

})
</script>
<style scoped>
    .ctrl{
        margin-left: 20px;
        margin: 20px;
    }

    .button{
    }
</style>
