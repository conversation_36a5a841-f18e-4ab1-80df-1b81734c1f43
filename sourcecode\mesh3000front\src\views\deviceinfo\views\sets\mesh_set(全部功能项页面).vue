<template>
    <a-card title="Mesh设置">
        <a-form
                    :model="model"
                    name="nest-messages"
                    layout="horizontal"
                    @finish="confirm"
                    :validate-messages="validate">
                    <!-- 第一行 -->
                    <a-row justify="space-around" align="left" :span="24" style="margin-top:2%" >
                        <!-- 跳频开关 -->
                        <a-col :span="4" >
        <a-form-item
                        :label="LabelCaption.mesh_FreqHopping.label"
                      >

                       <a-checkbox @change="changeHoppingFre" v-model:checked="model.configData.hoppingFre" />
            </a-form-item>
                        </a-col>

                         <!-- 中心频率 -->
                        <a-col :span="4">
           <!-- <a-form-item
                        :label="LabelCaption.mesh_CenterFreq.label"
                        :name="['configData', 'centerFreq']"
                      >
                        <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.centerFreq"
                          v-bind:placeholder="LabelCaption.mesh_CenterFreq.label"
                          :disabled="model.configData.hoppingFre"
                        >
                          <a-select-option
                            v-for="option in CenterFreqOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item> -->
                        </a-col>

                        <!-- 带宽 -->
                         <a-col :span="4">
                    <!-- <a-form-item
                        :label="LabelCaption.mesh_Bandwidth.label"
                        :name="['configData', 'bandWidth']"
                      >
                        <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.bandWidth"
                          v-bind:placeholder="LabelCaption.work_mode.label"
                        >
                          <a-select-option
                            v-for="option in BandWidthOptions"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item> -->
                        </a-col>

                        <!-- 输出功率 -->
                         <a-col :span="4" >
                    <!-- <a-form-item
                        :label="LabelCaption.mesh_Outputrate.label"
                        :name="['configData', 'txPower']"
                        :rules="[{ required: true, message: '请输入输出功率' },Pattern('Outputrate')]"
                      >
                       <a-input type="number" @change="changePower" v-model:value="model.configData.txPower" />
                      </a-form-item> -->
                        </a-col>
                    </a-row>
                    <a-divider />

                    <!-- 第一行 -->
                    <a-row justify="space-around" align="left" :span="24" style="margin-top:2%" >
                         <!-- 中心频率 -->
                        <a-col :span="4">
           <a-form-item
                        :label="LabelCaption.mesh_CenterFreq.label"
                        :name="['configData', 'centerFreq']"
                      >
                        <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.centerFreq"
                          v-bind:placeholder="LabelCaption.mesh_CenterFreq.label"
                          :disabled="model.configData.hoppingFre"
                        >
                          <a-select-option
                            v-for="option in CenterFreqOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item>
                        </a-col>

                        <!-- 带宽 -->
                         <a-col :span="4">
                    <a-form-item
                        :label="LabelCaption.mesh_Bandwidth.label"
                        :name="['configData', 'bandWidth']"
                      >
                        <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.bandWidth"
                          v-bind:placeholder="LabelCaption.work_mode.label"
                        >
                          <a-select-option
                            v-for="option in BandWidthOptions"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item>
                        </a-col>

                        <!-- 输出功率 -->
                         <a-col :span="4" >
                    <a-form-item
                        :label="LabelCaption.mesh_Outputrate.label"
                        :name="['configData', 'txPower']"
                        :rules="[{ required: true, message: '请输入输出功率' },Pattern('Outputrate')]"
                      >
                       <a-input type="number" @change="changePower" v-model:value="model.configData.txPower" />
                      </a-form-item>
                        </a-col>
                        <!-- 占位 -->
                        <a-col :span="4" >
                        </a-col>

                    </a-row>
                    <!-- 第二行 -->
                   <a-row justify="space-around" align="left" :span="24" v-show="true" style="margin-top:2%">
                        <!-- 载波聚合 -->

                         <!-- 聚合频率 -->
                        <a-col :span="4">
           <a-form-item
                        :label="LabelCaption.mesh_CAFreq.label"
                      >
                      <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.centerFreq"
                          v-bind:placeholder="LabelCaption.mesh_CenterFreq.label"
                          :disabled="model.configData.hoppingFre"
                        >
                          <a-select-option
                            v-for="option in CenterFreqOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item>
                        </a-col>

                        <!-- 聚合宽带 -->
                         <a-col :span="4" >
                    <a-form-item
                        :label="LabelCaption.mesh_CABandwidth.label"
                      >
                      <a-select
                          ref="select"
                          @change="changeCenterFreBandOut"
                          v-model:value="model.configData.bandWidth"
                          v-bind:placeholder="LabelCaption.work_mode.label"
                        >
                          <a-select-option
                            v-for="option in BandWidthOptions"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item>
                        </a-col>

                        <!-- 聚合输出功率 -->
                        <a-col :span="4" >
                    <a-form-item
                        :label="LabelCaption.mesh_CAOutputrate.label"
                        :name="['configData', 'txPower']"
                        :rules="[{ required: true, message: '请输入输出功率' },Pattern('Outputrate')]"
                      >
                       <a-input type="number" @change="changePower" v-model:value="model.configData.txPower" />
                      </a-form-item>
                        </a-col>
                        <a-col :span="4" >
            <!-- <a-form-item
                        :label="LabelCaption.mesh_CA.label"
                      >
                      <a-checkbox :disabled=true />
            </a-form-item> -->
                        </a-col>
                    </a-row>
                    <a-divider />
                    <!-- 第三行 -->
                    <a-row justify="space-around" align="left" :span="24" style="margin-top:2%">
                         <!-- 距离 -->
                        <a-col :span="4">
                             <a-form-item
                        :label="LabelCaption.mesh_Distance.label"
                        :name="['configData', 'distance']"
                      >
                        <a-select
                          ref="select"
                          @change="changeRegisterDistensKey"
                          v-model:value="model.configData.distance"
                        >
                          <a-select-option
                            v-for="option in DistanceOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item>
                        </a-col>

                        <!-- 空口密钥 -->
                         <a-col :span="4">
                    <a-form-item
                        :label="LabelCaption.mesh_Key.label"
                        :name="['configData', 'meshKey']"
                        :rules="[{ required: true, message: '请输入空口密钥' },Pattern('MeshKey')]"
                      >
                       <a-input @change="changeRegisterDistensKey" v-model:value="model.configData.meshKey" />
                      </a-form-item>
                        </a-col>
                         <!-- 监听模式 -->
                         <a-col :span="4" >
           <!-- <a-form-item
                        :label="LabelCaption.mesh_WorkMode.label"
                      >
                        <a-checkbox @change="changelisten" v-model:checked="model.configData.workMode"/>
            </a-form-item> -->
                        </a-col>

                        <!-- 注册间隔 -->
                         <a-col :span="4">
                    <!-- <a-form-item
                        :label="LabelCaption.mesh_RegisterPeriod.label"
                        :name="['configData', 'registerPeriod']"
                        :rules="[Pattern('RegisterPeriod')]"
                      >
                       <a-input @change="changelisten" v-model:value="model.configData.registerPeriod"/>
                      </a-form-item> -->
                        </a-col>

                    </a-row>
                    <a-divider />

                    <!-- 第三行 -->
                    <a-row justify="space-around" align="left" :span="24" style="margin-top:2%">
                     <!-- 监听模式 -->
                        <a-col :span="4" >
           <a-form-item
                        :label="LabelCaption.mesh_WorkMode.label"
                      >
                      <!-- <a-switch  v-model:checked="model.configData.workMode" -->
                        <a-checkbox @change="changelisten" v-model:checked="model.configData.workMode"/>
            </a-form-item>
                        </a-col>

                        <!-- 注册间隔 -->
                         <a-col :span="4">
                    <a-form-item
                        :label="LabelCaption.mesh_RegisterPeriod.label"
                        :name="['configData', 'registerPeriod']"
                        :rules="[Pattern('RegisterPeriod')]"
                      >
                       <a-input @change="changelisten" v-model:value="model.configData.registerPeriod"/>
                      </a-form-item>
                        </a-col>

                         <!-- 距离 -->
                        <a-col :span="4">
                             <!-- <a-form-item
                        :label="LabelCaption.mesh_Distance.label"
                        :name="['configData', 'distance']"
                      >
                        <a-select
                          ref="select"
                          @change="changeRegisterDistensKey"
                          v-model:value="model.configData.distance"
                        >
                          <a-select-option
                            v-for="option in DistanceOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}</a-select-option
                          >
                        </a-select>
                      </a-form-item> -->
                        </a-col>

                        <!-- 空口密钥 -->
                         <a-col :span="4">
                    <!-- <a-form-item
                        :label="LabelCaption.mesh_Key.label"
                        :name="['configData', 'meshKey']"
                        :rules="[{ required: true, message: '请输入空口密钥' },Pattern('MeshKey')]"
                      >
                       <a-input @change="changeRegisterDistensKey" v-model:value="model.configData.meshKey" />
                      </a-form-item> -->
                        </a-col>
                    </a-row>

                   <a-divider />

                     <!-- 第四行 -->
                     <a-row align="left" :span="4" v-show="false" style="margin-top:2%">

                         <!-- 节点白名单开关 -->
                        <a-col :span="4" style="margin-left:4%" >
           <a-form-item
                        :label="LabelCaption.mesh_WhiteListEnabled.label"
                      >
                      <a-checkbox   :disabled=true v-model:checked="model.configData.model"/>
            </a-form-item>
                        </a-col>

                        <a-col>
                            <a-form-item v-show="model.configData.model">
                       <a-row>
                        <a-input style="width:60%"/>
                        <a-button >
                            删除记录
                        </a-button >
                       </a-row>

                      </a-form-item>
                      <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button v-show="model.configData.model">
            添加记录
          </a-button>
        </a-form-item>
                        </a-col>
    <a-divider />
                    </a-row>

                    <!-- 提交按钮 -->
                    <a-row style="margin-left:2%">
                      <a-col :span="2">
                        <a-form-item >
                        <a-button type="primary" html-type="submit">保存</a-button>
                        </a-form-item>
                      </a-col>
                      <a-col :span="2">
                        <a-form-item >
                        <a-button type="primary" @click="getmeshSet ">刷新</a-button>
                        </a-form-item>
                      </a-col>
                       <!-- 频点规划 -->
                        <a-col :span="5" >
           <a-form-item>
                      <a-button type="primary" style="margin-left:20%" @click="showModal" disabled="true">频点规划</a-button>
                       <a-modal
                        v-model:visible="visible"
                       :closable=false
                       okType="primary"
                       okText="确认"
                       cancelText="取消"
                        title="频点规划"
                        @ok="handleOk">
                        <a-row>
                        <a-col span=10>
                            <a-row style="margin-top:5%">
                                <a-form-item
                                 :name="['configData', 'meshKey']"
                                 >
                                    <a-input :addon-before="LabelCaption.mesh_BeginFreq.label" addon-after="Mhz"/>
                                </a-form-item>
                            </a-row>
                            <a-row >
                                <a-col>
                                <a-form-item
                                 :name="['configData', 'meshKey']"

                                 >
                                    <a-input :addon-before="LabelCaption.mesh_EndFreq.label" addon-after="Mhz"/>
                                </a-form-item>
                                </a-col>
                            </a-row>
                            <a-row >
                                <a-form-item
                                 :name="['configData', 'meshKey']"
                                 >
                                    <a-input :addon-before="LabelCaption.mesh_SpaceFreq.label" addon-after="Mhz"/>
                                </a-form-item>
                            </a-row>
                            <a-row >
                                <a-space>
                                <a-button  >批量添加</a-button>
                                 <a-form-item
                                 :name="['configData', 'meshKey']"
                                 >
                                  <a-popover
                                  title="添加单条" trigger="click">
                                    <template #content>
                                    <a-input  v-model:value="LabelCaption.mesh_EndFreq.label"/>
                                    <a-space :size="15">
                                    <a @click="addCenterFreqList">确定</a>
                                    <a @click="hide">取消</a>
                                    </a-space>
                                    </template>
                                    <a-button>添加单条</a-button>
                                </a-popover>
                                </a-form-item>
                                </a-space>
                            </a-row>
                        </a-col>
                        <a-col span=10>
                           <a-card
                           size="small"
                           title="中心频率列表"
                            style="width: 200px;
                             height: 200px;
                              overflow:scroll;
                              overflow-x:hidden;
                               margin-left:20%;">
                                <a-form-item
                                 v-for="(n,i) in model.configData.pccfreq"
                                    v-bind:key="n.value"
                                    :name="['configData', 'pccfreq']">
                                    <a-space>
                                    {{model.configData.pccfreq[i]}}
                                 <a-button type="link" @click="deletepccfreq(i)">
                            删除
                        </a-button >
                        </a-space>
                                </a-form-item>

                            </a-card>
                        </a-col>
                        </a-row>
                       </a-modal>
            </a-form-item>
                        </a-col>
                      </a-row>
                      </a-form>

    </a-card>
    </template>
