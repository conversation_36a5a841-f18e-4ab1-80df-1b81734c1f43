<template>
  <div class="limiter">
    <div class="container-login100">
      <div class="wrap-login100">
        <div class="login100-pic js-tilt" data-tilt="" style="will-change: transform; transform: perspective(300px) rotateX(0deg) rotateY(0deg);">
          <img src="@/assets/img/img-01.png" alt="IMG">
        </div>
        <form class="login100-form validate-form" v-on:submit.prevent>
          <span class="login100-form-title">
            DTT-MESH3000
          </span>
          <a-row>
            <a-col :span="23">
                <a-row>
                    <div class="wrap-input100 validate-input">
                    <input class="input100" type="text" name="email" placeholder="用户名" v-model="username">
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    </span>
                </div>
                </a-row>
               <a-row>
                <div class="wrap-input100 validate-input">
                    <input class="input100" :type="passwordShow ? 'text' : 'password'"   name="pass" placeholder="密码" v-model="password">
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                    <i class="fa fa-lock" aria-hidden="true"></i>
                    </span>
                </div>
               </a-row>
            </a-col>
            <a-col :span="1">
                <a-row style="height:50%">

                </a-row>
                <a-row style="height:50%; margin-left: 30%;">
                    <button>
                        <EyeOutlined style="color:dodgerblue;" @click="show()"/>
                    </button>
                </a-row>
            </a-col>
        </a-row>
          <div class="container-login100-form-btn">
            <button class="login100-form-btn" @click="user_login">
              登录
            </button>
          </div>
          <a-row justify="space-around" class="text-center p-t-12">
            <a-col>
                <a-checkbox class="txt2" v-model:checked="remember" >记住账号及密码</a-checkbox>
            </a-col>
            <a-col>
                <a class="txt2" @click="forgetPassword" >
                忘记密码?
                </a>
            </a-col>
        </a-row>
          <div class="text-center p-t-136">
              <i class="fa fa-code " aria-hidden="true"></i> v1.1 release_20220830 <br>
              <i class="fa fa-copyright"></i>&nbsp;2022&nbsp;XADT
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, defineComponent, ref } from 'vue'
import Router from '../router/index'
import { message, Modal } from 'ant-design-vue'
import { UserRequestAction } from '@/views/deviceinfo/action/userRequestAction'
import { MeetingAction } from '@/views/deviceinfo/action/meetingAction'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
import { EyeOutlined } from '@ant-design/icons-vue'
import md5 from 'js-md5'
import Cookies from 'js-cookie'
import CryptoJS from 'crypto-js'

export default defineComponent({
  components: { EyeOutlined },
  setup () {
    const username = ref('')
    const password = ref('')
    const remember = ref(false)
    const passwordShow = ref(false)

    let hostNumber = null

    const userRequestAction = new UserRequestAction()
    const meetingAction = new MeetingAction()

    const show = () => {
      passwordShow.value = !passwordShow.value
    }
    // 如果Cokies中没有账号和密码，则将记住密码复选框置为false
    if (!Cookies.get('userName') || !Cookies.get('passWord')) {
      // 调试
    //   alert(Cookies.get('userName'))
    //   alert(Cookies.get('userName'))
      // 将复选框置为false
      remember.value = false
    } else {
      // cokies中存在账号和密码则将记住密码复选框置为true并读取cookie中的账号密码
      // 从cookie中读取用户名和密码
      username.value = Cookies.get('userName') ? Cookies.get('userName') : ''
      password.value = Cookies.get('passWord') ? Cookies.get('passWord') : ''
      password.value = CryptoJS.AES.decrypt(password.value, 'user123!').toString(CryptoJS.enc.Utf8)
      //   alert(Cookies.get('userName'))
      //   alert(Cookies.get('passWord'))
      //   alert(password.value)
      // 将复选框置为true
      remember.value = true
    }

    // 当用户点击忘记密码
    const forgetPassword = () => {
      Modal.info({
        title: '忘记密码？',
        content: '请联系供应商获取超级账户。',
        okText: '知道了'
      })
    }

    // // 当用户点击版本号时
    // const version = () => {
    //   Modal.info({
    //     title: '版本',
    //     content: {
    //       123: '123'
    //     },
    //     okText: '知道了'
    //   })
    // }

    const user_login = () => {
      passwordShow.value = false
      if (username.value.trim() === '') {
        message.info('请填写用户名!')
        return
      }

      if (password.value.trim() === '') {
        message.info('请填写密码!')
        return
      }
      // 判断保存密码复选框是否选中
      if (remember.value === true) {
        // 添加cookie
        Cookies.set('userName', username.value, {
          expires: 30
        })
        // 添加cookie
        Cookies.set('passWord', CryptoJS.AES.encrypt(password.value, 'user123!'), {
          expires: 30
        })
      } else {
        // 删除cookie
        Cookies.remove('userName')
        Cookies.remove('passWord')
      }
      const formData = new FormData()
      formData.append('username', username.value)
      password.value = md5(password.value)
      // 如果Mmd5加密后的密码和Cookie中保存的密码相同，则说明
      formData.append('password', password.value)
      userRequestAction.login(formData, getUserSuccess, callbackError, null)
    }
    // 用于记住账号密码复选框值调试
    // const changeRemember = () => {
    //   alert(remember.value)
    // }

    const getUserSuccess = (data, hader) => {
      console.log(data)
      console.log(hader.number)
      if (data === '登录成功') {
        message.success('登录成功')
        const myDate = new Date()
        const login_user = { user: username.value, login_time: myDate.toLocaleString() }
        console.info(login_user)
        const json = JSON.stringify(login_user)
        sessionStorage.setItem('login_state', json)
        console.info('login_state:' + json)
        // if (login_user.user === 'admin') {
        //   Object.keys(AuthUi).forEach((key) => {
        //     AuthUi[key].vis = true
        //   })
        // // } else {
        // //   Object.keys(AuthUi).forEach((key) => {
        // //     if ((AuthUi[key].name === 'login_item1') || (AuthUi[key].name === 'login_item2') || (AuthUi[key].name === 'login_item3') || (AuthUi[key].name === 'login_item4')) { AuthUi[key].vis = false }
        // //     if ((AuthUi[key].name === 'left_item_video') || (AuthUi[key].name === 'left_item_videohk') || (AuthUi[key].name === 'left_item_topo') || (AuthUi[key].name === 'main_item_wifi') || (AuthUi[key].name === 'main_item_dhcp')) { AuthUi[key].vis = false }
        // //   })
        // }
        console.info(AuthUi)
        const json_auth = JSON.stringify(AuthUi)
        sessionStorage.setItem('AuthUi', json_auth)
        getConfig()
        setTimeout(() => {
          Router.push({
            path: '/'
          })
        }, 1000)

        // 获取设定的坐席号码
        if (hader.number !== null || hader.number !== undefined) {
          hostNumber = hader.number
        } else {
          message.error('获取坐席号码失败！')
        }
      } else {
        if (data.error_code === 1002) {
          message.warning('账号或密码错误')
          // 删除cookie
          Cookies.remove('userName')
          Cookies.remove('passWord')
          remember.value = false
        } else {
          message.warning('登录错误：其他错误！')
        }
        // 将密码框置为空
        password.value = ''
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      console.error(error.message, 3)
      message.warning('登录请求错误:' + error.message)
      // 将密码框置为空
      password.value = ''
    }

    const getConfig = () => {
      // baseRequestData.entity.data = JSON.parse(JSON.stringify(holdMeet.value))
      meetingAction.get_config('', getConfigSuccess, getConfig_callbackError, null)
    }

    const getConfigSuccess = (data) => {
      console.log(data)
      const mesh_Config = {
        mqtt_bloker_wss: data.data.mqtt_bloker_wss,
        mqtt_bloker_tcp: data.data.mqtt_bloker_tcp,
        mqtt_bloker_wx: data.data.mqtt_bloker_wx,
        mqtt_bloker_ws: data.data.mqtt_bloker_ws,
        jitsi_server: data.data.jitsi_server,
        mqtt_username: data.data.mqtt_username,
        mqtt_password: data.data.mqtt_password,
        video_server: data.data.video_server,
        map_server: data.data.map_server,
        simulate_nodes: data.data.simulate_nodes,
        simulate_style: data.data.simulate_style,
        host_number: hostNumber
      }
      console.info(mesh_Config)
      const json = JSON.stringify(mesh_Config)
      sessionStorage.setItem('mesh_Config', json)
    }

    const getConfig_callbackError = (error) => {
      console.error(error.message, 3)
    }

    onMounted(() => {

    })

    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      username,
      password,
      EyeOutlined,
      remember,
      passwordShow,
      show,
      //   version,
      forgetPassword,
      //   changeRemember,
      user_login,
      getConfig,
      getConfigSuccess,
      getConfig_callbackError
    }
  }
})
</script>

<style>
  @import '@/assets/css/font-awesome.min.css';
  @import '@/assets/css/main.css';
  @import '@/assets/css/util.css';

  body {
      background: url(@/assets/img/backgroundnew.svg) top center no-repeat;
      background-size: cover;
    }
</style>
