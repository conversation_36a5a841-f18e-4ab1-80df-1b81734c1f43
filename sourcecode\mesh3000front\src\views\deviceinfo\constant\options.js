export const BandWidthOptions = [
  { value: 0, name: '1.4M' },
  { value: 1, name: '3M' },
  { value: 2, name: '5M' },
  { value: 3, name: '10M' },
  //   { value: 4, name: '15M' },（保留，暂时不可用）
  { value: 5, name: '20M' }
]

export const WorkModeOptions = [
  { value: 0, name: '正常模式' },
  { value: 1, name: '监听模式' }
]

export const TrunkOptions = [
  { value: 0, name: '不作为中继节点' },
  { value: 1, name: '可作为中继节点' }
]

export const device_type = [
  { value: 1, name: '手持' },
  { value: 2, name: '背负' },
  { value: 3, name: '车载' }
]

export const RoutingHealthIndexOptions = [
  { value: 0, name: '(输出速率/输入速率)>1' },
  { value: 1, name: '0.8<=(输出速率/输入速率)<=1' },
  { value: 2, name: '(输出速率/输入速率)<0.8' }
]

export const InNetworkOptions = [
  { value: 0, name: '不在网' },
  { value: 1, name: '在网' },
  { value: 2, name: '离线' },
  { value: -1, name: '离线' }
]

export const DistanceOptions = [
  { value: 0, name: '近距离' },
  { value: 1, name: '预留' },
  { value: 2, name: '远距离（100km）' }
]

export const HDMIinOptions = [
  { value: 0, name: '无HDMI设备连接' },
  { value: 1, name: '有HDMI设备连接' }
]

export const DeviceTypeOptions = [
  { value: 1, name: '手持' },
  { value: 2, name: '背负' },
  { value: 3, name: '车载' },
  { value: 4, name: '机载' }

]
// 工作模式选项
export const WfModeOptions = [
  { value: 0, name: '公网+专网+自组网' },
  { value: 1, name: '公网+自组网' },
  { value: 2, name: '专网+自组网' },
  { value: 3, name: '公网' },
  { value: 4, name: '专网' },
  { value: 5, name: '自组网' }
]
// 防火墙选项
export const firewallOptions = [
  { value: 0, name: '关闭防火墙' },
  { value: 1, name: '设置白名单' },
  { value: 2, name: '设置黑名单' }
]
// 语音选项
export const VoiceOption = [
  { value: 0, name: '关闭语音服务' },
  { value: 1, name: '语音对讲' },
  { value: 2, name: '语音会议' },
  { value: 5, name: '语音蓝牙' }
]
// 中心频率
export const CenterFreqOption = [
  { key: 0, value: 14300, name: '1430' },
  { key: 1, value: 14400, name: '1440' },
  { key: 2, value: 14500, name: '1450' },
  { key: 3, value: 14600, name: '1460' },
  { key: 4, value: 14700, name: '1470' },
  { key: 5, value: 14800, name: '1480' },
  { key: 6, value: 14900, name: '1490' },
  { key: 7, value: 15000, name: '1500' },
  { key: 8, value: 15100, name: '1510' },
  { key: 9, value: 15200, name: '1520' }
]
// 带宽
export const BandWidthOption = [
  { value: 1, name: '1.4M' },
  { value: 2, name: '3M' },
  { value: 3, name: '5M' },
  { value: 4, name: '10M' },
  { value: 5, name: '20M' }
]
// 距离
export const DistanceOption = [
  { value: 0, name: '10Km' },
  { value: 2, name: '100Km' }
]

export const VideoMode = [
  { value: 0, name: '关闭' },
  { value: 1, name: '开启' }
]

export const AppMode = [
  { value: 1, name: '开启视频传输功能' },
  { value: 2, name: '关闭视频录像功能' },
  { value: 3, name: '关闭视频传输和录像功能' }
]

export const Framerate = [
  { value: 1, name: '25' },
  { value: 2, name: '30' }
]

export const Encoding = [
  { value: 2, name: 'H264' },
  { value: 1, name: 'H265' }
]

export const Resolution = [
  { value: 0, name: '1920*1080' }
]

export const MeshNetID = [
  { value: 0, name: '0' },
  { value: 1, name: '1' },
  { value: 2, name: '2' },
  { value: 3, name: '3' },
  { value: 4, name: '4' },
  { value: 5, name: '5' },
  { value: 6, name: '6' },
  { value: 7, name: '7' }
]
// 串口配置
// 数据模式
export const DataMode = [
  { value: 1, name: 'UDP' },
  { value: 2, name: 'TCP CLIENT' },
  { value: 3, name: 'TCP SERVER' }
]
// 波特率
export const Baudrate = [
  { value: 'B4800', name: 'B4800' },
  { value: 'B9600', name: 'B9600' },
  { value: 'B115200', name: 'B115200' },
  { value: 'B460800', name: 'B460800' }
]
// 奇偶校验
export const Party = [
  { value: 0, name: '不设置，默认' },
  { value: 1, name: '奇数位' },
  { value: 2, name: '偶数位' }
]
// 停止位
export const Stop = [
  { value: 0, name: '不设置，默认' },
  { value: 1, name: '1位' },
  { value: 2, name: '2位' }
]

// 播放路数
export const Plays = [
  { value: 0, name: '单路播放' },
  { value: 1, name: '四路播放' },
  { value: 2, name: '九路播放' }
  { value: 4, name: '--待选择--' }
]

// 用户类型选项
export const UserType = [
  { value: 0, name: '超级用户' },
  { value: 1, name: '普通用户' }
]
// 通道
export const channel = [
  { value: '0', name: '第一通道' },
  { value: '1', name: '第二通道' },
  { value: '2', name: '第三通道' },
  { value: '3', name: '第四通道' },
  { value: '4', name: '第五通道' },
  { value: '5', name: '第六通道' },
  { value: '6', name: '第七通道' },
  { value: '7', name: '第八通道' },
  { value: '8', name: '第九通道' },
  { value: '10', name: '第十通道' },
  { value: '11', name: '第十一通道' },
  { value: '12', name: '第十二通道' }
]
// 摄像头厂商
export const firm = [
  { value: 0, name: '海康威视' },
  { value: 1, name: '浙江大华' }
]

// 摄像头类型
export const type = [
  { value: 1, name: '网络摄像机' },
  { value: 0, name: '录像机' },
  { value: 2, name: 'Mesh3000设备HDMI接口' }
]

// 码流选择
export const stream = [
  { value: '1', name: '主码流' },
  { value: '2', name: '子码流' },
  { value: '3', name: '第三码流' }
]

// 设备生产日期
export const years = [
  { value: 0, name: '2012年前' },
  { value: 1, name: '2012年后' }

]

// 载波聚合工作模式
export const CAMode = [
  { value: 0, name: '非CA非MIMO' },
  { value: 1, name: 'CA' },
  { value: 2, name: 'MIMO' }
]

// GPS工作模式
export const GPSMode = [
  { value: 1, name: 'GPS模式' },
  { value: 2, name: '北斗模式' },
  { value: 3, name: 'GPS+北斗模式' }
]

// 扫频带宽
export const SweepBandWidth = [
  { value: 0, name: '1.4M' },
  { value: 1, name: '3M' },
  { value: 2, name: '5M' },
  { value: 3, name: '10M' },
  { value: 5, name: '20M' }
]

export const encryptModeOption = [
  { value: 0, name: '无' },
  { value: 1, name: 'SNOW' },
  { value: 2, name: 'AES' },
  { value: 3, name: 'ZUC' }
]

export const DebugModeOption = [
  // { value: 0, name: '正常模式' }
  { value: 1, name: '调试模式' }
]
