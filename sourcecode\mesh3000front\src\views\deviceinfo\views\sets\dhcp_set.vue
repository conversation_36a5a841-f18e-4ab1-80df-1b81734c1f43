<template>
  <a-card title="DHCP设置" :span="24">
    <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">

      <a-row justify="space-around" style="margin-top:3%">
        <a-col :span="6">
          <a-form-item label="DHCP开关">
            <a-switch v-model:checked="checked" @change="onchange" />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :name="['configData', 'Firstaddr']" :rules="[{ required: true, message: '起始地址不能为空' },pattern=Pattern('IP')]" :label="LabelCaption.dhcp_firstAddr.label">
            <a-input v-model:value="model.configData.Firstaddr" :disabled="(model.configData.Enabled == 0)?true:false" />
          </a-form-item>
        </a-col>

        <a-col :span="6">
          <a-form-item :name="['configData', 'Lastaddr']" :rules="[{ required: true, message: '结束地址不能为空' },Pattern('IP')]" :label="LabelCaption.dhcp_lastAddr.label">
            <a-input v-model:value="model.configData.Lastaddr" :disabled="(model.configData.Enabled == 0)?true:false" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row justify="space-around" style="margin-top:3%">
        <a-col :span="6">
          <a-form-item :name="['configData', 'SubMask']" :rules="[{ required: true, message: '子网掩码不能为空' },Pattern('IP')]" :label="LabelCaption.dhcp_subMask.label">
            <a-input v-model:value="model.configData.SubMask" :disabled="(model.configData.Enabled == 0)?true:false" />

          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item :name="['configData', 'Gatewayaddr']" :rules="[Pattern('IP')]" :label="LabelCaption.dhcp_gateAddr.label">
            <a-input v-model:value="model.configData.Gatewayaddr" :disabled="(model.configData.Enabled == 0)?true:false" />

          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item :name="['configData', 'Dnsaddr']" :rules="[Pattern('IP')]" :label="LabelCaption.dhcp_dnsAddr.label">
            <a-input v-model:value="model.configData.Dnsaddr" :disabled="(model.configData.Enabled == 0)?true:false" />

          </a-form-item>
        </a-col>
      </a-row>
      <a-divider />
      <a-row justify="center">
        <a-col :span="2">
          <a-form-item>
            <!-- <a-button type="primary" html-type="submit" v-if="AuthUi.WiFi.status">保存</a-button> -->
            <a-button type="primary" html-type="submit" v-if="AuthUi.DHCP.statusW">保存</a-button>
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-form-item>
            <a-button type="primary" @click="getDhcp">刷新</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-card>
</template>
<script>
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { DhcpRequestAction } from '@/views/deviceinfo/action/dhcpRequestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { Dhcp } from '@/views/deviceinfo/model/Dhcp'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

export default defineComponent({
  setup () {
    // 开关控件显示
    const checked = ref(false)
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const dhcp = new Dhcp()
    model.configData = dhcp

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const dhcpRequestAction = new DhcpRequestAction()
    const pageDirectAction = new PageDirectAction()
    const wifi_set_value = ref(2)

    // 组件状态进行改变时候，改变model的值
    const onchange = () => {
      if (checked.value === false) {
        model.configData.Enabled = 0
      } else model.configData.Enabled = 1
    }

    // 当model进行更改时候，改变组件状态
    const onModel = () => {
      if (model.configData.Enabled === 0) {
        checked.value = false
      } else {
        checked.value = true
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    // ****************** 根据主键查询数据 *******************
    /*  const getDeviceinfoByIdSuccess = (data) => { */
    /*    if (data.error_code === ErrorInfo.Success) { */
    /*      model = data.data */
    /*      getDhcp() */
    /*    } */
    /*  } */

    /*  const getDeviceinfoByIdFinally = () => { */
    /*    console.info('OK') */
    /*  } */

    /*  const getDeviceinfoById = () => { */
    /*    requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally) */
    /*  } */

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('DHCP信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置DHCP信息失败!${data.error_code}`)
      }
    }

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      dhcpRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前DHCP参数 *******************
    const getDhcp = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      dhcpRequestAction.query(baseRequestData, getDhcpSuccess, callbackError, null)
    }

    const getDhcpSuccess = (data) => {
      model.configData = data.data.configData
      // this.model = data.data
      console.info(dhcp.value)
      onModel()
    }
    // ****************** 设备当前DHCP参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getDhcp()
      // getDeviceinfoById()
    })

    const onFinish = (values) => {
      console.log('Success:', values)
    }

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      wifi_set_value,
      checked,
      onModel,
      confirm,
      Pattern,
      onFinish,
      getDhcp,
      onchange,
      AuthUi
    }
  }
})
</script>
