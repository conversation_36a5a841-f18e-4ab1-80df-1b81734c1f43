
<template>

<a-card title="语音设置">
   <a-form
                :model="model"
                name="nest-messages"
                layout="horizontal"
                @finish="confirm"
                :validate-messages="validate">
                  <!-- 语音组 -->
                  <a-row justify="center" >
                   <a-form-item>
                  <a-card hoverable title="工作方式与地址选择" size="small" style="width:400px">
                     <!-- 模式选择下拉框 -->
               <a-row justify="space-around" style="margin-top:2%">
                <a-form-item
                    :label="LabelCaption.work_mode.label"
                    :name="['configData', 'Mode']"
                  >
                    <a-select
                      v-model:value="model.configData.Mode"
                      v-bind:placeholder="LabelCaption.work_mode.label"
                      style="width:170px"
                    >
                      <a-select-option
                        v-for="option in VoiceOption"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-row>
                    <a-row justify="space-around" style="margin-top:2%">
                   <a-radio-group
                    v-model:value="model.configData.VoicegroupChose"
                   style="margin-left:3%">

                       <a-col>
                        <a-row>
                    <a-radio :style="radioStyle"
                             :value="0"
                      >
                      <a-form-item
                      :rules="[{ required:true, message: '组播地址不能为空' },Pattern('IP')]"
                      :name="['configData', 'Voicegroup1']">
                      <a-input   style="width: 200px; margin-left: 30px" v-model:value="model.configData.Voicegroup1"/>
                      </a-form-item>
                      </a-radio>
                        </a-row>
                    <a-row>
                    <a-radio :style="radioStyle"
                             :value="1"
                      >
                       <a-form-item
                      :rules="[{ required:true, message: '组播地址不能为空' },Pattern('IP')]"
                      :name="['configData', 'Voicegroup2']">
                      <a-input  style="width: 200px; margin-left: 30px" v-model:value="model.configData.Voicegroup2"/>
                       </a-form-item>
                      </a-radio>
                        </a-row>
                        <a-row>
                    <a-radio :style="radioStyle"
                             :value="2"
                      >
                       <a-form-item
                      :rules="[{ required:true, message: '组播地址不能为空' },Pattern('IP')]"
                      :name="['configData', 'Voicegroup3']">
                      <a-input  style="width: 200px; margin-left: 30px" v-model:value="model.configData.Voicegroup3"/>
                       </a-form-item>
                      </a-radio>
                        </a-row>
                       </a-col>
                </a-radio-group>
                    </a-row>
                  </a-card>
                  </a-form-item>
                   <!-- 扬声器音量 -->
                 <a-form-item>
                  <a-card hoverable title="音量设置" size="small" style="width:400px;margin-left:3%">
                <a-row justify="space-around" style="margin-top:10%">
                  <a-form-item
                  style="width:60%"
                    :label="LabelCaption.voice_Speakergain.label"
                    :name="['configData', 'speakergain']"
                  >
                    <a-slider
                      ref="slider"
                      v-model:value="model.configData.Speakergain"
                            :max="5"
                            :min="1"
                      style="width: 60%"
                      v-bind:placeholder="LabelCaption.voice_Speakergain.label"
                    />
                  </a-form-item>
                </a-row>

                  <!-- 麦克风音量 -->
                   <a-row justify="space-around" style="margin-top:2%">
                   <a-form-item
                   style="width:60%"
                    :label="LabelCaption.voice_Micgain.label"
                    :name="['configData', 'Micgain']"
                  >
                    <a-slider
                      ref="slider"
                      v-model:value="model.configData.Micgain"
                       :max="125"
                      style="width: 60%"
                      v-bind:placeholder="LabelCaption.voice_Micgain.label"
                    />
                  </a-form-item>
                   </a-row>
                  <!-- 背景音量 -->
                   <a-row justify="space-around" style="margin-top:2%">
                  <a-form-item
                  style="width:60%"
                    :label="LabelCaption.voice_Backlevel.label"
                    :name="['configData', 'Backlevel']"
                  >
                    <a-slider
                      ref="slider"
                      v-model:value="model.configData.Backlevel"
                             :disabled="true"
                      style="width: 60%"
                      v-bind:placeholder="LabelCaption.voice_Backlevel.label"
                    />
                  </a-form-item>
                   </a-row>
                   </a-card>
                  </a-form-item>
                  </a-row>
                    <!-- 分割线 -->
                <a-divider />
    <a-row justify="space-around">
     <a-form-item v-bind="formItemLayoutWithOutLabel">
      <a-button type="primary" html-type="submit" @click="submitForm" v-if="AuthUi.语音.statusW">保存</a-button>
      <a-button type="primary" style="margin-left: 10px" @click="getVoiceMode">刷新</a-button>
    </a-form-item>
    </a-row>
   </a-form>
</a-card>

</template>

<script>
import { QuestionOutlined } from '@ant-design/icons-vue'
import { onMounted, defineComponent, createVNode, reactive } from 'vue'
import { Modal, message } from 'ant-design-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { VoiceModeRequestAction } from '@/views/deviceinfo/action/voiceModeAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { VoiceMode } from '@/views/deviceinfo/model/VoiceMode'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import {
  VoiceOption
} from '@/views/deviceinfo/constant/options'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

export default defineComponent({

  setup () {
    // 单选按钮风格
    const radioStyle = reactive({
      display: 'flex',
      height: '50px',
      lineHeight: '30px',
      size: 'large'

    })

    const onFinish = values => {
      console.log('Received values of form:', values)
    }

    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const voicemode = new VoiceMode()
    model.configData = voicemode

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const voiceModeRequestAction = new VoiceModeRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }
    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('语音信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置语音信息失败!${data.error_code}`)
      }
    }

    const set = () => {
    //  model.configData.Enabled === true ? 1 : 0
      voiceModeRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null

      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前防火墙参数 *******************
    const getVoiceMode = () => {
      voiceModeRequestAction.query(baseRequestData, getVoiceModeSuccess, callbackError, null)
    }

    const getVoiceModeSuccess = (data) => {
      model.configData = data.data.configData
      //   console.log(model.configData.IpAddr.toString());
    }
    // ****************** 设备当前防火墙参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getVoiceMode()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      VoiceOption,
      AuthUi,
      confirm,
      Pattern,
      getVoiceMode,
      onFinish,
      // 单选按钮参数
      radioStyle
    }
  }
})
</script>
