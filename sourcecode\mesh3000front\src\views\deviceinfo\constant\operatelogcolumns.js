/* import { moment } from 'moment'  */// 这个moment方法。框架里本来就有引入就好
/*  */
/* const formatterTime = (val) => { */
/*   return val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '' */
/* } */
/*  */
/* import { InNetworkOptions } from '@/views/deviceinfo/constant/options' */
/*  */
/* export default function formatter (val, dict) { */
/*   return val ? dict[val] : '' */
/* } */

export const getFullDate = (date) => {
  const dateAndTime = date.split('T')
  return dateAndTime[0].split('-').reverse().join('-')
}

export const OperateLogColumns = [
  {
    title: '操作用户',
    dataIndex: 'operator',
    key: 'operator',
    width: '10%',
    align: 'center'
  },
  {
    title: '设备ID',
    dataIndex: 'mesh_id',
    key: 'mesh_id',
    width: '10%',
    align: 'center'
  },
  {
    title: '设备名称',
    dataIndex: 'mesh_name',
    key: 'mesh_name',
    width: '10%',
    align: 'center'
  },
  {
    title: '设备SN',
    dataIndex: 'mesh_sn',
    key: 'mesh_sn',
    width: '10%',
    align: 'center'
  },
  {
    title: '时间',
    dataIndex: 'occurrence_time',
    key: 'occurrence_time',
    align: 'center',
    width: '10%',
    render: (date) => getFullDate(date)
  },
  {
    title: '访问页面',
    dataIndex: 'url',
    key: 'url',
    width: '15%',
    align: 'center',
    resizable: false,
    ellipsis: true
    /* Text: (val) => { return formatter(val, InNetworkOptions) } */
  },
  {
    title: '命令名称',
    dataIndex: 'cmd_name',
    key: 'cmd_name',
    width: '10%',
    align: 'center'
  },
  {
    title: '命令参数',
    dataIndex: 'cmd_parameter',
    key: 'cmd_parameter',
    align: 'center',
    width: '25%',
    resizable: false,
    ellipsis: true
  }
]
