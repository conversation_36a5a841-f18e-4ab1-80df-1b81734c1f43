<template>
  <div>
    <a-row>
      <a-col :span="24">
        <a-card title="设备信息详情">
          <a-row :gutter="[4, 4]">
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.sn.label" v-model:value="model.sn" />
            </a-col>
            <a-col :span="6">
            <a-input :addonBefore="LabelCaption.wfmode.label" v-model:value="deviceInfoTran.wfModel"/>
              <!-- <a-select
                      ref="select"
                      v-model:value="model.wfmode"
                      style="width: 100%"
                      v-bind:placeholder="LabelCaption.wfmode.label"
                      addon-before="Http://"
                    >
                      <a-select-option
                        v-for="option in WfModeOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select> -->
            </a-col>
             <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.device_no.label" v-model:value="model.device_no" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.device_name.label" v-model:value="model.device_name" />
            </a-col>
            <a-col :span="6">
            <a-input :addonBefore="LabelCaption.device_type.label" v-model:value="deviceInfoTran.device_type"/>
              <!-- <a-select
                      ref="select"
                      v-model:value="model.device_type"
                      style="width: 100%"
                      v-bind:placeholder="LabelCaption.device_type.label"
                    >
                      <a-select-option
                        v-for="option in DeviceTypeOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select> -->
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.net_id.label" v-model:value="model.net_id" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.apversion.label" v-model:value="model.apversion" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.mac_addr.label" v-model:value="model.mac_addr" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.ip_addr.label" v-model:value="model.ip_addr" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.gate_way.label" v-model:value="model.gate_way" />
            </a-col>
            <a-col :span="6">
              <a-input  :addonBefore="LabelCaption.center_freq.label" v-model:value="model.center_freq" />
            </a-col>
            <a-col :span="6">
            <a-input  :addonBefore="LabelCaption.band_width.label" v-model:value="deviceInfoTran.band_width"  />
              <!-- <a-select
                ref="select"
                v-model:value="model.band_width"
                style="width: 100%"
                v-bind:placeholder="LabelCaption.band_width.label"
              >
                <a-select-option
                v-for="option in BandWidthOptions"
                v-bind:key="option.value"
                :value="value">{{ option.name }}</a-select-option>
              </a-select>-->
            </a-col>
            <a-col :span="6">
              <a-input  type="number" :addonBefore="LabelCaption.tx_power.label" v-model:value="model.tx_power" />
            </a-col>
            <a-col :span="6">
            <a-input  :addonBefore="LabelCaption.work_mode.label" v-model:value="deviceInfoTran.work_mode"/>
              <!-- <a-select
                ref="select"
                v-model:value="model.work_mode"
                style="width: 100%"
                v-bind:placeholder="LabelCaption.work_mode.label"
              >
                <a-select-option
                v-for="option in WorkModeOptions"
                v-bind:key="option.value"
                :value="value">{{ option.name }}</a-select-option>
              </a-select> -->
            </a-col>
            <a-col :span="6">
            <a-input :addonBefore="LabelCaption.in_network.label" v-model:value="deviceInfoTran.inNetWork"/>
              <!-- <a-select
                ref="select"
                v-model:value="model.in_network"
                style="width: 100%"
                v-bind:placeholder="LabelCaption.in_network.label"
              >
                <a-select-option
                v-for="option in InNetworkOptions"
                v-bind:key="option.value"
                :value="value">{{ option.name }}</a-select-option>
              </a-select> -->
            </a-col>
            <a-col :span="6">
            <a-input :addonBefore="LabelCaption.distance.label" v-model:value="deviceInfoTran.distance"/>
              <!-- <a-select
                ref="select"
                v-model:value="model.distance"
                style="width: 100%"
                v-bind:placeholder="LabelCaption.distance.label"
              >
                <a-select-option
                v-for="option in DistanceOptions"
                v-bind:key="option.value"
                :value="value">{{ option.name }}</a-select-option>
              </a-select> -->
            </a-col>
            <a-col :span="6">
            <a-input :addonBefore="LabelCaption.hdmi_in.label" v-model:value="deviceInfoTran.hdmi_in"/>
              <!-- <a-select
                ref="select"
                v-model:value="model.hdmi_in"
                style="width: 100%"
                v-bind:placeholder="LabelCaption.hdmi_in.label"
              >
                <a-select-option
                v-for="option in HDMIinOptions"
                v-bind:key="option.value"
                :value="value">{{ option.name }}</a-select-option>
              </a-select> -->
            </a-col>
          </a-row>
          <template #actions>
            <a-row type="flex" justify="end" align="top">
              <a-col :span="4">
               <a-space>
                <a-button type="primary" @click="pageDirectAction.goToIndex()">返回</a-button>
               </a-space>
              </a-col>
            </a-row>
          </template>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { onMounted, defineComponent, ref } from 'vue'
import { message } from 'ant-design-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import {
  BandWidthOptions,
  WorkModeOptions,
  TrunkOptions,
  RoutingHealthIndexOptions,
  InNetworkOptions,
  DistanceOptions,
  HDMIinOptions,
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'
export default defineComponent({
  name: 'Deviceinfo-Detail',
  setup () {
    // 对应后台数据表
    const model = ref(new Deviceinfo())
    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const pageDirectAction = new PageDirectAction()
    // 回调函数错误处理
    const callbackError = () => {
      message.error('数据异常，请重试', 5)
    }
    const deviceInfoTran = ref({
      // 接入模式（wfModel）
      wfModel: '',
      // 设备类型
      device_type: '',
      // 带宽
      band_width: '',
      // 监听模式
      work_mode: '',
      // 在网状态
      inNetWork: '',
      // 通信距离
      distance: '',
      // hdmi接入
      hdmi_in: ''
    })
    // 设备参数与显示转换
    const tranDeviceInfo = () => {
      // 循环变量，用于遍历options
      let i
      // 设备类型
      for (i = 0; i < DeviceTypeOptions.length; i++) {
        if (model.value.device_type === DeviceTypeOptions[i].value) {
          deviceInfoTran.value.device_type = DeviceTypeOptions[i].name
          break
        }
      }
      // 工作模式
      for (i = 0; i < WfModeOptions.length; i++) {
        if (model.value.wfmode === WfModeOptions[i].value) {
          deviceInfoTran.value.wfModel = WfModeOptions[i].name
          break
        }
      }
      // 带宽
      // 遍历option
      for (i = 0; i < BandWidthOptions.length; i++) {
      // 主路带宽
        if (model.value.band_width === BandWidthOptions[i].value) {
          deviceInfoTran.value.band_width = BandWidthOptions[i].name
          break
        }
      }
      // 监听模式
      for (i = 0; i < WorkModeOptions.length; i++) {
        if (model.value.work_mode === WorkModeOptions[i].value) {
          deviceInfoTran.value.work_mode = WorkModeOptions[i].name
          break
        }
      }
      // 在网状态
      for (i = 0; i < InNetworkOptions.length; i++) {
        if (model.value.in_network === InNetworkOptions[i].value) {
          deviceInfoTran.value.inNetWork = InNetworkOptions[i].name
          break
        }
      }
      // 通信距离
      for (i = 0; i < DistanceOptions.length; i++) {
        if (model.value.distance === DistanceOptions[i].value) {
          deviceInfoTran.value.distance = DistanceOptions[i].name
          break
        }
      }
      // hdmi接入
      for (i = 0; i < HDMIinOptions.length; i++) {
        if (model.value.hdmi_in === HDMIinOptions[i].value) {
          deviceInfoTran.value.hdmi_in = HDMIinOptions[i].name
          break
        }
      }
    }
    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model.value = data.data
      }
      // 获取设备数据的时候进行参数显示转换
      tranDeviceInfo()
    }
    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }
    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData.value, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }
    // ****************** 根据主键查询数据 *******************
    onMounted(() => {
      model.value.id = pageDirectAction.getCurrentRouteValue()
      getDeviceinfoById()
    })
    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      BandWidthOptions,
      WorkModeOptions,
      TrunkOptions,
      RoutingHealthIndexOptions,
      InNetworkOptions,
      deviceInfoTran,
      DistanceOptions,
      HDMIinOptions,
      DeviceTypeOptions,
      WfModeOptions,
      DeviceinfoColumns,
      pageDirectAction
    }
  }
})
</script>
<style scoped>
</style>
