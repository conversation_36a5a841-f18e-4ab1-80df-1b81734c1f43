<template>
    <button v-if=!important class="button"> {{button_name}}
    </button>
    <button v-if=important class="button-imp"> {{button_name}}
    </button>
</template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { defineComponent, watch, ref, onMounted } from 'vue'

export default defineComponent({
  props: ['button_name', 'type'],
  setup (props) {
    const dis_button_name = ref('')
    const dis_type = ref('')
    const important = ref(false)
    onMounted(() => {
      watch(() => props.button_name, (newValue, oldValue) => {
        dis_button_name.value = newValue
      })
      watch(() => props.type, (newValue, oldValue) => {
        dis_type.value = newValue

        if (dis_type.value === 'important') {
          important.value = true
        } else {
          important.value = false
        }
      })
    })
    return {
      dis_button_name,
      dis_type,
      important
    }
  }
})
</script>
<style scoped>
/* From Uiverse.io by adamgiebl */
.button {
  position: relative;
  display: inline-block;
  margin: 10px;
  padding: 7px 14px;
  text-align: center;
  font-size: 19px;
  letter-spacing: 2px;
  text-decoration: none;
  color: #00eaff;
  background: transparent;
  cursor: pointer;
  transition: ease-out 0.5s;
  border: 2px solid #00eaff;
  border-radius: 10px;
  box-shadow: inset 0 0 0 0 #0c458a;
}

.button:hover {
  color: white;
  box-shadow: inset 0 -100px 0 0 #0a366d;
}

.button:active {
  transform: scale(0.9);
}

.button-imp{
  position: relative;
  display: inline-block;
  margin: 10px;
  padding: 7px 14px;
  text-align: center;
  font-size: 19px;
  letter-spacing: 2px;
  text-decoration: none;
  color: #ff0000;
  background: transparent;
  cursor: pointer;
  transition: ease-out 0.5s;
  border: 2px solid #ff0000;
  border-radius: 10px;
  box-shadow: inset 0 0 0 0 #ff4000;
}

.button-imp:hover {
  color: white;
  box-shadow: inset 0 -100px 0 0 #ff0000;
}

.button-imp:active {
  transform: scale(0.9);
}
</style>
