import baseRequest from '@/request/request'
class RtmpRequestAction {
  constructor () {
    this.BASE_API_URL = '/rtmp'
    this.urlStop = `${this.BASE_API_URL}/stop`
    this.urlquery = `${this.BASE_API_URL}/query`
    this.urltouch = `${this.BASE_API_URL}/touch`
  }

  /**
   * 设置
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   stop = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlStop, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlquery, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 心跳
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
       touch = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
         baseRequest
           .post(this.urltouch, baseRequestData)
           .then(response => {
             if (typeof successCallback === 'function') {
               successCallback(response.data)
             }
           })
           .catch(error => {
             if (typeof errorCallback === 'function') {
               errorCallback(error)
             }
           })
           .finally(() => {
             if (typeof finalllyCallback === 'function') {
               finalllyCallback()
             }
           })
       }
}

export { RtmpRequestAction }
