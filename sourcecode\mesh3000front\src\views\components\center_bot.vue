<template>
    <dv-loading v-if="showLoading"></dv-loading>
    <div :v-if = "! showLoading" id="main4" style="width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0); position: relative;"></div>
  </template>

<script>
import { defineComponent, onMounted, watch, ref } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['powerInfo'],
  setup (props) {
    const dataZoomMove = {
      start: 0,
      end: 4
    }

    const showLoading = ref(false)

    const dataSourceX = ref([])
    const dataSourceY = ref([])
    const dataSourceY2 = [
      {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }, {
        value: 100
      }]
    const option = {
      dataZoom: [
        {
          show: false, // 为true 滚动条出现
          realtime: true,
          type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
          startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
          endValue: dataZoomMove.end,
          width: 6,
          right: '20',
          top: '20%', // 位置和grid配置注意下
          height: '56%',
          xAxisIndex: [1, 0], // 关联多个y轴
          moveHandleStyle: {
            color: 'rgba(89, 202, 241,.5)'
          },
          moveHandleSize: '6',
          emphasis: {
            moveHandleStyle: {
              color: 'rgba(89, 202, 241,.5)'
            }
          },
          textStyle: {
            color: 'rgba(255,255,255,0)'
          },
          backgroundColor: 'rgba(255,255,255,.1)',
          borderColor: 'rgba(255,255,255,0)',
          fillerColor: 'rgba(0,0,0,0)',
          handleSize: '6',
          handleStyle: {
            color: 'rgba(255,255,255,0)'
          },
          brushStyle: {
            color: 'rgba(129, 243, 253)'
          }
        },
        { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
          type: 'inside',
          xAxisIndex: 0,
          zoomOnMouseWheel: false, // 滚轮是否触发缩放
          moveOnMouseMove: true, // 鼠标滚轮触发滚动
          moveOnMouseWheel: true
        }
      ],
      backgroundColor: '#202632',
      legend: {
        icon: 'circle',
        color: '#434857',
        data: [],
        itemGap: 24,
        top: 0
      },
      grid: {
        top: '3%',
        left: '2%',
        bottom: '4%',
        right: '0%',
        containLabel: true
      },
      xAxis: {
        show: true,
        data: dataSourceX.value,
        axisLabel: {
          fontSize: '15px',
          color: '#fff',
          padding: [0, 10, 0, 0]
        }
      },
      yAxis: {
        show: true,
        max: 100,
        min: 0,
        interval: 10,
        axisLine: {
          lineStyle: {
            color: '#434857'
          }
        },
        axisTick: {
          length: 10
        },
        axisLabel: {
          fontSize: '10px',
          color: '#6B717D',
          formatter: '{value} %',
          padding: [0, 10, 0, 0]
        },
        axisPointer: {
          show: true
        },
        splitLine: {
          show: false
        }
      },
      animationEasing: 'elasticOut',
      series: [{
        name: '正常',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolRepeat: true,
        symbolSize: [60, 8],
        symbolMargin: 4,
        itemStyle: {
          normal: {
            color: {
              colorStops: [{
                offset: 0, color: '#43F2A6' // 0% 处的颜色
              }, {
                offset: 1, color: '#15CF7E' // 100% 处的颜色
              }]
            }
          }

        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          formatter: '{c}%'
        },
        data: dataSourceY.value
      },
      {
        name: '异常',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolRepeat: true,
        symbolSize: [60, 8],
        symbolMargin: 4,
        color: '#12121E',
        barGap: '-100%',
        z: 1,
        data: dataSourceY2
      }]
    }
    onMounted(() => {
      const drawChart = () => {
        option.value = {
          dataZoom: [
            {
              show: false, // 为true 滚动条出现
              realtime: true,
              type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
              startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
              endValue: dataZoomMove.end,
              width: 6,
              right: '20',
              top: '20%', // 位置和grid配置注意下
              height: '56%',
              xAxisIndex: [1, 0], // 关联多个y轴
              moveHandleStyle: {
                color: 'rgba(89, 202, 241,.5)'
              },
              moveHandleSize: '6',
              emphasis: {
                moveHandleStyle: {
                  color: 'rgba(89, 202, 241,.5)'
                }
              },
              textStyle: {
                color: 'rgba(255,255,255,0)'
              },
              backgroundColor: 'rgba(255,255,255,.1)',
              borderColor: 'rgba(255,255,255,0)',
              fillerColor: 'rgba(0,0,0,0)',
              handleSize: '6',
              handleStyle: {
                color: 'rgba(255,255,255,0)'
              },
              brushStyle: {
                color: 'rgba(129, 243, 253)'
              }
            },
            { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
              type: 'inside',
              xAxisIndex: 0,
              zoomOnMouseWheel: false, // 滚轮是否触发缩放
              moveOnMouseMove: true, // 鼠标滚轮触发滚动
              moveOnMouseWheel: true
            }
          ],
          backgroundColor: '#202632',
          legend: {
            icon: 'circle',
            color: '#434857',
            data: [],
            itemGap: 24,
            top: 0
          },
          grid: {
            top: '3%',
            left: '2%',
            bottom: '4%',
            right: '0%',
            containLabel: true
          },
          xAxis: {
            show: true,
            data: dataSourceX.value,
            axisLabel: {
              fontSize: '15px',
              color: '#fff',
              padding: [0, 10, 0, 0]
            }
          },
          yAxis: {
            show: true,
            max: 100,
            min: 0,
            interval: 10,
            axisLine: {
              lineStyle: {
                color: '#434857'
              }
            },
            axisTick: {
              length: 10
            },
            axisLabel: {
              fontSize: '10px',
              color: '#6B717D',
              formatter: '{value} %',
              padding: [0, 10, 0, 0]
            },
            axisPointer: {
              show: true
            },
            splitLine: {
              show: false
            }
          },
          animationEasing: 'elasticOut',
          series: [{
            name: '正常',
            type: 'pictorialBar',
            symbol: 'rect',
            symbolRepeat: true,
            symbolSize: [60, 8],
            symbolMargin: 4,
            itemStyle: {
              normal: {
                color: {
                  colorStops: [{
                    offset: 0, color: '#43F2A6' // 0% 处的颜色
                  }, {
                    offset: 1, color: '#15CF7E' // 100% 处的颜色
                  }]
                }
              }

            },
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              formatter: '{c}%'
            },
            data: dataSourceY.value
          },
          {
            name: '异常',
            type: 'pictorialBar',
            symbol: 'rect',
            symbolRepeat: true,
            symbolSize: [60, 8],
            symbolMargin: 4,
            color: '#12121E',
            barGap: '-100%',
            z: 1,
            data: dataSourceY2
          }]
        }
      }
      const chartDom2 = document.getElementById('main4')
      const myChart2 = echarts.init(chartDom2, 'dark')
      // 初始化图表
      myChart2.setOption(option)

      // 监听 props.powerInfo 的变化，当 props.powerInfo 发生变化时，更新柱状图数据
      watch(() => props.powerInfo, (newValue, oldValue) => {
        showLoading.value = (newValue.x.length === 0) || (newValue.y.length === 0)

        // 在这里根据新的 props.a 值更新柱状图数据
        dataSourceX.value = newValue.x
        dataSourceY.value = newValue.y
        drawChart()
        // 更新图表配置
        myChart2.setOption(option.value)
      })

      // 自动轮播和鼠标移入移出的停止和开启
      let dataZoomMoveTimer = null
      const startMoveDataZoom = (myChart2, dataZoomMove) => {
        dataZoomMoveTimer = setInterval(() => {
          dataZoomMove.start += 1
          dataZoomMove.end += 1
          if (dataZoomMove.end > dataSourceX.value.length - 1) {
            dataZoomMove.start = 0
            dataZoomMove.end = 4
          }
          myChart2.setOption({
            dataZoom: [
              {
                type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
                startValue: dataZoomMove.start,
                endValue: dataZoomMove.end
              }
            ]
          })
        }, 1000)
      }
      startMoveDataZoom(myChart2, dataZoomMove)
      const chartDom = myChart2.getDom()
      chartDom.addEventListener('mouseout', () => {
        if (dataZoomMoveTimer) return
        const dataZoomMove_get = myChart2.getOption().dataZoom[0]
        dataZoomMove.start = dataZoomMove_get.startValue
        dataZoomMove.end = dataZoomMove_get.endValue
        startMoveDataZoom(myChart2, dataZoomMove)
      })
      // 移入
      // myChart.on
      chartDom.addEventListener('mouseover', () => {
        clearInterval(dataZoomMoveTimer)
        dataZoomMoveTimer = undefined
      })
    })
    return {
      showLoading
    }
  }
})
</script>
