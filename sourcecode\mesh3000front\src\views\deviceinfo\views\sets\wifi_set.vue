<template>
  <a-card title="WIFI设置">
    <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
      <a-row justify="center">
        <a-form-item>
          <a-radio-group v-model:value="model.configData.WifiSet" @change="onchange">
            <a-radio :value="0">关闭wifi</a-radio>
            <a-radio :value="1">开启并设置工作模式为AP</a-radio>
            <a-radio :value="2">开启并设置工作模式为client</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-row>

      <a-row justify="center">
        <a-col :span="6">
          <a-form-item :rules="[{ required: model.configData.WifiSet , message: 'SSID不能为空' }]" :name="['configData', 'Ssid']" :label="LabelCaption.wifi_ssid.label">
            <a-input v-model:value="model.configData.Ssid" :disabled="(model.configData.WifiSet == 0)?true:false" style="width:200px;margin-left:50px" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row justify="center">
        <a-col :span="6">
          <a-form-item :rules="[{ required: model.configData.WifiSet , message: '密码不能为空' }]" :name="['configData', 'Keys']" :label="LabelCaption.wifi_keys.label">
            <a-input-password v-model:value="model.configData.Keys" :disabled="(model.configData.WifiSet == 0)?true:false" style="width:200px;margin-left:50px" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row justify="center">
        <a-col :span="6">
          <a-form-item :rules="[{ required: model.configData.WifiSet , message: '信道不能为空' }]" :name="['configData', 'Channel']" :label="LabelCaption.wifi_channel.label">
            <a-input enabled v-model:value="model.configData.Channel" :disabled="(model.configData.WifiSet == 0)?true:false" style="width:200px;margin-left:18px" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider />
      <a-row justify="center">
        <a-col :span="2">
          <a-form-item>
            <!-- <a-button type="primary" html-type="submit" v-if="AuthUi.WiFi.status">保存</a-button> -->
            <a-button type="primary" html-type="submit" v-if="AuthUi.WiFi.statusW">保存</a-button>
          </a-form-item>
        </a-col>
        <a-col :span="2">
          <a-form-item>
            <a-button type="primary" @click="getWifi">刷新</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-card>
</template>

<script>
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { WifiRequestAction } from '@/views/deviceinfo/action/wifiRequestAction'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { Wifi } from '@/views/deviceinfo/model/Wifi'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { ErrorInfo } from '@/common/errorInfo'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

export default defineComponent({
  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const wifi = new Wifi()
    model.configData = wifi
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const wifiRequestAction = new WifiRequestAction()
    const pageDirectAction = new PageDirectAction()
    const wifi_set_value = ref(2)

    // 单选按钮变化
    const onchange = e => {
      switch (e.target.value) {
        case 0: {
          model.configData.Ssid = null
          model.configData.Keys = null
          model.configData.Channel = null
          // Element('#ssid').attr('disabled', true)
          break
        }
      }
    }

    // 回调函数错误处理
    const callbackError = error => {
      message.error(error.message, 3)
    }

    const set = () => {
      // 如果选择wifi模式为关闭模式则给定ssid和key为0(不能为空)
      // if(model.configData.WifiSet==0){
      //     model.configData.Ssid = 0
      //     model.configData.Keys = 0
      // }
      wifiRequestAction.set(baseRequestData, setSuccess, callbackError, null)
      getWifi()
    }

    const setSuccess = data => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('wifi信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置wifi信息失败!${data.error_code}`)
      }
    }

    const confirm = record => {
      /* model.configData.Channel = parseInt(model.configData.Channel, '') */
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const getWifi = () => {
      wifiRequestAction.query(baseRequestData, getWifiSuccess, callbackError, null)
    }

    const getWifiSuccess = data => {
      model.configData = data.data.configData
      console.info(model.configData)
    }

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getWifi()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      wifi_set_value,
      wifi,
      AuthUi,
      getWifi,
      confirm,
      onchange
    }
  }
})
</script>
