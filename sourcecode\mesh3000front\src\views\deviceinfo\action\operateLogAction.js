import baseRequest from '@/request/request'
class OperateLogInfoAction {
  constructor () {
    this.BASE_API_URL = '/meshoperatelog'
    this.urlGetOne = `${this.BASE_API_URL}/get/getOne`
    this.urlGetList = `${this.BASE_API_URL}/get/getList`
  }

  /**
   * 设置
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   set = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlSet, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    getList = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      baseRequest
        .post(this.urlGetList, baseRequestData)
        .then(response => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }
}

export { OperateLogInfoAction }
