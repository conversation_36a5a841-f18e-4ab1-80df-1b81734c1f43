import baseRequest from '@/request/request'
class FsAction {
  constructor () {
    this.BASE_API_URL = '/fs_interface'
    // 用户管理
    this.urlUserMod = `${this.BASE_API_URL}/user_mod`
    this.urlUserDel = `${this.BASE_API_URL}/user_del`
    this.urlUserQuery = `${this.BASE_API_URL}/user_query`
    this.urlAllUserQuery = `${this.BASE_API_URL}/alluser_query`
    // 部门管理
    this.urlDeptAdd = `${this.BASE_API_URL}/dept_add`
    this.urlDeptMod = `${this.BASE_API_URL}/dept_mod`
    this.urlDeptDel = `${this.BASE_API_URL}/dept_del`
    this.urlDeptQuery = `${this.BASE_API_URL}/dept_query`
    this.urlDeptUserOperate = `${this.BASE_API_URL}/deptUser_operate`
    // 通话调度
    this.urlDial = `${this.BASE_API_URL}/dial` // 呼叫
    this.urlDialUpQueue = `${this.BASE_API_URL}/dial_up_queue` // 接听队列
    this.urlGroupAnswerQueue = `${this.BASE_API_URL}/group_answer_queue` // 群答
    this.urlDialUp = `${this.BASE_API_URL}/dial_up`
    this.urlDialOff = `${this.BASE_API_URL}/dial_off` // 挂断
    this.urlDialHold = `${this.BASE_API_URL}/dial_hold`
    this.urlDialContinue = `${this.BASE_API_URL}/dial_continue`
    this.urlGetDialQueue = `${this.BASE_API_URL}/getDialQueue`
    this.urlDialRecord = `${this.BASE_API_URL}/dial_record`
    this.urlTransDial = `${this.BASE_API_URL}/trans_dial`
    this.urlInsertDial = `${this.BASE_API_URL}/insert_dial`
    this.urlSplitDial = `${this.BASE_API_URL}/split_dial`
    this.urlRemoveDial = `${this.BASE_API_URL}/remove_dial`
    this.urlOffDial = `${this.BASE_API_URL}/off_dial`
    this.urlListenDial = `${this.BASE_API_URL}/listen_dial`
    this.urlInterceptAnswer = `${this.BASE_API_URL}/intercept_answer`
    this.urlTransfer = `${this.BASE_API_URL}/transfer`
    this.urlSmsSend = `${this.BASE_API_URL}/sms_send`
    this.urlData_call = `${this.BASE_API_URL}/data_call`
    this.urlData_call = `${this.BASE_API_URL}/data_sms`
    this.urlQueryHis = `${this.BASE_API_URL}/queryHis`
    // 会议
    this.urlStartMeeting = `${this.BASE_API_URL}/start_meeting` // 会议发起
    this.urlQueryMeeting = `${this.BASE_API_URL}/conference_list` // 查询当前会议
    this.urlQueryHisMeeting = `${this.BASE_API_URL}/his_conference_list` // 查询历史会议
    this.urlConferenceStop = `${this.BASE_API_URL}/conference_stop` // 会议结束
    this.urlMeetOperate = `${this.BASE_API_URL}/meet_operate` // 会议操作
    this.urlConferenceMute = `${this.BASE_API_URL}/conference_mute` // 禁言操作
    this.urlConferenceKick = `${this.BASE_API_URL}/conference_kick` // 踢人操作
    this.urlConferenceIsolate = `${this.BASE_API_URL}/conference_isolate` // 隔离操作
    this.urlConferenceBroadcast = `${this.BASE_API_URL}/conference_broadcast` // 广播操作
    this.urlBroadcastFileQuery = `${this.BASE_API_URL}/broadcast_file_query` // 广播文件查询
    this.urlBroadcastFileDel = `${this.BASE_API_URL}/broadcast_file_del` // 广播文件删除

    // 广播
    this.urlconferenceListBroadcast = `${this.BASE_API_URL}/conference_list_broadcast` // 获取广播列表
    this.urlconferenceBroadcastDirect = `${this.BASE_API_URL}//conference_broadcast_direct` // 创建广播
  }

  /**
   * 获取用户列表响应
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  dept_query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDeptQuery, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  deptUser_operate = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDeptUserOperate, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  user_add = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlUserAdd, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  user_mod = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlUserMod, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  user_del = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlUserDel, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  user_query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlUserQuery, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  alluser_query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlAllUserQuery, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getDialQueue = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetDialQueue, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  dial = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDial, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

    /**
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    dial_off = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      baseRequest
        .post(this.urlDialOff, baseRequestData)
        .then(response => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }

        /**
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @description 代答
   * @returns
   */
        intercept_answer = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
          baseRequest
            .post(this.urlAnswering, baseRequestData)
            .then(response => {
              if (typeof successCallback === 'function') {
                successCallback(response.data)
              }
            })
            .catch(error => {
              if (typeof errorCallback === 'function') {
                errorCallback(error)
              }
            })
            .finally(() => {
              if (typeof finalllyCallback === 'function') {
                finalllyCallback()
              }
            })
        }

  /**
   * 接听队列的接听请求
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  dial_up_queue = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDialUpQueue, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

    /**
   * 接听队列的群答请求
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    group_answer_queue = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      baseRequest
        .post(this.urlGroupAnswerQueue, baseRequestData)
        .then(response => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }

  listen_dial = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlListenDial, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  trans_dial = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlTransDial, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  insert_dial = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlInsertDial, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  split_dial = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlSplitDial, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  intercept_answer = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlInterceptAnswer, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  queryHis = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlQueryHis, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  get_config = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetConfig, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  start_meeting = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlStartMeeting, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_stop = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlConferenceStop, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_list = (baseRequestData, successCallback, errorCallback, finalllyCallback, para) => {
    baseRequest
      .post(this.urlQueryMeeting, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data, para)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  his_conference_list = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlQueryHisMeeting, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  meet_operate = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlMeetOperate, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_mute = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlConferenceMute, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_kick = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlConferenceKick, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_isolate = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlConferenceIsolate, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_broadcast = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlConferenceBroadcast, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  broadcast_file_query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlBroadcastFileQuery, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  broadcast_file_del = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlBroadcastFileDel, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_list_broadcast = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlconferenceListBroadcast, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  conference_broadcast_direct = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlconferenceBroadcastDirect, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }
}

export { FsAction }
