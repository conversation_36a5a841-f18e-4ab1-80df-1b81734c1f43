<template>
    <dv-loading v-if="showLoading"></dv-loading>
    <div  :v-if = "! showLoading" id="main3" style="width: 99%; height: 99%; background-color: rgba(255, 255, 255, 0); position: relative;"></div>
  </template>

<script>
import { defineComponent, onMounted, watch, ref } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['temperature'],
  setup (props) {
    const dataZoomMove = {
      start: 0,
      end: 4
    }

    const dataSouce = ref([])
    const dataSouceX = ref([])
    const option = {
      dataZoom: [
        {
          show: false, // 为true 滚动条出现
          realtime: true,
          type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
          startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
          endValue: dataZoomMove.end,
          width: 6,
          right: '20',
          top: '20%', // 位置和grid配置注意下
          height: '56%',
          xAxisIndex: [1, 0], // 关联多个y轴
          moveHandleStyle: {
            color: 'rgba(89, 202, 241,.5)'
          },
          moveHandleSize: '6',
          emphasis: {
            moveHandleStyle: {
              color: 'rgba(89, 202, 241,.5)'
            }
          },
          textStyle: {
            color: 'rgba(255,255,255,0)'
          },
          //   backgroundColor: 'rgba(255,255,255,.1)',
          borderColor: 'rgba(255,255,255,0)',
          fillerColor: 'rgba(0,0,0,0)',
          handleSize: '6',
          handleStyle: {
            color: 'rgba(255,255,255,0)'
          },
          brushStyle: {
            color: 'rgba(129, 243, 253)'
          }
        },
        { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
          type: 'inside',
          xAxisIndex: 0,
          zoomOnMouseWheel: false, // 滚轮是否触发缩放
          moveOnMouseMove: true, // 鼠标滚轮触发滚动
          moveOnMouseWheel: true
        }
      ],
      backgroundColor: '',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,.6)',
        borderColor: 'rgba(147, 235, 248, 0)',
        textStyle: {
          color: '#FFF'
        }
        // axisPointer: {
        // 	type: "shadow",
        // 	label: {
        // 		show: true,
        // 	},
        // },
      },
      grid: {
        left: '10%',
        top: '18%',
        right: '5%',
        bottom: '25%'
      },
      legend: {
        top: '4%',
        left: '75%',
        itemWidth: 13,
        itemHeight: 13,
        itemStyle: {
          color: '#18A4FF'
        },
        icon: 'rect',
        padding: 0,
        textStyle: {
          color: '#c0c3cd',
          fontSize: 13,
          padding: [2, 0, 0, 0]
        }
      },
      xAxis: {
        data: dataSouceX.value,

        axisLine: {
          show: true, // 隐藏X轴轴线
          lineStyle: {
            color: '#163a5f',
            width: 2
          }
        },
        axisTick: {
          show: false, // 隐藏X轴刻度
          alignWithLabel: true
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#BDD8FB', // X轴文字颜色
            fontSize: 12
          },
          interval: 0,
          formatter: function (value) {
            let ret = '' // 拼接加\n返回的类目项
            const maxLength = 4 // 每项显示文字个数
            const valLength = value.length // X轴类目项的文字个数
            const rowN = Math.ceil(valLength / maxLength) // 类目项需要换行的行数
            if (rowN > 1) {
              // 如果类目项的文字大于5,
              for (let i = 0; i < rowN; i++) {
                let temp = '' // 每次截取的字符串
                const start = i * maxLength // 开始截取的位置
                const end = start + maxLength // 结束截取的位置
                // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                temp = value.substring(start, end) + '\n'
                ret += temp // 凭借最终的字符串
              }
              return ret
            } else {
              return value
            }
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '单位：℃',
          nameTextStyle: {
            color: '#BDD8FB',
            fontSize: 12
          },

          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed' // dotted 虚线
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: true, // 隐藏X轴轴线
            lineStyle: {
              color: '#163a5f',
              width: 1
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#BDD8FB',
              fontSize: 12
            }
          }
        },
        {
          type: 'value',
          name: '',
          nameTextStyle: {
            color: '#BDD8FB',
            fontSize: 12
          },
          splitLine: {
            show: false,
            lineStyle: {
              width: 1,
              color: '#CED2DB'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false, // 隐藏X轴轴线
            lineStyle: {
              color: '#163a5f',
              width: 2
            }
          },
          axisLabel: {
            show: false,
            textStyle: {
              color: '#797A7F',
              fontSize: 12
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: 15,
          itemStyle: {
            // color: new graphic.LinearGradient(0, 0, 0, 1, [
            //    {
            //       offset: 0,
            //       color: "#00A2FF",
            //    },
            //    {
            //       offset: 1,
            //       color: "#00CCD2",
            //    },
            // ]),
            color: {
              type: 'linear',
              x: 0, // 右
              y: 0, // 下
              x2: 0, // 左
              y2: 1, // 上
              colorStops: [
                {
                  offset: 0.1,
                  color: '#13D5FC' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#2059B8' // 100% 处的颜色
                }
              ]
            },
            barBorderRadius: [20, 20, 20, 20]
          },
          label: {
            show: true,
            position: 'top',
            distance: 0,
            color: '#1ACDDC',
            formatter: '{c}' + '℃'
          },
          data: dataSouce.value
        },
        {
          // name: '背景',
          type: 'bar',
          barWidth: '15px',
          xAxisIndex: 0,
          yAxisIndex: 1,
          barGap: '-110%',
          data: [100, 100, 100, 100, 100, 100, 100], // 背景阴影长度
          itemStyle: {
            normal: {
              color: 'rgba(255,255,255,0.039)',
              barBorderRadius: [20, 20, 20, 20]
            }
          },
          tooltip: {
            show: false
          },
          zlevel: 9
        }
      ]
    }
    const showLoading = ref(false)

    onMounted(() => {
      const drawChart = () => {
        option.value = {
          dataZoom: [
            {
              show: false, // 为true 滚动条出现
              realtime: true,
              type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
              startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
              endValue: dataZoomMove.end,
              width: 6,
              right: '20',
              top: '20%', // 位置和grid配置注意下
              height: '56%',
              xAxisIndex: [1, 0], // 关联多个y轴
              moveHandleStyle: {
                color: 'rgba(89, 202, 241,.5)'
              },
              moveHandleSize: '6',
              emphasis: {
                moveHandleStyle: {
                  color: 'rgba(89, 202, 241,.5)'
                }
              },
              textStyle: {
                color: 'rgba(255,255,255,0)'
              },
              backgroundColor: 'rgba(255,255,255,.1)',
              borderColor: 'rgba(255,255,255,0)',
              fillerColor: 'rgba(0,0,0,0)',
              handleSize: '6',
              handleStyle: {
                color: 'rgba(255,255,255,0)'
              },
              brushStyle: {
                color: 'rgba(129, 243, 253)'
              }
            },
            { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
              type: 'inside',
              xAxisIndex: 0,
              zoomOnMouseWheel: false, // 滚轮是否触发缩放
              moveOnMouseMove: true, // 鼠标滚轮触发滚动
              moveOnMouseWheel: true
            }
          ],
          //   backgroundColor: '#03213D',
          tooltip: {
            trigger: 'axis',
            // backgroundColor: 'rgba(0,0,0,.6)',
            borderColor: 'rgba(147, 235, 248, 0)',
            textStyle: {
              color: '#FFF'
            }
            // axisPointer: {
            // 	type: "shadow",
            // 	label: {
            // 		show: true,
            // 	},
            // },
          },
          grid: {
            left: '10%',
            top: '18%',
            right: '5%',
            bottom: '25%'
          },
          legend: {
            top: '4%',
            left: '75%',
            itemWidth: 13,
            itemHeight: 13,
            itemStyle: {
              color: '#18A4FF'
            },
            icon: 'rect',
            padding: 0,
            textStyle: {
              color: '#c0c3cd',
              fontSize: 13,
              padding: [2, 0, 0, 0]
            }
          },
          xAxis: {
            data: dataSouceX.value,

            axisLine: {
              show: true, // 隐藏X轴轴线
              lineStyle: {
                color: '#163a5f',
                width: 2
              }
            },
            axisTick: {
              show: false, // 隐藏X轴刻度
              alignWithLabel: true
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#BDD8FB', // X轴文字颜色
                fontSize: 12
              },
              interval: 0,
              formatter: function (value) {
                let ret = '' // 拼接加\n返回的类目项
                const maxLength = 4 // 每项显示文字个数
                const valLength = value.length // X轴类目项的文字个数
                const rowN = Math.ceil(valLength / maxLength) // 类目项需要换行的行数
                if (rowN > 1) {
                  // 如果类目项的文字大于5,
                  for (let i = 0; i < rowN; i++) {
                    let temp = '' // 每次截取的字符串
                    const start = i * maxLength // 开始截取的位置
                    const end = start + maxLength // 结束截取的位置
                    // 这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                    temp = value.substring(start, end) + '\n'
                    ret += temp // 凭借最终的字符串
                  }
                  return ret
                } else {
                  return value
                }
              }
            }
          },
          yAxis: [
            {
              type: 'value',
              name: '单位：℃',
              nameTextStyle: {
                color: '#BDD8FB',
                fontSize: 12
              },

              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.15)',
                  type: 'dashed' // dotted 虚线
                }
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: true, // 隐藏X轴轴线
                lineStyle: {
                  color: '#163a5f',
                  width: 1
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#BDD8FB',
                  fontSize: 12
                }
              }
            },
            {
              type: 'value',
              name: '',
              nameTextStyle: {
                color: '#BDD8FB',
                fontSize: 12
              },
              splitLine: {
                show: false,
                lineStyle: {
                  width: 1,
                  color: '#CED2DB'
                }
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false, // 隐藏X轴轴线
                lineStyle: {
                  color: '#163a5f',
                  width: 2
                }
              },
              axisLabel: {
                show: false,
                textStyle: {
                  color: '#797A7F',
                  fontSize: 12
                }
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 15,
              itemStyle: {
                // color: new graphic.LinearGradient(0, 0, 0, 1, [
                //    {
                //       offset: 0,
                //       color: "#00A2FF",
                //    },
                //    {
                //       offset: 1,
                //       color: "#00CCD2",
                //    },
                // ]),
                color: {
                  type: 'linear',
                  x: 0, // 右
                  y: 0, // 下
                  x2: 0, // 左
                  y2: 1, // 上
                  colorStops: [
                    {
                      offset: 0.1,
                      color: '#13D5FC' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#2059B8' // 100% 处的颜色
                    }
                  ]
                },
                barBorderRadius: [20, 20, 20, 20]
              },
              label: {
                show: true,
                position: 'top',
                distance: 0,
                color: '#1ACDDC',
                formatter: '{c}' + '℃'
              },
              data: dataSouce.value
            },
            {
              // name: '背景',
              type: 'bar',
              barWidth: '15px',
              xAxisIndex: 0,
              yAxisIndex: 1,
              barGap: '-110%',
              data: [100, 100, 100, 100, 100, 100, 100], // 背景阴影长度
              itemStyle: {
                normal: {
                  color: 'rgba(255,255,255,0.039)',
                  barBorderRadius: [20, 20, 20, 20]
                }
              },
              tooltip: {
                show: false
              },
              zlevel: 9
            }
          ]
        }
      }

      const chartDom2 = document.getElementById('main3')
      const myChart2 = echarts.init(chartDom2, 'dark')

      // 初始化图表
      myChart2.setOption(option)

      // 监听 props.temperature 的变化，当 props.temperature 发生变化时，更新柱状图数据
      watch(() => props.temperature, (newValue, oldValue) => {
        // alert(123)
        // 在这里根据新的 props.a 值更新柱状图数据

        showLoading.value = (newValue.x.length === 0) || (newValue.x.length === 0)
        dataSouce.value = newValue.y
        dataSouceX.value = newValue.x
        drawChart()

        // 更新图表配置
        myChart2.setOption(option.value)
      })

      // 自动轮播和鼠标移入移出的停止和开启
      let dataZoomMoveTimer = null
      const startMoveDataZoom = (myChart, dataZoomMove) => {
        dataZoomMoveTimer = setInterval(() => {
          dataZoomMove.start += 1
          dataZoomMove.end += 1
          if (dataZoomMove.end > dataSouce.value.length - 1) {
            dataZoomMove.start = 0
            dataZoomMove.end = 4
          }
          myChart.setOption({
            dataZoom: [
              {
                type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
                startValue: dataZoomMove.start,
                endValue: dataZoomMove.end
              }
            ]
          })
        }, 1000)
      }
      startMoveDataZoom(myChart2, dataZoomMove)
      const chartDom = myChart2.getDom()
      chartDom.addEventListener('mouseout', () => {
        if (dataZoomMoveTimer) return
        const dataZoomMove_get = myChart2.getOption().dataZoom[0]
        dataZoomMove.start = dataZoomMove_get.startValue
        dataZoomMove.end = dataZoomMove_get.endValue
        startMoveDataZoom(myChart2, dataZoomMove)
      })
      // 移入
      // myChart.on
      chartDom.addEventListener('mouseover', () => {
        clearInterval(dataZoomMoveTimer)
        dataZoomMoveTimer = undefined
      })
    })
    return {
      showLoading
    }
  }
})
</script>
