<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible>
      <!-- <div class="logo" style="" /> -->
      <a-image :preview="false" :width="200" :height="60" :src="ico_logo" />
      <a-menu  v-model:selectedKeys="selectedKeys" theme="dark" mode="inline">

        <a-menu-item key="9">
          <BarChart-outlined/>
          <span @click="goToDeviceView">大屏概览</span>
        </a-menu-item>

        <a-menu-item key="10">
          <global-outlined />
          <span @click="goToDeviceMap">设备概览</span>
        </a-menu-item>

        <a-sub-menu key="sub1">
          <template #title>
            <span>
              <code-sandbox-outlined />
              <span>设备管理</span>
            </span>
          </template>
          <a-menu-item key="2" @click="goToTopoIndex" v-if="AuthUi.网络拓扑.vis">
            <gateway-outlined />
            <span>网络拓扑</span>
          </a-menu-item>
          <a-menu-item key="5" @click="goToSweepIndex" v-if="AuthUi.扫频.vis">
            <lineChart-outlined />
            <span>扫频</span>
          </a-menu-item>
          <a-menu-item key="3" @click="goToDeviceIndex" v-if="AuthUi.设备维护.vis">
            <tool-outlined />
            <span>设备维护</span>
            </a-menu-item>
          <a-menu-item key="4" @click="goToDevicelogIndex" v-if="AuthUi.设备日志.vis">
            <reconciliation-outlined />
            <span>设备日志</span>
          </a-menu-item>
          <a-menu-item key="14" @click="goToOperatelogIndex" v-if="AuthUi.设备日志.vis">
            <reconciliation-outlined />
            <span>操作日志</span>
          </a-menu-item>
        </a-sub-menu>

        <a-menu-item key="11" v-if="AuthUi.视频播放.vis">
            <playSquare-outlined />
          <span @click="goToVideoPlay">视频播放</span>
        </a-menu-item>

        <a-menu-item key="12" v-if="AuthUi.海康视频播放.vis">
          <playSquare-outlined />
          <span @click="gotoHkVideo">视频播放（海康）</span>
        </a-menu-item>

        <a-menu-item key="6"  v-if="AuthUi.设备日志.vis">
          <VideoCameraOutlined />
          <span @click="goToMeetIndex">视频会议</span>
        </a-menu-item>

        <a-menu-item key="7"  v-if="AuthUi.设备日志.vis">
          <VideoCameraOutlined />
          <span @click="goToFsIndex">通话调度</span>
        </a-menu-item>

        <a-menu-item key="13" v-if="AuthUi.系统设置.vis">
          <setting-outlined />
          <span @click="goToSystemInfo">系统设置</span>
        </a-menu-item>

        <a-menu-item key="15" v-if="AuthUi.系统设置.vis">
          <setting-outlined />
          <span @click="goToSeaMap">海图定位</span>
        </a-menu-item>

      </a-menu>
    </a-layout-sider>

    <a-layout>
      <a-layout-header style="padding: 0">

        <!--   <menu-unfold-outlined -->
        <!--   v-if="collapsed" -->
        <!--   class="trigger" -->
        <!--   @click="() => (collapsed = !collapsed)" -->
        <!-- /> -->
        <!-- <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" /> -->

        <!-- <div id="components-dropdown-demo-placement"> -->

        <!-- <a-button v-if="switchShowDrawer" type="primary" @click="showDrawer">设备列表</a-button> -->
        <!-- <a-button v-if="switchShowVideoDrawer" type="primary" @click="showVideoDrawer">视频列表</a-button> -->

        <a-row justify="space-between" style="background-color: #162130;">
          <a-col offset="1">
            <templet>
              <a-typography-title style="margin-top: 1%;">{{sysname}}</a-typography-title>
            </templet>
          </a-col>
          <!-- <a-col span="6" offset="2"></a-col> -->

          <!-- <a-row justify="end"> -->
          <!-- <a-col span="7"> -->
          <!--   <a-typography-title>MESH3000设备管理平台</a-typography-title> -->
          <!-- </a-col> -->
          <!-- <a-col span="5" offset="2">主板温度:{{wd}}  电池电量{{sy}}</a-col> -->
          <a-col span="2" offset="4">

            <a-dropdown-button type="primary" size="large" :style="{ position:'absolute', top:'0px', right:'5px', margin: '24px 16px'}">
              {{ current_user }}
              <template #overlay>
                <a-menu style="width:140px" align="center" @click="handleMenuClick">
                  <a-menu-item key="8" align="center" v-if="logingUserRoleName === 'ROLE_超级管理员'">
                    <TeamOutlined />
                    用户管理
                  </a-menu-item>

                  <a-menu-item key="9" align="center" v-if="logingUserRoleName === 'ROLE_超级管理员'">
                    <SolutionOutlined />
                    角色管理
                  </a-menu-item>

                  <a-menu-item key="10" align="center">
                    <UserOutlined />
                    修改信息
                  </a-menu-item>
                  <!-- <a-menu-item key="2" v-if="AuthUi.login_item2.vis">
                      <PlusOutlined />
                      添加用户
                    </a-menu-item> -->
                  <!-- <a-menu-item key="3" v-if="AuthUi.login_item3.vis">
                      <TeamOutlined />
                      添加角色
                    </a-menu-item> -->
                  <!-- <a-menu-item key="4" v-if="AuthUi.login_item4.vis">
                      <TeamOutlined />
                      用户设置
                    </a-menu-item> -->
                  <!-- <a-menu-item key="5" v-if="AuthUi.login_item5.vis">
                      <UserOutlined />
                      用户登录
                    </a-menu-item> -->
                  <a-menu-item key="7" align="center">
                    <LogoutOutlined />
                    退出
                  </a-menu-item>
                </a-menu>
              </template>
              <template #icon>
                <UserOutlined />
              </template>
            </a-dropdown-button>

          </a-col>
        </a-row>
      </a-layout-header>
      <a-layout-content style="padding: 10px;">
        <!-- <a-breadcrumb style="margin: 16px 0">
          <a-breadcrumb-item>User</a-breadcrumb-item>
          <a-breadcrumb-item>Bill</a-breadcrumb-item>
        </a-breadcrumb> -->
        <div :style="{ background: '#162130ce', minHeight: '100%' }">
          <router-view></router-view>
          <!-- 用户管理弹窗 -->
          <a-modal title="用户管理" ok-text="确认" cancel-text="取消" v-model:visible='visibleUserManage' width="900px" @ok="handleOkUserManage()">
            <a-row span="24">
              <a-col span="7" align="center">
                <a-button @click="addUser">添加用户</a-button>
              </a-col>
              <a-col span="6">

              </a-col>
              <a-col span="8">
                <a-form-item label="选择角色：">
                  <a-select ref="select" v-model:value="model.configData.roleList" placeholder="选择角色类型" style="width:200px" :options="role_opts" @focus="focus" @change="handleChange_role_item4">
                  </a-select>
                </a-form-item>

              </a-col>
            </a-row>
            <a-table :columns="columns" :data-source="dataSource" :pagination="pagination" style="margin-top:2%">
              <template v-for="col in ['er']" #[col]="{ text}" :key="col">
                <div>
                  <template>
                    {{ text }}
                  </template>
                </div>
              </template>
              <!-- <template #operation="{ record }"> -->
              <template #operation="{ record }">
                <div class="editable-row-operations">
                  <span>
                    <a-button type="primary" @click="edit(record.key)">编辑</a-button>
                    <!-- 登录的用户不可以删除自己以及系统默认用户admin -->
                    <a-button type="primary" @click="deluser(record.key)" danger style="margin-left : 10%" v-if="!((((dataSource[record.key].user) === logingUserName)) || (((dataSource[record.key].user) === 'admin')))">删除</a-button>
                    <!-- <a @click="edit(record.key)">Edit</a> -->
                  </span>
                </div>
              </template>
            </a-table>
          </a-modal>
          <!-- 角色管理弹窗 -->
          <a-modal title="角色管理" ok-text="确认" closable="false" cancel-text="取消" width="900px" v-model:visible="visibleRoleManage" @ok="handleOkRoleManage">
            <!-- <a-row span="24">
                    <a-col span="7" align="center">
                        <a-button @click="add_role()">添加角色</a-button>
                    </a-col>
                    <a-col span="6">

                    </a-col>
                    <a-col span="8">
                    </a-col>
                </a-row> -->
            <a-table :columns="roleColumns" :data-source="roleDataSource" :pagination="pagination" style="margin-top:2%">
              <template v-for="col in ['name']" #[col]="{ text}" :key="col">
                <div>
                  <template>
                    {{ text }}
                  </template>
                </div>
              </template>
              <!-- <template #operation="{ record }"> -->
              <template #operation="{ record }">
                <div class="editable-row-operations">
                  <span>
                    <a-button type="primary" @click="editAuth(record.key)" >编辑权限</a-button>
                    <div v-if="(roleDataSource[record.key].role === 'ROLE_超级管理员')"> --系统角色，不可编辑-- </div>
                    <!-- <a-button type="primary" @click="roleInfoEdit(record.key)" style="margin-left : 7%">编辑角色信息</a-button>
            <a-button type="primary" @click="delRole(record.key)" danger style="margin-left : 7%">删除</a-button> -->
                    <!-- <a @click="edit(record.key)">Edit</a> -->
                  </span>
                </div>
              </template>
            </a-table>
          </a-modal>

          <!-- 修改信息弹窗 -->
          <a-modal title="修改信息" ok-text="确认" cancel-text="取消" width="900px" v-model:visible="visibleInfoEdit" @ok="editInfoOK">
            <a-row span="24">
              <a-col span="24">
                <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
                  <a-row span="24">
                    <a-col span="7" align="end">
                      用户名：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      {{logingUserName}}
                    </a-col>
                  </a-row>

                  <a-row span="24" style="margin-top:3%">
                    <a-col span="7" align="end">
                      密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'password']">
                        <a-input-password v-model:value="model.configData.password" style="width:60%" />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row span="24">
                    <a-col span="7" align="end">
                      重复密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'passwordRepeat']">
                        <a-input-password v-model:value="model.configData.passwordRepeat" style="width:60%" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-form>
              </a-col>

            </a-row>
          </a-modal>

          <!-- 编辑用户弹窗 -->
          <a-modal title="编辑用户" ok-text="确认" cancel-text="取消" width="900px" v-model:visible="visibleUserEditManage" @ok="handleItem4Ok">

            <a-row span="24">
              <a-col span="24">
                <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
                  <a-row span="24">
                    <a-col span="7" align="end">
                      用户名：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      {{editUser}}
                    </a-col>
                  </a-row>
                  <a-row span="24">
                    <a-col span="7" align="end">
                      用户号码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      {{editNumber}}
                    </a-col>
                  </a-row>
                  <a-row span="24" style="margin-top:3%">
                    <a-col span="7" align="end">
                      密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'password']">
                        <a-input-password v-model:value="model.configData.password" style="width:60%" />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row span="24">
                    <a-col span="7" align="end">
                      重复密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'passwordRepeat']">
                        <a-input-password v-model:value="model.configData.passwordRepeat" style="width:60%" />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <!-- <a-row span="24">
                            <a-col span="7" align="end">
                                角色：
                            </a-col>
                            <a-col span="1">
                            </a-col>
                            <a-col span="15">
                                <a-form-item>
                        <a-select
                          ref="select"
                          v-model:value="model.configData.roleList"
                          placeholder="选择角色类型"
                          style="width:60%"
                          :options="role_opts"
                          @focus="focus"
                          @change="handleChange_role_item4"
                        >
                        </a-select>
                      </a-form-item>
                            </a-col>
                        </a-row> -->
                </a-form>
              </a-col>

            </a-row>
          </a-modal>
          <!-- 新增用户弹窗 -->
          <a-modal title="新增用户" ok-text="确认" cancel-text="取消" width="900px" v-model:visible="visibleAddUser" @ok="handleItem2Ok">
            <a-row span="24">
              <a-col span="24">
                <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
                  <a-row span="24">
                    <a-col span="7" align="end">
                      用户名：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '用户名不能为空' },Pattern('UserName')]" :name="['configData', 'username']">
                        <a-input v-model:value="model.configData.username" style="width:60%">
                        </a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row span="24">
                    <a-col span="7" align="end">
                      用户号码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '用户号码选填' },Pattern('Number')]">
                        <a-input v-model:value="model.configData.number" style="width:60%">
                        </a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row span="24" style="margin-top:3%">
                    <a-col span="7" align="end">
                      密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'password']">
                        <a-input-password v-model:value="model.configData.password" style="width:60%">
                        </a-input-password>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row span="24" style="margin-top:3%">
                    <a-col span="7" align="end">
                      确认密码：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item :rules="[{ required: true, message: '请输入密码' },Pattern('Pass')]" :name="['configData', 'passwordRepeat']">
                        <a-input-password v-model:value="model.configData.passwordRepeat" style="width:60%">
                        </a-input-password>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row span="24" style="margin-top:3%">
                    <a-col span="7" align="end">
                      角色：
                    </a-col>
                    <a-col span="1">
                    </a-col>
                    <a-col span="15">
                      <a-form-item>
                        <a-select ref="select" v-model:value="model.configData.roleList" placeholder="---选择用户角色---" style="width:60%" :options="role_opts" @focus="focus" @change="handleChange_adduser">
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </a-form>
              </a-col>
            </a-row>
          </a-modal>
          <a-modal v-model:visible="visible_item1" title="编辑权限" ok-text="确认" cancel-text="取消" width="500px" :confirm-loading="confirmLoading" @ok="handleItem1Ok" @cancel="handleItem1Cancel">
            <!-- <a-card title="权限设置"> -->
            <a-form :model="model" name="nest-messages" style="width:40%;
                           height:40%" layout="horizontal" @finish="confirm" :validate-messages="validate">
              <a-tree v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys_auth" v-model:checkedKeys="checkedKeys" checkable :tree-data="treeData">
                <template #title="{ title, key }">
                  <span v-if="key === '0-0-1'" style="color: #1890ff">{{ title }}</span>
                  <template v-else>{{ title }}</template>
                </template>
              </a-tree>
            </a-form>
          </a-modal>

          <!-- <a-modal
            v-model:visible="visible_item2"
            title="添加用户"
            :confirm-loading="confirmLoading"
            @ok="handleItem2Ok"
          >
                <a-form
                  :model="model"
                  name="nest-messages"
                  layout="horizontal"
                  @finish="confirm"
                  :validate-messages="validate">
                  <a-row justify="left">
                      <a-form-item label="用户类型：">
                        <a-select
                          ref="select"
                          v-model:value="model.configData.roleList"
                          placeholder="用户类型"
                          style="width:100px;margin-left:50px"
                          :options="role_opts"
                          @focus="focus"
                          @change="handleChange_adduser"
                        >
                        </a-select>
                      </a-form-item>

                  </a-row>

                  <a-row justify="left">
                        <a-col :span="6">
                            <a-form-item :rules="[{  message: '用户名不能为空' }]"  :name="['configData', 'username']" label="用户名称：">
                            <a-input  v-model:value="model.configData.username"  style="width:200px;margin-left:50px"/>
                            </a-form-item>
                        </a-col>
                  </a-row>

                  <a-row justify="left">
                      <a-col :span="6">
                        <a-form-item :rules="[{  message: '密码不能为空' }]" :name="['configData', 'password']" label="输入密码：" >
                          <a-input-password  v-model:value="model.configData.password"  style="width:200px;margin-left:50px"/>
                        </a-form-item>
                      </a-col>
                  </a-row>

                  <a-row justify="left">
                      <a-col :span="6">
                        <a-form-item :rules="[{  message: '密码不能为空' }]" :name="['configData', 'passwordRepeat']" label="重复输入：">
                          <a-input-password  v-model:value="model.configData.passwordRepeat"  style="width:200px;margin-left:50px"/>
                        </a-form-item>
                      </a-col>
                  </a-row>
                  <a-divider />
                  <p>{{ modalText }}</p>
                </a-form>
            </a-card>
          </a-modal> -->

          <a-modal v-model:visible="visible_item3" title="添加角色" ok-text="确认" cancel-text="取消" width="900px" @ok="handleItem3Ok">
            <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">

              <a-row span="24">
                <a-col span="7" align="end">
                  角色名称：
                </a-col>
                <a-col span="1">
                </a-col>
                <a-col span="16" align="start">
                  <a-form-item :rules="[{  message: '角色类型不能为空' }]" :name="['configData', 'rolename']">
                    <a-input v-model:value="roleName" style="width:60%" />
                  </a-form-item>
                </a-col>
              </a-row>
              <p>{{ modalText }}</p>
            </a-form>
            <!-- </a-card> -->
          </a-modal>

          <a-modal v-model:visible="visibleRoleInfoEdit" title="编辑角色信息" ok-text="确认" cancel-text="取消" width="900px" @ok="handleItem3Ok">
            <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">

              <a-row span="24">
                <a-col span="7" align="end">
                  角色名称：
                </a-col>
                <a-col span="1">
                </a-col>
                <a-col span="16" align="start">
                  <a-form-item :rules="[{  message: '角色类型不能为空' }]" :name="['configData', 'rolename']">
                    <a-input v-model:value="roleName" style="width:60%" />
                  </a-form-item>
                </a-col>
              </a-row>
              <p>{{ modalText }}</p>
            </a-form>
            <!-- </a-card> -->
          </a-modal>

          <!-- <a-modal
            v-model:visible="visible_item4"
            title="用户设置"
            :confirm-loading="confirmLoading"
            @ok="handleItem4Ok"
          >

                <a-row justify="left">
                      <a-form-item label="角色类型：">
                        <a-select
                          ref="select"
                          v-model:value="model.configData.roleList"
                          placeholder="选择角色类型"
                          style="width:200px"
                          :options="role_opts"
                          @focus="focus"
                          @change="handleChange_role_item4"
                        >
                        </a-select>
                      </a-form-item>

                </a-row>

                <a-form
                  :model="model"
                  name="nest-messages"
                  layout="horizontal"
                  @finish="confirm"
                  :validate-messages="validate">
                  <a-row justify="left">
                      <a-form-item label="选择用户：">
                        <a-select
                          ref="select"
                          v-model:value="model.configData.userList"
                          placeholder="用户"
                          style="width:200px"
                          :options="user_opts"
                          @focus="focus"
                          @change="handleChange_user_item4"
                        >
                        </a-select>
                      </a-form-item>

                  </a-row>

                  <a-row justify="left">
                    <a-switch v-model:checked="checked1" checked-children="开" un-checked-children="关"  @change="switchChange_ifmodPass()"/>   是否修改密码
                  </a-row>
                  <a-divider />
                  <a-row justify="left">
                      <a-col :span="6">
                        <a-form-item :rules="[{  message: '密码不能为空' }]" :name="['configData', 'password']" label="输入密码：" >
                          <a-input-password  v-model:value="model.configData.password"  :disabled="!checked1" style="width:200px;margin-left:50px"/>
                        </a-form-item>
                      </a-col>
                  </a-row>

                  <a-row justify="left">
                      <a-col :span="6">
                        <a-form-item :rules="[{  message: '密码不能为空' }]" :name="['configData', 'passwordRepeat']" label="重复输入：">
                          <a-input-password  v-model:value="model.configData.passwordRepeat"  :disabled="!checked1" style="width:200px;margin-left:50px"/>
                        </a-form-item>
                      </a-col>
                  </a-row>

                  <a-divider />
                  <p>{{ modalText }}</p>
                </a-form>
               </a-card>
          </a-modal> -->

          <a-modal v-model:visible="visible_item5" title="用户登录" :confirm-loading="confirmLoading" @ok="handleItem5Ok">
            <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">

              <a-row justify="left">
                <a-col :span="6">
                  <a-form-item :rules="[{  message: '用户名不能为空' }]" :name="['configData', 'username']" label="用户名称：">
                    <a-input v-model:value="model.configData.username" style="width:200px;margin-left:50px" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row justify="left">
                <a-col :span="6">
                  <a-form-item :rules="[{  message: '密码不能为空' }]" :name="['configData', 'password']" label="输入密码：">
                    <a-input-password v-model:value="model.configData.password" style="width:200px;margin-left:50px" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-divider />
              <p>{{ modalText }}</p>
            </a-form>
            <!-- </a-card> -->
          </a-modal>

          <a-card id="notice_card" class="sticky" v-if="ifShowNoticeCard" hoverable style="width: 300px;font-size: large;z-index: 999999 !important; position:fixed; top:60px" title="会议通知">
            <p style="font-size:16px; color:white">【{{ meet_master_name }}】 通知您参加会议</p>
            <p style="font-size:16px; color:white; padding-left:15px;"> 会议主题：{{ meet_title }}</p>
            <p style="font-size:16px; color:white; padding-left:15px;"> 房间号：{{ meet_number }}</p>
            <a-button type="primary" @click="joinMeeting()">跳转</a-button>
            <a-button type="primary" @click="denyMeeting()">忽视</a-button>
          </a-card>

        </div>
      </a-layout-content>
    </a-layout>

  </a-layout>
</template>
<script>
import { CodeSandboxOutlined, LogoutOutlined, VideoCameraOutlined, ReconciliationOutlined, ToolOutlined, LineChartOutlined, GatewayOutlined, SettingOutlined, BarChartOutlined, PlaySquareOutlined, GlobalOutlined, UserOutlined, TeamOutlined, SolutionOutlined } from '@ant-design/icons-vue'
import { onMounted, onUnmounted, defineComponent, ref, watch, reactive, toRefs } from 'vue'
import { TreeSelect, message, notification } from 'ant-design-vue'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { UserSet } from '@/views/deviceinfo/model/UserSet'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { AuthList } from '@/views/deviceinfo/constant/authlist'
import { AuthOption } from '@/views/deviceinfo/constant/authOption'
import { UserRequestAction } from '@/views/deviceinfo/action/userRequestAction'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { AuthUiRequestAction } from '@/views/deviceinfo/action/authuiRequestAction'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { UserType } from '@/views/deviceinfo/constant/options'
import { useRoute } from 'vue-router'
import Router from '@/router/index'
import bus from '@/libs/bus'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
import { SystemInfoActive } from '@/views/deviceinfo/action/systeminfoAction'
import { cloneDeep } from 'lodash-es'
import md5 from 'js-md5'
import Pattern from '@/common/pattern'
import * as mqtt from 'mqtt/dist/mqtt.min'
import { MeetingAction } from '@/views/deviceinfo/action/meetingAction'
// import axios from 'axios'

export default defineComponent({
  components: {
    VideoCameraOutlined,
    CodeSandboxOutlined,
    BarChartOutlined,
    GlobalOutlined,
    UserOutlined,
    TeamOutlined,
    PlaySquareOutlined,
    GatewayOutlined,
    ReconciliationOutlined,
    ToolOutlined,
    LogoutOutlined,
    LineChartOutlined,
    SettingOutlined,
    SolutionOutlined
    // PlusOutlined
    /* MenuUnfoldOutlined, */
    /* MenuFoldOutlined */
  },
  setup () {
    const pagination = {
      pageSize: 5
    }
    // 用户的columns
    const columns = [
      {
        title: '用户名',
        dataIndex: 'user',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'user'
        }
      },
      {
        title: '用户号码',
        dataIndex: 'number',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'number'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    // 角色的columns
    const roleColumns = [
      {
        title: '角色',
        dataIndex: 'role',
        width: '40%',
        align: 'center',
        slots: {
          customRender: 'role'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    const userData = ref()
    userData.value = []

    // 显示通知
    const ifShowNoticeCard = ref(false)

    // 角色数据
    const roleName = ref()
    const roleData = ref()
    roleData.value = []
    let athName
    const editUser = ref()
    const editNumber = ref()
    const dataSource = ref(userData.value)
    const meetingAction = new MeetingAction()

    const systemUser = ref(true)
    const logingUser = ref('')
    const logingUserName = ref('')
    const logingUserRoleName = ref('')
    let editUserRoleName = reactive()
    // 初始化客户端
    // 连接mq 的配置
    let brokerUrl // Replace with your MQTT broker URL over WSS
    const topic = 'android' // Replace with the topic you want to subscribe to

    let mqttClient
    let jsonconfig_str
    // 会议通知
    const nociceMeet = ref()
    let t_message = ''
    const dataMeet = ref()
    dataMeet.value = []
    const meet_master_id = ref('')
    const meet_master_name = ref('')
    const meet_title = ref('')
    const meet_number = ref('')
    const meet_millis_time = ref('')
    const confirm = ref('')
    // 用户列表
    const userList = ref([])

    const bookList = ref([])
    const currList = ref([])
    const hisList = ref([])

    const meetInfoList = [] // 立即会议列表，通过标志位判断是否已接收通知

    // 角色数据源
    const roleDataSource = ref(roleData.value)
    const editableData = reactive({})

    // 用户管理确定按钮绑定的函数
    const handleOkUserManage = () => {
      visibleUserManage.value = false
    }

    // 角色管理确定按钮绑定的函数
    const handleOkRoleManage = () => {
      visibleRoleManage.value = false
    }

    // 修改用户信息（密码）确定按钮绑定函数
    const editInfoOK = () => {
      // 角色为用户登录时候的角色，有getRoleName(Action)方法获取到的角色,在页面刷新的时候将用户的角色读出来（onMount钩子函数中调用）
      baseRequestData_user.entity.rolename = logingUserRoleName
      baseRequestData_user.entity.password = model.configData.password
      if (model.configData.password === '') {
        // 弹窗提示
        message.info('密码不能为空')
        return
      }
      if (model.configData.passwordRepeat === '') {
        // 弹窗提示
        message.info('重复密码不能为空')
        return
      }
      if (model.configData.password !== model.configData.passwordRepeat) {
        // 弹窗提示
        message.info('密码与确认密码不一致！')
        return
      }
      baseRequestData_user.entity.password = md5(baseRequestData_user.entity.password)
      model.configData.passwordRepeat = md5(model.configData.passwordRepeat)
      baseRequestData_user.entity.username = logingUserName.value

      // 编辑用户信息
      userRequestAction.modPass(baseRequestData_user, modPassSuccess, callbackError, null)

      // 添加完成后将username和Rolename 恢复至登录状态
      baseRequestData_user.entity.username = logingUser
      baseRequestData_user.entity.rolename = logingUserRoleName
      visibleInfoEdit.value = false
    }

    // 编辑用户
    const edit = key => {
      editableData[key] = cloneDeep(dataSource.value.filter(item => key === item.key)[0])
      //   alert(key)
      model.configData.username = ''
      model.configData.password = ''
      model.configData.passwordRepeat = ''
      model.configData.number = ''
      // 打开编辑弹窗
      visibleUserEditManage.value = ref(true)

      // 需要编辑的用户
      editUser.value = userData.value[key].user
      editNumber.value = userData.value[key].number
      userRequestAction.getRoleName(baseRequestData_user, getEditUserRoleNameSuccess, callbackError, null)
      baseRequestData_user.entity.rolename = editUserRoleName
    }

    // 添加用户
    const addUser = () => {
      model.configData.username = ''
      model.configData.password = ''
      model.configData.passwordRepeat = ''
      model.configData.number = ''
      user_list = []
      //   userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
      visibleAddUser.value = ref(true)
    }

    // 删除用户
    const deluser = (i) => {
      //   alert('删除用户：' + userData.value[i].user)
      //   console.log(' UserData: ===========')
      //   console.log(userData)
      baseRequestData_user.entity.rolename = selectRole
      // 获取到需要删除的用户名
      baseRequestData_user.entity.username = userData.value[i].user
      userRequestAction.delUser(baseRequestData_user, delUserSuccess, callbackError, null)
    }

    // 删除角色
    const delRole = (i) => {
      //   alert('删除角色：' + roleData.value[i].role)
      baseRequestData_user.entity.rolename = roleData.value[i].role
      userRequestAction.delRole(baseRequestData_user, delRoleSuccess, callbackError, null)
    }

    // 编辑角色信息
    const roleInfoEdit = (i) => {
      visibleRoleInfoEdit.value = true
      //   alert('编辑角色：' + roleData.value[i].role)
    }
    // 编辑权限
    const editAuth = (i) => {
      visible_item1.value = true
      baseRequestData_user.entity.rolename = roleData.value[i].role
      // 将对应角色的权限显示在tree中
      showAuthInTree()
    }

    // 调用打招呼事件，传入消息内容
    console.info(bus)
    const ico_logo = ref('')
    const selectRole = ref()
    selectRole.value = null
    let user_list = ref([])
    let treeData = ref([])
    const curr_tree = ref([])
    let current_user = ref()
    const model = reactive(new Deviceinfo())
    const user = new UserSet()
    model.configData = user
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const baseRequestData_user = reactive(new BaseRequestData(baseParam, user))

    const sysrequestAction = new SystemInfoActive()
    const getSysInfo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      sysrequestAction.query(baseRequestData, getSystemInfoSuccess, callbackError, null)
    }

    const sysname = ref('')
    const getSystemInfoSuccess = (data) => {
      // model.value = data.data
      // this.model = data.data
      // console.info(model.value)
      sysname.value = data.data.sysname
      ico_logo.value = data.data.logourl
      console.info(sysname)
    }
    // 设置定时器获取系统信息更新
    setInterval(() => {
      if (sessionStorage.getItem('sysname')) { sysname.value = sessionStorage.getItem('sysname') }
      if (sessionStorage.getItem('syslogo')) { ico_logo.value = sessionStorage.getItem('syslogo') }
    }, 3000)
    // bus.$on('sysname', sysname.value = sysname)

    const SHOW_PARENT = TreeSelect.SHOW_PARENT
    const value = ref(['0-0-0'])
    let authlist = null
    let topo_id = 0
    let getMeetingUser_count = 0

    watch(value, () => {
      console.log(value.value)
    })
    // 对应后台数据表

    const modalText = ref('')
    const modalText1 = ref('')
    const confirmLoading = ref(false)
    // 用户管理弹窗控制变量
    const visibleUserManage = ref(false)
    // 角色管理弹窗控制变量
    const visibleRoleManage = ref(false)
    // 信息修改弹窗控制变量
    const visibleInfoEdit = ref(false)
    // 编辑用户弹窗控制变量
    const visibleUserEditManage = ref(false)
    // 编辑角色权限弹窗控制变量
    const visibleUserAuthEdit = ref(false)
    // 编辑角色信息弹窗控制变量
    const visibleRoleInfoEdit = ref(false)
    // 增加用户信息弹窗控制变量
    const visibleAddUser = ref(false)
    const visible = ref(false)
    const visible_item0 = ref(false)
    const visible_item1 = ref(false)
    const visible_item2 = ref(false)
    const visible_item3 = ref(false)
    const visible_item4 = ref(false)
    const visible_item5 = ref(false)

    const userRequestAction = new UserRequestAction()
    const authuiRequestAction = new AuthUiRequestAction()

    const role_opts = ref([])
    const user_opts = ref([])
    const state = reactive({
      checked1: false,
      checked2: false,
      checked3: false
    })

    const state_ui = reactive({
      indeterminate: true,
      checkAll: false,
      checkedList: []
    })

    const options1 = [
      { value: '超级用户', label: '超级用户' },
      { value: '普通用户', label: '普通用户' }
    ]

    // let plainOptions = ['Apple', 'Pear', 'Orange']
    const plainOptions = ref([])

    treeData = AuthList

    bus.$on('hide_notice_card', (data) => {
      const notice_card_id = window.document.getElementById('notice_card')
      if (notice_card_id !== null) { notice_card_id.style.display = 'none' }
    })

    const selectedKeys_auth = ref([])
    watch(selectedKeys_auth, () => {
      console.info('selectedKeys_auth', selectedKeys_auth)
    })

    watch(() => state_ui.checkedList, val => {
      console.info(val)
      console.info(state_ui.checkedList)
      modalText1.value = state_ui.checkedList
      state_ui.indeterminate = !!val.length && val.length < plainOptions.value.length
      state_ui.checkAll = val.length === plainOptions.value.length
    })

    // const expandedKeys = ref(['0-0-0', '0-0-1'])
    // const checkedKeys = ref(['0-0-0', '0-0-1'])
    const expandedKeys = ref()
    const checkedKeys = ref([])
    watch(expandedKeys, () => {
      console.info('expandedKeys', expandedKeys)
    })
    watch(checkedKeys, () => {
      console.info('checkedKeys', checkedKeys)
      const obj = checkedKeys.value
      // curr_tree.value = ''
      modalText1.value = ''
      authlist = []

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        if (obj[key] != null) {
          // curr_tree.value.push({
          //   value: obj[key].value
          // })
          // curr_tree.value.push(obj[key].value)
          modalText1.value += obj[key] + ' | '
          authlist.push(obj[key])
        }
      })
      // console.info(curr_tree.value)
      console.info(authlist)
    })

    const onCheckAllChange = e => {
      Object.assign(state_ui, {
        checkedList: e.target.checked ? JSON.parse(JSON.stringify(plainOptions.value)) : [],
        indeterminate: false
      })
    }
    const showDrawer = () => {
      visible.value = true
      bus.$emit('sayHi', visible.value)
    }
    const showVideoDrawer = () => {
      visible.value = true
      bus.$emit('sayHi', visible.value)
    }

    // 点击用户管理后的处理
    const userManage = () => {
      visibleUserManage.value = true
      userData.value.length = 0
      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
      console.log('*******************')
      console.log('selectRole:' + selectRole.value)
    }

    // 点击角色管理后的处理
    const roleManage = () => {
      visibleRoleManage.value = true
      roleData.value.length = 0
      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
    }

    // 点击修改信息后的处理
    const infoEdit = () => {
      // 将密码框清空
      model.configData.password = ''
      model.configData.passwordRepeat = ''
      visibleInfoEdit.value = true
    }

    const add_user = () => {
      user_list = []
      modalText.value = null
      visible_item2.value = true

      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
    }
    // 编辑对应角色权限的时候对权限列表进行清空
    const authority_set = (i) => {
      user_list = []
      current_user = null
      visible_item1.value = true
      // 将权限列表所有的权限全部设置为false
      for (const p in AuthUi) {
        AuthUi[p].vis = false
      }
      // 从数据库中读取对应用户设置的权限并对authList中的值进行设置
      AuthUi.login_item1.vis = true
    }

    const authui_set = () => {
      user_list = []
      current_user = null
      visible_item0.value = true

      const obj = AuthUi
      plainOptions.value = []

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        if (obj[key].name != null) {
          plainOptions.value.push(
            obj[key].name
          )
        }
      })

      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
    }

    const handleChange_ui = value => {
      console.log(`selected ${value}`)
      // curr_tree = treeData
      console.info('curr_tree:')
      console.info(curr_tree.value)
      baseRequestData_user.entity = {}
      baseRequestData_user.entity.rolename = value
      authuiRequestAction.getAuthui(baseRequestData_user, getAuthUiListSuccess, callbackError, null)
    }

    const handleChange_auth = value => {
      console.log(`selected ${value}`)
      // curr_tree = treeData
      console.info('curr_tree:')
      console.info(curr_tree.value)
      baseRequestData_user.entity = {}
      baseRequestData_user.entity.rolename = value
      userRequestAction.getAuthList(baseRequestData_user, getAuthListSuccess, callbackError, null)
    }

    const handleChange_adduser = value => {
      console.log(`selected ${value}`)
      baseRequestData_user.entity.rolename = value
    }

    const handleChange_role_item4 = value => {
      console.log(`selected ${value}`)
      selectRole.value = value
      console.log('selectRole:' + selectRole.value)
      userData.value.length = 0
      baseRequestData_user.entity.rolename = value

      userRequestAction.getUserList(baseRequestData_user, getuserlistSuccess, callbackError, null)
    }

    const handleChange_user_item4 = value => {
      console.log(`selected ${value}`)
      baseRequestData_user.entity.username = value
    }

    /*     const handleChange_auth = value => {
      console.log(`selected ${value}`)
      // model.configData.rolename = value
      baseRequestData_user.entity.rolename = value
      userRequestAction.getUserList(baseRequestData_user, getAuthListSuccess, callbackError, null)
    } */

    // 获取权限列表成功回调
    const getAuthListSuccess = (data) => {
      const obj = data.data.authoritylist
      console.log('==========obj==============')
      console.log(obj)
      modalText.value = ''
      // 将对应authList项设为true
      for (let i = 0; i < obj.length; i++) {
        for (const k in AuthUi) {
          if (obj[i].authorityname === AuthUi[k].name) {
            // 如果这个权限为可读
            if (obj[i].authority === 1) {
              // 将对应的UI显示列表中的vis设为true
              AuthUi[k].vis = true
              // 将可读置为1
              AuthUi[k].statusR = 1
              break
            } else if (obj[i].authority === 2) { // 如果这个权限可写
              // 将对应的UI显示列表中的vis设为true
              AuthUi[k].vis = true
              // 将可读置为1
              AuthUi[k].statusR = 1
              // 将可写置为1
              AuthUi[k].statusW = 1
            } else {
              AuthUi[k].vis = false
            }
            console.log('~~~~~~~~~~~')
            console.log(AuthUi[k].name + ':' + AuthUi[k].vis)
          }
        }
      }
      // 获取登录用户的角色
      // 再将登录用户管理和角色管理的权限进行设置
      userManagerMethod(baseRequestData_user.entity.rolename)
      //   Object.keys(obj).forEach((key) => {
      //     console.info(obj[key])
      //     if (obj[key].id != null) {
      //       curr_tree.value.push((obj[key].authorityname))
      //       // role_opts.value.push({
      //       //   value: obj[key].rolename,
      //       //   label: obj[key].rolename
      //       // })
      //       modalText.value += obj[key].authorityname + ' -' + obj[key].authority + ' -' + obj[key].url + ' |    \r\n '
      //     }
      //   })
    }

    // 获取权限列表成功回调（将对应权限写入树中使用）
    const getAuthListSuccessTree = (data) => {
      const obj = data.data.authoritylist
      console.log('obj数组：')
      console.log(obj)
      modalText.value = ''
      // 外层循环遍历obj,目的为了找到同name的key
      for (let i = 0; i < obj.length; i++) {
        // 内层循环遍历auth，目的是为了找到key
        for (const k in AuthUi) {
          if (obj[i].authorityname === AuthUi[k].name) {
            // 如果这个权限为可读,则将对应KEY后追加-0，并将其加入选择列表中
            if (obj[i].authority === 1) {
              // 将对应的权限key加-0，并将其加入选择列表
              checkedKeys.value.push(AuthUi[k].key + '-0')
            } else if (obj[i].authority === 2) { // 如果这个权限可写
              // 将对应的权限key加-1，并将其加入选择列表
              checkedKeys.value.push(AuthUi[k].key + '-1')
            }
          }
        }
      }
      // 获取登录用户的角色
      // 再将登录用户管理和角色管理的权限进行设置
      userManagerMethod(baseRequestData_user.entity.rolename)
      //   Object.keys(obj).forEach((key) => {
      //     console.info(obj[key])
      //     if (obj[key].id != null) {
      //       curr_tree.value.push((obj[key].authorityname))
      //       // role_opts.value.push({
      //       //   value: obj[key].rolename,
      //       //   label: obj[key].rolename
      //       // })
      //       modalText.value += obj[key].authorityname + ' -' + obj[key].authority + ' -' + obj[key].url + ' |    \r\n '
      //     }
      //   })
    }

    // 将获取到的对应角色权限在树中显示
    const showAuthInTree = () => {
      checkedKeys.value = []
      userRequestAction.getAuthList(baseRequestData_user, getAuthListSuccessTree, callbackError, null)
    }
    // 获取登录用户的角色名回调
    const getRoleNameSuccess = (data) => {
      baseRequestData_user.entity.rolename = data.data.rolename
      logingUserRoleName.value = baseRequestData_user.entity.rolename
      getAuthListLoding()
    }

    // 获取待编辑用户的角色名回调
    const getEditUserRoleNameSuccess = (data) => {
      // 获取编辑用户的角色
      editUserRoleName = reactive(data.data.rolename)
    }

    const getAuthUiListSuccess = (data) => {
      const obj = data.data.authoritylist
      console.log('=========obj1========')
      console.log(obj)
      modalText.value = ''
      // const check_option = []

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        if (obj[key].id != null) {
          // check_option.push((obj[key].authorityname))
          // plainOptions.value.push((obj[key].authorityname))
          // role_opts.value.push({
          //   value: obj[key].rolename,
          //   label: obj[key].rolename
          // })
          modalText.value += obj[key].authorityname + ' -' + obj[key].authority + ' -' + obj[key].url + ' |    \r\n '
        }
      })
      // plainOptions = check_option
      console.info(plainOptions)
    }

    const modAuthSuccess = (data) => {
      console.info(data)
      message.success('权限设置成功')
      // const obj = data.data.rolelist

      // model.configData.roleList = []
      // role_opts.value = []

      // Object.keys(obj).forEach((key) => {
      //   console.info(obj[key])
      //   if (obj[key].rolename != null) {
      //     model.configData.roleList.push((obj[key].rolename))
      //     role_opts.value.push({
      //       value: obj[key].rolename,
      //       label: obj[key].rolename
      //     })
      //   }
      // })
      // 重新获取登录用户权限数据
      reSetRoleList()
    }

    const add_role = () => {
      user_list = []
      model.configData.rolename = ''
      visible_item3.value = true
    }

    const getrolelistSuccess = (data) => {
      console.log(data)
      const obj = data.data.rolelist
      console.log(obj)

      model.configData.roleList = []
      role_opts.value = []

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        if (obj[key].rolename != null) {
          model.configData.roleList.push((obj[key].rolename))
          role_opts.value.push({
            value: obj[key].rolename,
            label: obj[key].rolename
          })
        }
      })

      for (let i = 0; i < role_opts.value.length; i++) {
        console.log('========角色==========')
        console.log(role_opts.value[i].value)
        roleData.value.push({
          key: i.toString(),
          role: role_opts.value[i].value
        })
        console.log(roleData)
      }
      // role_opts.value = role
      console.log('roleList:' + model.configData.roleList)
      console.log(role_opts.value)
      baseRequestData_user.entity.rolename = role_opts.value[0].value

      // 如果selectRole为空，则默认为刚打开页面时候的role
      if (selectRole.value === null) {
        selectRole.value = role_opts.value[0].value
      }
      userRequestAction.getUserList(baseRequestData_user, getuserlistSuccess, callbackError, null)
    }

    const getuserlistSuccess = (data) => {
      console.log(data)
      const obj = data.data.userlist
      console.log(obj)

      model.configData.userList = []
      user_opts.value = []

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        if (obj[key].username !== null && obj[key].username !== 'Sadmin') {
          model.configData.userList.push((obj[key].username))
          user_opts.value.push({
            value: obj[key].username,
            label: obj[key].username,
            number: obj[key].number
          })
        }
      })

      for (let i = 0; i < user_opts.value.length; i++) {
        console.log('====================')
        console.log(user_opts.value[i].value)
        // 隐藏超级用户的数据
        if (user_opts.value[i].value !== null) {
          userData.value.push({
            key: i.toString(),
            user: user_opts.value[i].value,
            number: user_opts.value[i].number
          })
        }
        if (user_opts.value[i].value === 'admin') {
          systemUser.value = false
        } else {
          systemUser.value = true
        }
        console.log(userData)
      }
      console.log('userList:' + model.configData.userList)
      console.log(user_opts.value)
    }

    const handleChange_role = value => {
      console.log(`selected ${value}`)
    }

    const user_login = () => {
      user_list = []
      visible_item5.value = true
    }

    const mod_pass = () => {
      user_list = []
      visible_item4.value = true

      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
    }

    const login_out = () => {
      // // 结束mqtt
      // if (window.mq_client !== undefined) {
      //   window.mq_client.end()
      //   window.mq_client = null
      // }
      sessionStorage.removeItem('login_state')
      Router.push({
        path: '/login'
      })
      window.location.reload(true)
    }

    const handleItem1Ok = () => {
      // 将选择的key加入authlist
      authlist = checkedKeys.value
      //   console.log('checkedkeys:')
      //   console.log(checkedKeys)
      //   console.log('authlist:')
      //   console.log(authlist)
      // modalText.value = 'The modal will be closed after two seconds'
      confirmLoading.value = true
      setTimeout(() => {
        visible_item1.value = false
        confirmLoading.value = false
      }, 3000)

      //   authlist = ['0-0-0', '0-0-1', '0-1-0']

      // 选择的节点key
      let obj = authlist
      // 父节点
      const parent = AuthList
      // 一级子节点
      const child_1 = []
      // 二级子节点
      const child_2 = []
      // 三级子节点
      const child_3 = []
      const setAuthlist = []
      const auth_number = ref(0)
      // 对权限列表默认赋值为false (测试用)
      //   for (let i = 0; i < AuthUi.length; i++) {
      //     AuthUi[i].vis = false
      //   }
      // 获取一级子节点、二级子节点、三级子节点数据
      // 一级
      for (let i = 0; i < parent.length; i++) {
        console.log(parent[i].children)
        child_1[i] = parent[i].children
        // // 创建二级子节点
        for (let j = 0; j < child_1[i].length; j++) {
          const temp = child_1[i]
          if (temp[j].children != null) {
            console.log(temp[j].title + '有二级子节点：')
            console.log(temp[j].children)
            child_2[j] = temp[j].children
            // 创建三级节点
            for (let k = 0; k < child_2[j].length; k++) {
              const temp3 = child_2[j]
              if (temp3[k].children != null) {
                console.log(temp3[k].title + '有三级子节点：')
                console.log(temp3[k].children)
                child_3[k] = temp3[k].children
              }
            }
          }
          //   console.log('===============')
          //   console.log(temp)
        }
        // if (child_1[i].children != null) {
        //   child_2[i] = child_1[i].children
        // }
      }
      console.log('=====父节点=====')
      console.log(parent)
      console.log('=====一级子节点=====')
      console.log(child_1)
      console.log('=====二级子节点=====')
      console.log(child_2)
      console.log('=====三级子节点=====')
      console.log(child_3)
      // const child = parent.children

      // 查看用户选择的tree_的key
      // console('====用户选择的key===')
      // console.log(obj)
      if (obj === null) {
        obj = []
      }
      // 遍历所选数组
      for (let i = 0; i < obj.length; i++) {
        if (obj[i].length !== 1) {
          // 如果长度为1 则不进行判断遍历
          if (obj[i].length === 3) {
            // 则是一级子节点，有可能为权限,对其进行遍历
            for (let j = 0; j < child_1.length; j++) {
              for (let k = 0; k < child_1[j].length; k++) {
                const temp_child = child_1[j]
                if (typeof (temp_child[k].children) === 'undefined') {
                  // 下面为无子节点的时候
                  // 打印权限
                  if (obj[i] === temp_child[k].key) {
                    console.log('=== 权限====')
                    console.log(temp_child[k].name + ':' + temp_child[k].title)
                    athName = temp_child[k].name
                    // 对元素写入权限
                    if (temp_child[k].title === '读权限') {
                      //   AuthUi.athName.vis = true
                      console.log('==========权限表中对应的数据=============')
                      console.log(athName)
                      auth_number.value = 1
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child[k].url)
                      changeAth(athName, true)
                      cheangeWOrR(athName, 'r')
                    } else if (temp_child[k].title === '写权限') {
                      auth_number.value = 2
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child[k].url)
                      changeAth(athName, true)
                      cheangeWOrR(athName, 'w')
                    } else {
                      auth_number.value = 0
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child[k].url)
                      changeAth(athName, false)
                    }
                  }
                }
              }
            }
          } else if (obj[i].length === 5) { // 则是二级子节点，有可能是权限，对其进行遍历
            for (let j = 0; j < child_2.length; j++) {
              for (let k = 0; k < child_2[j].length; k++) {
                const temp_child2 = child_2[j]
                if (typeof (temp_child2[k].children) === 'undefined') {
                  // 下面为无子节点的时候
                  // 打印权限
                  if (obj[i] === temp_child2[k].key) {
                    console.log('=== 权限====')
                    console.log(temp_child2[k].name + ':' + temp_child2[k].title)
                    athName = temp_child2[k].name
                    // 对元素写入权限
                    if (temp_child2[k].title === '读权限') {
                      //   AuthUi.athName.vis = true
                      console.log('==========权限表中对应的数据=============')
                      console.log(athName)
                      auth_number.value = 1
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child2[k].url)
                      changeAth(athName, true)
                    } else if (temp_child2[k].title === '写权限') {
                      auth_number.value = 2
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child2[k].url)
                      changeAth(athName, true)
                    } else {
                      auth_number.value = 0
                      writeAuthToList(setAuthlist, athName, auth_number.value, temp_child2[k].url)
                      changeAth(athName, false)
                    }
                  }
                }
              }
            }
          } else if (obj[i].length === 7) { // 则是三级子节点，是权限，对其进行遍历
            for (let j = 0; j < child_3.length; j++) {
              for (let k = 0; k < child_3[j].length; k++) {
                const temp_child3 = child_3[j]
                // 打印权限
                if (obj[i] === temp_child3[k].key) {
                  console.log('=== 权限====')
                  console.log(temp_child3[k].name + ':' + temp_child3[k].title)
                  athName = temp_child3[k].name
                  // 对元素写入权限
                  if (temp_child3[k].title === '读权限') {
                    //   AuthUi.athName.vis = true
                    console.log('==========权限表中对应的数据=============')
                    console.log(athName)
                    auth_number.value = 1
                    writeAuthToList(setAuthlist, athName, auth_number.value, temp_child3[k].url)
                    changeAth(athName, true)
                    cheangeWOrR(athName, 'r')
                  } else if (temp_child3[k].title === '写权限') {
                    auth_number.value = 2
                    changeAth(athName, true)
                    writeAuthToList(setAuthlist, athName, auth_number.value, temp_child3[k].url)
                    cheangeWOrR(athName, 'w')
                  } else {
                    auth_number.value = 0
                    changeAth(athName, false)
                    writeAuthToList(setAuthlist, athName, auth_number.value, temp_child3[k].url)
                  }
                }
              }
            }
          }
        }
      }

      console.log('遍历完成后的权限列表为：')
      console.log(setAuthlist)

      // 进行遍历，对权限进行赋零操作
      // 找到权限列表中是否有obj中的对象，采用双层循环
      let finded = 0
      for (const i in AuthOption) {
        for (let k = 0; k < setAuthlist.length; k++) {
          if ((setAuthlist[k].authorityname) === AuthOption[i].name) {
            // 当前权限中有对应的权限项目
            finded = 1
            break
          }
        }
        // 遍历完权限列表后依然没有将对应权限加入
        if (finded === 0) {
          auth_number.value = 0
          writeAuthToList(setAuthlist, AuthOption[i].name, auth_number.value, AuthOption[i].url)
        }
        // 结束后将finded置为0
        finded = 0
      }
      baseRequestData_user.entity.authoritylist = setAuthlist
      //   alert('准备设定的角色' + baseRequestData_user.entity.rolename)
      userRequestAction.modAuth(baseRequestData_user, modAuthSuccess, callbackError, null)
      //   alert('设置角色权限完成：' + baseRequestData_user.entity.rolename)
      // 修改权限完成后对页面进行刷新
      //   countDown()
    }

    // 编辑权限取消按键回调
    const handleItem1Cancel = () => {
      reSetRoleList()
    }

    // // 刷新页面弹窗
    // const countDown = () => {
    //   let secondsToGo = 5
    //   const modal = Modal.success({
    //     width: '800px',
    //     okText: '好',
    //     title: '权限修改成功',
    //     content: `权限修改成功，系统将在 ${secondsToGo} 秒后自动刷新以生效.`
    //   })
    //   const interval = setInterval(() => {
    //     secondsToGo -= 1
    //     modal.update({
    //       content: `权限修改成功，系统将在 ${secondsToGo} 秒后自动刷新以生效.`
    //     })
    //   }, 1000)
    //   setTimeout(() => {
    //     clearInterval(interval)
    //     modal.destroy()
    //     location.reload()
    //   }, secondsToGo * 1000)
    // }

    // 对权限进行改变的方法
    const changeAth = (athName, athValue) => {
      console.log('*********AuthUi*********')
      console.log(AuthUi)
      // 遍历权限列表，找出对应的权限，并对其进行更改
      for (const p in AuthUi) {
        if (p === athName) {
          AuthUi[p].vis = athValue
          // 改变后的值
          console.log('改变后的值：' + AuthUi[p].vis)
          break
        }
      }
    }
    // 对读写权限进行操作的方法
    const cheangeWOrR = (athName, r) => {
      // 查找变量
      let findFlag = false
      // 遍历权限列表，找出对应的权限，并对其进行更改
      for (const p in AuthUi) {
        if (p === athName) {
          // 判断该权限是否有读写属性
          if (AuthUi[p].statusW !== undefined) {
            // alert(AuthUi[p].name + '具有读写属性')
            if (r === 'w') {
              let t = ''
              t = AuthUi[p].key + '-0'
              // 只有选择好的keys数组中不存在t的时候才将其加入
              for (let i = 0; i < checkedKeys.value.length; i++) {
                if (checkedKeys.value[i] === t) {
                  // 出现相同的key则将其查找变量设置true
                  findFlag = true
                  break
                }
              }
              // 当查找变量不为true时说明之前没有选中对应权限的读权限，选中写权限的时候需要将读权限也作以附带选中
              if (!findFlag) {
                // 将对应权限的key加入checkedKeys
                checkedKeys.value.push(t)
                // 将查找变量设为false
                findFlag = false
              }
            } else {
              console.error('读写权限错误')
            }
          }
          break
        }
      }
    }

    // 将权限写入权限列表的方法
    const writeAuthToList = (setAuthlist, authorityName, auth_number, authUrl) => {
      setAuthlist.push({
        authorityname: authorityName,
        authority: auth_number,
        url: authUrl
      })
    }

    const handleItem2Ok = () => {
      // modalText.value = 'The modal will be closed after two seconds'
      if (model.configData.username === '') {
        // 弹窗提示
        message.info('用户名不能为空')
        return
      }
      if (model.configData.password === '') {
        // 弹窗提示
        message.info('密码不能为空')
        return
      }
      if (model.configData.passwordRepeat === '') {
        // 弹窗提示
        message.info('重复密码不能为空')
        return
      }
      if (model.configData.password !== model.configData.passwordRepeat) {
        // 弹窗提示
        message.info('密码与确认密码不一致！')
        return
      }

      // 调用后端接口

      setTimeout(() => {
        visible_item2.value = false
        confirmLoading.value = false
      }, 3000)

      //      model.id = 8888
      baseRequestData_user.entity.username = model.configData.username
      baseRequestData_user.entity.number = model.configData.number
      baseRequestData_user.entity.password = model.configData.password
      baseRequestData_user.entity.password = md5(baseRequestData_user.entity.password)
      model.configData.passwordRepeat = md5(model.configData.passwordRepeat)

      userRequestAction.addUser(baseRequestData_user, addUserSuccess, callbackError, null)

      //   alert(logingUserName.value)
      // 添加完成后对弹窗进行隐藏
      visibleAddUser.value = false
    }

    const addUserSuccess = (data) => {
      if (data.error_code === 1016) {
        message.error('添加失败，用户名已被使用！')
      } else {
        message.success('用户添加成功')
        // 添加成功后重新刷新列表
        userData.value.length = 0
        baseRequestData_user.entity.rolename = selectRole.value
        userRequestAction.getUserList(baseRequestData_user, getuserlistSuccess, callbackError, null)
      }
      // 添加完成后将用户名置为登录状态
      baseRequestData_user.entity.username = logingUserName.value
    }

    const handleItem3Ok = () => {
      model.configData.rolename = roleName
      baseRequestData_user.entity.rolename = model.configData.rolename

      userRequestAction.addRole(baseRequestData_user, roleSetSuccess, callbackError, null)
    }

    const roleSetSuccess = (data) => {
      console.log(data)
      message.success('添加角色成功')
      // 添加成功后重新获取列表
      roleData.value.length = 0
      userRequestAction.getrolelist(baseRequestData, getrolelistSuccess, callbackError, null)
      // 隐藏弹窗
      visible_item3.value = false
    }

    const modPassSuccess = (data) => {
      console.log(data)
      message.success('密码修改成功')
      // 添加完成后将username和Rolename 恢复至登录状态
      baseRequestData_user.entity.username = logingUser
      baseRequestData_user.entity.rolename = logingUserRoleName.value
      visibleUserEditManage.value = false
    }

    const delUserSuccess = (data) => {
      console.log(data)
      message.success('删除用户成功')
      // 删除成功后重新刷新列表
      userData.value.length = 0
      baseRequestData_user.entity.rolename = selectRole.value
      userRequestAction.getUserList(baseRequestData_user, getuserlistSuccess, callbackError, null)
    }

    const delRoleSuccess = (data) => {
      console.log(data)
      message.success('删除角色成功')
      // 删除成功后重新刷新列表
      roleData.value.length = 0
      userRequestAction.getrolelist(baseRequestData_user, getrolelistSuccess, callbackError, null)
    }
    const handleItem4Ok = () => {
      baseRequestData_user.entity.rolename = selectRole.value
      baseRequestData_user.entity.password = model.configData.password
      if (model.configData.password === '') {
        // 弹窗提示
        message.info('密码不能为空')
        return
      }
      if (model.configData.passwordRepeat === '') {
        // 弹窗提示
        message.info('重复密码不能为空')
        return
      }
      if (model.configData.password !== model.configData.passwordRepeat) {
        // 弹窗提示
        message.info('密码与确认密码不一致！')
        return
      }
      baseRequestData_user.entity.password = md5(baseRequestData_user.entity.password)
      model.configData.passwordRepeat = md5(model.configData.passwordRepeat)
      baseRequestData_user.entity.username = editUser.value

      // 编辑用户
      userRequestAction.modPass(baseRequestData_user, modPassSuccess, callbackError, null)
    }

    const handleItem5Ok = () => {
      confirmLoading.value = true
      const formData = new FormData()
      formData.append('username', model.configData.username)
      formData.append('password', model.configData.password)
      console.info(formData)
      userRequestAction.login(formData, getUserSuccess, callbackError, null)
    }

    // 用户更改权限后，重新将authui列表进行赋值，获取到登录角色的权限列表
    const reSetRoleList = () => {
      baseRequestData_user.entity.rolename = logingUserRoleName.value
      // 获取到登录的角色权限列表
      userRequestAction.getAuthList(baseRequestData_user, getAuthListSuccess, callbackError, null)
    }

    const getUserSuccess = (data) => {
      console.log(data)
      if (data === '登录成功') {
        const myDate = new Date()
        const login_user = { user: model.configData.username, login_time: myDate.toLocaleString() }
        console.info(login_user)
        const json = JSON.stringify(login_user)
        sessionStorage.setItem('login_state', json)
        console.info('login_state:' + json)
        setTimeout(() => {
          visible_item5.value = false
          confirmLoading.value = false
          message.success('用户登录成功')
        }, 300)
        current_user.value = model.configData.username
      } else if (data === '密码错误') {
        message.success('账号或密码错误！')
        login_out()
      } else {
        message.success('登录失败，请重试')
        login_out()
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      console.error(error.message, 3)
    }

    const switchChange_ifmodPass = () => {
      console.log(state.checked1)
    }

    const handleItem6Ok = () => {
      // modalText.value = 'The modal will be closed after two seconds'
      confirmLoading.value = true
      setTimeout(() => {
        visible_item0.value = false
        confirmLoading.value = false
      }, 3000)

      const authUIlist = []
      const obj = state_ui.checkedList
      const authui_obj = AuthUi

      Object.keys(authui_obj).forEach((key) => {
        authui_obj[key].vis = false
      }
      )

      obj.forEach((item, index, arr) => {
        if (item != null) {
          Object.keys(authui_obj).forEach((key) => {
            if (authui_obj[key].name === item) {
              authui_obj[key].vis = true
              // s_auth.uname = authui_obj[key].name
              // s_auth.uid = authui_obj[key].id
              // s_auth.uifshow = authui_obj[key].vis
              // s_auth.rolename = baseRequestData_user.entity.rolename
              // authUIlist.push(s_auth)

              authUIlist.push({
                uname: authui_obj[key].name,
                uid: authui_obj[key].id,
                uifshow: authui_obj[key].vis === true ? 1 : 0,
                rolename: baseRequestData_user.entity.rolename
              })
            }
          })
        }
      })

      baseRequestData_user.entity.authUIlist = authUIlist

      authuiRequestAction.modAuthui(baseRequestData_user, modAuthUiSuccess, callbackError, null)
    }

    const modAuthUiSuccess = (data) => {
      console.info(data)
      message.success('UI设置成功')
      // const obj = data.data.rolelist

      // model.configData.roleList = []
      // role_opts.value = []

      // Object.keys(obj).forEach((key) => {
      //   console.info(obj[key])
      //   if (obj[key].rolename != null) {
      //     model.configData.roleList.push((obj[key].rolename))
      //     role_opts.value.push({
      //       value: obj[key].rolename,
      //       label: obj[key].rolename
      //     })
      //   }
      // })
    }

    const handleMenuClick = (e) => {
      console.log('click', e)
      model.configData.username = ''
      model.configData.password = ''
      modalText.value = ''
      modalText1.value = ''
      switch (e.key) {
        case '1': // 权限设置
          authority_set()
          break
        case '2': // 添加用户
          add_user()
          break
        case '3': // 角色设置
          add_role()
          break
        // case '4': // 用户设置
        //   mod_pass()
        //   break
        case '5': // 用户登录
          user_login()
          break
        case '6': // 界面设置
          authui_set()
          break
        case '7': // 退出
          login_out()
          break
        case '8': // 用户管理
          userManage()
          break
        case '9': // 角色管理
          roleManage()
          break
        case '10': // 修改信息
          infoEdit()
          break
        default:
          break
      }
    }

    const handleChange = value => {
      console.log(`selected ${value}`)
    }

    const wd = 20
    const sy = 60
    const us = 'zhudangfeng'

    // 控制"设备列表"按钮的显示和隐藏，当路由路径为"/"时，显示，其他情况隐藏
    const route = useRoute()
    const switchShowDrawer = ref(true)
    const switchShowVideoDrawer = ref(true)
    watch(route, () => {
      if (route.path === '/') {
        switchShowDrawer.value = true
        switchShowVideoDrawer.value = true
      } else {
        switchShowDrawer.value = false
        switchShowVideoDrawer.value = false
      }
    })

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListSuccess = (data) => {
      const obj = data.data.content
      // console.info(obj)
      obj.forEach((item, index, arr) => {
        topo_id = item.id
        if (item.in_network === 1) {
          topo_id = item.id
        }
      })
      console.info('topo_id:' + topo_id)
      Router.push({
        path: 'topo_draw',
        query: {
          id: topo_id
        }
      })
    }

    const getListFinally = () => {
      console.info('OK')
    }

    // 在加载页面的时候获取对应角色权限的方法
    const getAuthListLoding = () => {
      // 在进入页面的时候就获取相应的权限
      userRequestAction.getAuthList(baseRequestData_user, getAuthListSuccess, callbackError, null)
      // 对用户管理、角色管理赋予权限
      userManagerMethod(baseRequestData_user.entity.rolename)
    }

    // 登录成功提示
    const successLoding = () => {
      const loging = sessionStorage.getItem('login_state')

      notification.success({
        message: '用户登录成功！',
        description:
          '您好,' + JSON.parse(loging).user + '       欢迎登录！'
      })
    }

    // 判断用户角色，给用户管理及权限管理赋予权限
    const userManagerMethod = (ro) => {
      // 如果用户是管理员权限，则对权限管理菜单进行权限设定
      if (ro === 'ROLE_超级管理员') {
        // 如果为超级管理员则有权限用户管理、权限管理
        AuthUi.用户管理.vis = true
        AuthUi.角色管理.vis = true
      } else {
        // 除此之外的用户不能进行用户管理和权限管理
        AuthUi.用户管理.vis = false
        AuthUi.角色管理.vis = false
      }
    }

    const getMeetingUser = () => {
      meetingAction.get_user_list(baseRequestData, getMeetingUserListSuccess, callbackError, null)
    }

    const getMeetingUserListSuccess = (data) => {
      const obj = data.data
      console.log(obj)
      let str_login = ''
      let str_topic = ''
      dataMeet.value = []
      let master_name

      getMeetingUser_count++
      console.info('In getMeetingUserListSuccess times: ', getMeetingUser_count)

      if (window.mq_client.connected) {
        mqttClient = window.mq_client
      }

      Object.keys(obj).forEach((key) => {
        if (obj[key].login === true) str_login = '在线'
        else str_login = '离线'
        if (obj[key].username === current_user.value) {
          str_topic = '/meeting/' + obj[key].id + '_' + current_user.value
          window.meet_master_id = obj[key].id
          mqttClient.subscribe(str_topic, (err) => {
            if (!err) {
              console.log('Subscribed to topic:', str_topic)
            }
          })
          mqttClient.on('message', (str_topic, message) => {
            // console.log('Received message on topic:', topic)
            t_message = message.toString()
            console.info(message.toString())
            nociceMeet.value = JSON.parse(message.toString())
            const list = nociceMeet.value.list
            // nociceMeet.value.meet_master_id = nociceMeet.meet_master_id
            // nociceMeet.value.meet_title = nociceMeet.meet_title.
            if (nociceMeet.value.msg_type === 0) {
              console.info('收到立即会议：' + list.length)
              list.forEach((item, index, arr) => {
                // if (item.meet_master_id === window.meet_master_id) {
                if (index === 0) {
                  userList.value.forEach((item1, index1, arr1) => {
                    if (item1.user_id === item.meet_master_id) {
                      master_name = item1.user_name
                    }
                  })
                  meet_title.value = item.meet_title
                  meet_number.value = item.meet_number
                  meet_master_id.value = item.meet_master_id
                  meet_master_name.value = master_name
                  meet_millis_time.value = item.millis_time

                  //  查看meetInfoList里是否有相同的立即会议且用户未确认
                  confirm.value = false
                  meetInfoList.forEach((item2, index2, arr2) => {
                    if ((item2.meet_number === meet_number.value) && (item2.meet_millis_time === meet_millis_time.value)) {
                      confirm.value = item2.confirm
                    }
                  })
                  if (confirm.value === false) { ifShowNoticeCard.value = true } else { ifShowNoticeCard.value = false }
                }
              })
            }
            if (nociceMeet.value.msg_type === 1) {
              console.info('收到预约会议：' + list.length)
              bookList.value = []
              list.forEach((item, index, arr) => {
                bookList.value.push({
                  key: index,
                  meet_number: item.meet_number,
                  meet_master_id: item.meet_master_id,
                  meet_title: item.meet_title,
                  meet_topic: item.meet_topic,
                  meet_time: item.millis_time,
                  user_list: item.user_list
                })
              })
              bus.$emit('bookmeetinfo', bookList.value)
            }
            if (nociceMeet.value.msg_type === 2) {
              console.info('收到当前会议：' + list.length)
              currList.value = []
              list.forEach((item, index, arr) => {
                currList.value.push({
                  key: index,
                  meet_number: item.meet_number,
                  meet_master_id: item.meet_master_id,
                  meet_title: item.meet_title,
                  meet_topic: item.meet_topic,
                  meet_time: item.millis_time,
                  user_list: item.user_list
                })
              })
              bus.$emit('currmeetinfo', currList.value)
            }
            if (nociceMeet.value.msg_type === 3) {
              console.info('收到历史会议：' + list.length)
              hisList.value = []
              list.forEach((item, index, arr) => {
                hisList.value.push({
                  key: index,
                  meet_number: item.meet_number,
                  meet_master_id: item.meet_master_id,
                  meet_title: item.meet_title,
                  meet_topic: item.meet_topic,
                  meet_time: item.millis_time,
                  user_list: item.user_list
                })
              })
              // bus.$emit('hismeetinfo', JSON.stringify(hisList.value))
              bus.$emit('hismeetinfo', hisList.value)
            }
          })
        }
        dataMeet.value.push({
          key: key,
          user_name: obj[key].username,
          user_id: obj[key].id,
          meetName: '',
          status: str_login,
          checked: false,
          lable: '未选中'
        })
      })
      window.dataMeet = dataMeet
    }

    const disconnectFromMqtt = () => {
      if (mqttClient) {
        // mqttClient.end()
        console.log('Disconnected from MQTT broker')
      }
    }

    const mqtt_connect = () => {
      mqttClient.on('connect', () => {
        console.log('Connected to MQTT broker')
        mqttClient.subscribe(topic, (err) => {
          if (!err) {
            console.log('Subscribed to topic:', topic)
          }
        })
      })

      mqttClient.on('error', (err) => {
        console.error('MQTT connection error:', err)
      })

      mqttClient.on('close', () => {
        console.log('mqtt 连接已关闭!')
      })

      // Listen for message arrival
      /*
      mqttClient.on('message', (topic, message) => {
        // console.log('Received message on topic:', topic)
        t_message = message.toString()
        console.info(message.toString())
        nociceMeet.value = JSON.parse(message.toString())
        // nociceMeet.value.meet_master_id = nociceMeet.meet_master_id
        // nociceMeet.value.meet_title = nociceMeet.meet_title
        if (nociceMeet.value.msg_type === 0) {
          ifShowNoticeCard.value = true
        }
        console.log(ifShowNoticeCard.value)
      })
    */
      if (mqttClient !== undefined) {
        window.mq_client = mqttClient
        window.topic = topic
      }
    }

    // 收到通知跳转到会议
    const joinMeeting = () => {
      ifShowNoticeCard.value = false

      console.log('当前页面')
      console.log(route.path)
      sessionStorage.setItem('joinMeetingByRoute', t_message)
      let find = false
      meetInfoList.forEach((item, index, arr) => {
        if ((item.meet_number === meet_number.value) && (item.meet_millis_time === meet_millis_time.value)) {
          find = true
        }
      })
      if (find === false) {
        meetInfoList.push({
          meet_number: meet_number.value,
          meet_millis_time: meet_millis_time.value,
          confirm: true
        })
      }

      if (route.path === '/meetindex') {
        //  bus.$emit('meetinfo', t_message)
        // console.log('In meetindex, send meetinfo! ')
        Router.push({
          path: 'joinMeet',
          query: {
            id: 'joinMeetingByRoute'
          }
        })
      } else {
        console.log('In other page, Router to meetindex! ')
        Router.push({
          path: 'meetindex',
          query: {
            id: 'joinMeetingByRoute'
          }
        })
      }
    }

    // 拒绝会议
    const denyMeeting = () => {
      ifShowNoticeCard.value = false
      let find = false
      meetInfoList.forEach((item, index, arr) => {
        if ((item.meet_number === meet_number.value) && (item.meet_millis_time === meet_millis_time.value)) {
          find = true
        }
      })
      if (find === false) {
        meetInfoList.push({
          meet_number: meet_number.value,
          meet_millis_time: meet_millis_time.value,
          confirm: true
        })
      }
    }

    const getMeetUserList = () => {
      meetingAction.get_user_list(baseRequestData, getMeetUserListSuccess, callbackError, null)
    }

    const getMeetUserListSuccess = (data) => {
      const obj = data.data
      console.log(obj)

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        userList.value.push({
          user_name: obj[key].username,
          user_id: obj[key].id
        })
      })
    }

    const pre_mqtt = () => {
      let MQTT_OPTION = {}
      let client_id = ''
      client_id = 'MeshForWeb--' + generateUniqueClientIdWithTime()
      if (jsonconfig_str != null) {
        console.log(jsonconfig_str)
        const json_obj = JSON.parse(jsonconfig_str)
        MQTT_OPTION = {
          brokerUrl: json_obj.mqtt_bloker_wss + '/mqtt',
          keepalive: 120, // 120 秒心跳
          reconnectPeriod: 1000, // 1秒钟后重连
          clientId: client_id,
          clean: true,
          username: json_obj.mqtt_username,
          password: json_obj.mqtt_password
        }
      }
      mqttClient = mqtt.connect(MQTT_OPTION.brokerUrl, MQTT_OPTION)
    }

    onUnmounted(() => {
      console.log('检查mqtt')
      if ((window.mq_client === undefined) || (window.mq_client === null)) {
        pre_mqtt()
        setTimeout(() => {
          mqtt_connect()
        }, 1000)
      }
    })

    onMounted(() => {
      const json_str = sessionStorage.getItem('login_state')
      jsonconfig_str = sessionStorage.getItem('mesh_Config')
      if (json_str != null) {
        console.log(json_str)
        const json_obj = JSON.parse(json_str)
        current_user.value = json_obj.user
        logingUser.value = json_obj.user
        logingUserName.value = json_obj.user
      }

      const jsonauth_str = sessionStorage.getItem('AuthUi')
      if (jsonauth_str != null) {
        console.log(jsonauth_str)
        const json_authobj = JSON.parse(jsonauth_str)
        console.info(json_authobj)
      }

      baseRequestData_user.entity.username = logingUser
      userRequestAction.getRoleName(baseRequestData_user, getRoleNameSuccess, callbackError, null)

      getSysInfo()
      successLoding()
      if ((window.mq_client === undefined) || (window.mq_client === null)) {
        pre_mqtt()
        setTimeout(() => {
          mqtt_connect()
          getMeetingUser()
          getMeetUserList()
        }, 3000)
      }
    })

    const goToTopoIndex = () => {
      // this.$router.push('topo_draw')
      console.info(baseRequestData)
      getList()
    }

    /**
 * 获取MQTT的连接ClientId唯一的标识
 */
    const generateUniqueClientIdWithTime = () => {
      let timestamp = ''
      timestamp = (Date.now() / 1000).toFixed(0) // 获取当前时间戳
      let randomBytes = ''
      randomBytes = Math.random().toString(16).substring(2, 10) // 生成随机字节
      return `${timestamp}-${randomBytes}` // 时间戳和随机字节组合
    }

    return {
      // placements: ['bottomLeft', 'bottom', 'bottomRight', 'topLeft', 'top', 'topRight']
      placements: ['topRight'],
      Pattern,
      getList,
      goToTopoIndex,
      showDrawer,
      showVideoDrawer,
      switchShowDrawer,
      switchShowVideoDrawer,
      getMeetUserList,
      getMeetUserListSuccess,
      ico_logo,
      visible,
      visible_item0,
      visible_item1,
      visible_item2,
      visible_item3,
      confirmLoading,
      user_list,
      current_user,
      model,
      UserSet,
      LabelCaption,
      UserType,
      TreeSelect,
      handleMenuClick,
      add_user,
      authority_set,
      modalText,
      handleItem1Ok,
      handleItem2Ok,
      SHOW_PARENT,
      treeData,
      value,
      handleChange,
      options1,
      handleItem3Ok,
      add_role,
      user_login,
      visible_item5,
      handleItem5Ok,
      wd,
      sy,
      us,
      roleSetSuccess,
      mod_pass,
      visible_item4,
      handleItem4Ok,
      login_out,
      sysname,
      addUserSuccess,
      getrolelistSuccess,
      getuserlistSuccess,
      role_opts,
      user_opts,
      handleChange_role,
      handleChange_auth,
      getAuthListSuccess,
      baseRequestData_user,
      handleChange_adduser,
      handleChange_user_item4,
      handleChange_role_item4,
      modPassSuccess,
      switchChange_ifmodPass,
      ...toRefs(state),
      ...toRefs(state_ui),
      selectedKeys_auth,
      logingUserRoleName,
      expandedKeys,
      checkedKeys,
      curr_tree,
      modalText1,
      authlist,
      AuthList,
      topo_id,
      AuthUi,
      pagination,
      getAuthUiListSuccess,
      handleChange_ui,
      authui_set,
      plainOptions,
      onCheckAllChange,
      AuthUiRequestAction,
      handleItem6Ok,
      modAuthUiSuccess,
      userManage,
      roleManage,
      infoEdit,
      visibleRoleInfoEdit,
      visibleUserAuthEdit,
      visibleUserManage,
      visibleRoleManage,
      visibleUserEditManage,
      visibleInfoEdit,
      visibleAddUser,
      dataSource,
      roleDataSource,
      columns,
      roleColumns,
      editingKey: '',
      editableData,
      roleName,
      logingUser,
      logingUserName,
      handleOkRoleManage,
      editInfoOK,
      edit,
      addUser,
      deluser,
      delRole,
      editUser,
      editNumber,
      handleOkUserManage,
      handleItem1Cancel,
      editAuth,
      userData,
      systemUser,
      roleInfoEdit,
      mqttClient,
      brokerUrl,
      topic,
      mqtt_connect,
      disconnectFromMqtt,
      ifShowNoticeCard,
      joinMeeting,
      denyMeeting,
      nociceMeet,
      dataMeet,
      getMeetingUser,
      meet_master_id,
      meet_title,
      meet_master_name,
      meet_number,
      confirm,
      pre_mqtt
    }
  },

  methods: {
    test () {
      this.$router.push('/about')
    },
    goToDeviceMap () {
      this.$router.push('/')
    },
    goToDeviceView () {
      this.$router.push('/view')
    },
    goToDeviceIndex () {
      this.$router.push('deviceinfo_index')
    },
    goToSweepIndex () {
      this.$router.push('sweep_set')
    },

    goToDevicelogIndex () {
      this.$router.push('deviceinfo_logindex')
    },
    goToOperatelogIndex () {
      this.$router.push('operateinfo_logindex')
    },
    goToMeetIndex () {
      this.$router.push('meetindex')
    },
    goToVideoPlay () {
      this.$router.push('video')
    },
    gotoHkVideo () {
      this.$router.push('video_hk')
    },
    goToFsIndex () {
      this.$router.push('fsindex')
    },
    goToSystemInfo () {
      this.$router.push('sysinfo_set')
    },
    goToSeaMap () {
      this.$router.push('SeaMapView')
    }
  },

  data () {
    return {
      collapsed: ref(false),
      selectedKeys: ref(['1'])
    }
  }

})
</script>
<style>
.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.site-layout .site-layout-background {
  background: #fff;
}

.demo-dropdown-wrap :deep(.ant-dropdown-button) {
  margin-right: 8px;
  margin-bottom: 8px;
}

.notice {
  position : fixed;
  margin-right: 0%;
  color: #ffffff

}

[data-theme='dark'] .site-layout .site-layout-background {
  background: #151010;
}
</style>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}
</style>
