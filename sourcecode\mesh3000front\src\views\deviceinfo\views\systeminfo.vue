<template>
    <div style="position: fixed; width: 100%; height: 100%;background-color: #162130;">
        <div style="width:90%;height: 99%;position : absolute">
  <a-card style="margin-top: 3%;" :bordered="false" >

    <a-form :label-col="labelCol" :wrapper-col="wrapperCol" name="basic" :model="model" layout="horizontal" autocomplete="off" @finish="onFinish" @finishFailed="onFinishFailed">

      <a-form-item label="系统名称:" :rules="[{ required: true, message: '请输入系统名称!' }]">
        <a-input v-model:value="model.sysname" />
      </a-form-item>

      <a-form-item label="公司名称:" :rules="[{ required: true, message: '请输入公司名称!' }]">
        <a-input v-model:value="model.comp" />
      </a-form-item>

      <a-form-item :span="8" label="版本号:" :rules="[{ required: true, message: '请输入版本信息!' }]">
        <a-input v-model:value="model.version" type="textarea" />
      </a-form-item>
      <a-form-item :span="8" label="格式化数据:">
        <a-row :span="24">
        <a-col :span="20">
            <a-select ></a-select>
        </a-col>
        <a-col>
            <a-button type="primary">确定</a-button>
        </a-col>
      </a-row>
      </a-form-item>

      <a-form-item label="logo:">
        <a-upload accept=".jpg,.png,.jpeg" v-model:file-list="fileList" list-type="picture" :max-count="1" :before-upload="beforeUpload">
          <a-button>
            <upload-outlined></upload-outlined>
            选择
          </a-button>
        </a-upload>
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
        <a-space>
          <a-button type="primary" html-type="submit" v-if="AuthUi.系统设置.statusW">保存</a-button>
          <a-button type="primary" @click="pageDirectAction.goToIndex()">返回</a-button>
        </a-space>
      </a-form-item>

    </a-form>

  </a-card>
</div>
  </div>
</template>

<script>
import { onMounted, defineComponent, ref, createVNode } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { SystemInfoActive } from '@/views/deviceinfo/action/systeminfoAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { SystemInfo } from '@/views/deviceinfo/model/SystemInfoMode'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
// import bus from '@/libs/bus'

import {
  BandWidthOptions,
  WorkModeOptions,
  TrunkOptions,
  RoutingHealthIndexOptions,
  InNetworkOptions,
  DistanceOptions,
  HDMIinOptions,
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  name: 'SystemInfo-Set',
  setup () {
    // 对应后台数据表
    const model = ref(new SystemInfo())
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new SystemInfoActive()
    const pageDirectAction = new PageDirectAction()

    const beforeUpload = info => {
      /* if (info.file.status !== 'uploading') { */
      /*   console.log(info.file, info.fileList) */
      /* } */

      /* if (info.file.status === 'done') { */
      /*   message.success(`${info.file.name} file uploaded successfully`) */
      /* } else if (info.file.status === 'error') { */
      /*   message.error(`${info.file.name} file upload failed.`) */
      /* } */
      /* const file = info.fileList[0] */
      const fileReader = new FileReader()
      fileReader.addEventListener('loadend', function () {
        model.value.logourl = fileReader.result
        console.info('000000')
      }, false)
      if (info) { fileReader.readAsDataURL(info) }
      console.info(model.value.logourl)
      return false
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // ****************** 新增 *******************
    const onFinish = (values) => {
      sysinfoset()
    }

    const onFinishFailed = (errorInfo) => {
      message.info('请填写带星的项目!')
    }

    const sysinfoset = (record) => {
      Modal.confirm({
        title: '系统信息编辑',
        icon: createVNode(QuestionOutlined),
        content: '确认修改信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setsysinfoSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('修改系统信息成功!')
        // 把系统信息存在session,主界面设定时器读信息更新标题
        sessionStorage.setItem('sysname', model.value.sysname)
        sessionStorage.setItem('syslogo', model.value.logourl)
        // 发送信息给MainLayout页面
        // bus.$emit('sysname', model.value.sysname)
        // pageDirectAction.goToIndex()
      } else {
        message.success(`修改系统信息失败!${data.error_code}`)
      }
    }

    const set = () => {
      requestAction.set(
        baseRequestData.value,
        setsysinfoSuccess,
        callbackError,
        null
      )
    }

    // ****************** 设备当前DHCP参数 *******************
    const getSysInfo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      requestAction.query(baseRequestData, getSystemInfoSuccess, callbackError, null)
    }
    const getSystemInfoSuccess = (data) => {
      model.value = data.data
      // this.model = data.data
      console.info(model.value)
    }

    onMounted(() => {
      getSysInfo()
      // getDeviceinfoById()
    })
    // ****************** 新增 *******************

    return {
      model,
      baseParam,
      LabelCaption,
      BandWidthOptions,
      WorkModeOptions,
      TrunkOptions,
      RoutingHealthIndexOptions,
      InNetworkOptions,
      DistanceOptions,
      HDMIinOptions,
      DeviceTypeOptions,
      WfModeOptions,
      DeviceinfoColumns,
      pageDirectAction,
      AuthUi,
      onFinish,
      onFinishFailed,
      Pattern,
      beforeUpload,
      labelCol: {
        style: {
          width: '120px'
        }
      },
      wrapperCol: {
        span: 8
      }
    }
  }
})
</script>

  <style scoped></style>
