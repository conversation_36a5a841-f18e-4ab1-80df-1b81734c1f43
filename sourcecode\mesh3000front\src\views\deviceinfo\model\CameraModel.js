class CameraModel {
  constructor () {
    this.id = null
    this.ip = null
    this.password = null
    this.firm = null
    this.device_name = null
    this.user_name = null
    this.port = null
    this.rtsp_url = null
    this.old_device = null
    this.device_type = null
    this.channel = null
    this.stream = null
    this.play_number = null
    this.description = null
  }

  init () {
    this.id = null
    this.ip = null
    this.password = null
    this.firm = null
    this.device_name = null
    this.user_name = null
    this.port = null
    this.rtsp_url = null
    this.old_device = null
    this.device_type = null
    this.channel = null
    this.stream = null
    this.play_number = null
    this.description = null
  }
}

export { CameraModel }
