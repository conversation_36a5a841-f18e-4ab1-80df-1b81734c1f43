<template>
<a-card title="Comuart串口设置">
      <a-form
                :model="model"
                name="nest-messages"
                layout="horizontal"
                @finish="confirm"
                :validate-messages="validate">
              <!-- 第一行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <!-- 开关 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_Switch.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item >
                                <a-switch  v-model:checked="checked" @change="onchange" style="margin-left:10px"/>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <!-- 数据模式 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_DataMode.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item
                    :name="['configData', 'mode']"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.configData.mode"
                      v-bind:placeholder="请选择"
                       style="width:200px;margin-left:10px"
                    >
                      <a-select-option
                        v-for="option in DataMode"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                     <!-- 波特率 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_BaudRate.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item
                    :name="['configData', 'bandWidth']"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.configData.baudRate"
                      v-bind:placeholder="请选择"
                       style="width:200px;margin-left:10px"
                    >
                      <a-select-option
                        v-for="option in Baudrate"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>

                 <!-- 第二行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <!-- 奇偶校验 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_Party.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item >
                               <a-select
                      ref="select"
                      v-model:value="model.configData.party"
                        style="width:200px;margin-left:10px"
                    >
                      <a-select-option
                        v-for="option in Party"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <!-- 停止位 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_Stop.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                           <a-form-item>
                    <a-select
                      ref="select"
                      v-model:value="model.configData.stop"
                        style="width:200px;margin-left:10px"
                    >
                      <a-select-option
                        v-for="option in Stop"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                     <!-- 目的IP地址 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_RemoteIP.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                        <a-form-item
                    :name="['configData', 'remoteAddr']"
                   :rules="[{ required:(model.configData.mode === 1 || model.configData.mode === 2 ), message: '目的IP不能为空' },Pattern('IP')]"
                  >
                   <a-input  v-model:value="model.configData.remoteAddr"
                   :disabled="model.configData.mode === 3"
                      style="width:200px;margin-left:10px"
                   />
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>

                <!-- 第三行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <!-- 目的端口 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_RemotePort.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item
                    :name="['configData', 'remotePort']"
                   :rules="[{ required:(model.configData.mode === 1 || model.configData.mode === 2 ), message: '目的端口不能为空' },Pattern('Port')]"
                  >
                   <a-input type="Number"
                    v-model:value="model.configData.remotePort"
                       style="width:200px;margin-left:10px"
                      :disabled="model.configData.mode === 3"
                       />
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <!-- 接收IP地址 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_LocalIP.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                          <a-form-item
                   :rules="[{ required:(model.configData.mode === 1 || model.configData.mode === 3 ), message: '接收IP不能为空' },Pattern('IP')]"
                    :name="['configData', 'localAddr']"
                  >
                   <a-input  v-model:value="model.configData.localAddr"  style="width:200px;margin-left:10px"
                   :disabled="model.configData.mode === 2"/>
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                     <!-- 接收端口 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.uart_LocalPort.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                        <a-form-item
                    :name="['configData', 'localPort']"
                    :rules="[{ required:(model.configData.mode === 1 || model.configData.mode === 3 ), message: '接收端口不能为空' },Pattern('Port')]"
                  >
                   <a-input type="number" v-model:value="model.configData.localPort"  style="width:200px;margin-left:10px"
                   :disabled="model.configData.mode === 2"/>
                  </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>
                <!-- 分割线 -->
                <a-row> <a-divider /></a-row>

                <!-- 提交按钮 -->
                <a-row justify="center" >
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" html-type="submit" v-if="AuthUi.Comart.statusW">保存</a-button>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" @click="getUart ">刷新</a-button>
                    </a-form-item>
                  </a-col>
                  </a-row>
                  </a-form>

</a-card>
</template>
<script>
import { QuestionOutlined } from '@ant-design/icons-vue'
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'

// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { UartRequestAction } from '@/views/deviceinfo/action/uartRequestAction2'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { Uart } from '@/views/deviceinfo/model/Uart2'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import {
  DataMode,
  Baudrate,
  Party,
  Stop
} from '@/views/deviceinfo/constant/options'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

export default defineComponent({

  setup () {
    // 开关控件显示
    const checked = ref(false)

    const onFinish = values => {
      console.log('Received values of form:', values)
    }

    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const uart = new Uart()
    model.configData = uart

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const uartRequestAction = new UartRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 组件状态进行改变时候，改变model的值
    const onchange = () => {
      if (checked.value === false) {
        model.configData.enabled = 0
      } else model.configData.enabled = 1
    }

    // 当model进行更改时候，改变组件状态
    const onModel = () => {
      if (model.configData.enabled === 0) {
        checked.value = false
      } else {
        checked.value = true
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }
    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
            transform()
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    // 当数据模式切换至Tcp_client的时候将接收IP&端口转为目的IP&端口，并将其接收地址以及端口转换为NULL
    // 当数据模式为Tcp_server的时候将其目的端口置空
    const transform = () => {
      if (model.configData.mode === 2) {
        model.configData.localAddr = model.configData.remoteAddr
        model.configData.localPort = model.configData.remotePort
      }
    }

    // 显示
    const transform_get = () => {
      if (model.configData.mode === 2) {
        if (model.configData.remoteAddr === null) {
          model.configData.remoteAddr = model.configData.localAddr
          model.configData.remotePort = model.configData.localPort
        }
      }
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('串口设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`串口信息设置失败!${data.error_code}`)
      }
    }

    const set = () => {
    //  model.configData.Enabled === true ? 1 : 0
      uartRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前串口参数 *******************
    const getUart = () => {
      uartRequestAction.query(baseRequestData, getUartSuccess, callbackError, null)
    }

    const getUartSuccess = (data) => {
      model.configData = data.data.configData
      transform_get()
      onModel()
    }
    // ****************** 设备当前串口参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getUart()
      // getDeviceinfoById()
    })
    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      DataMode,
      Baudrate,
      Party,
      Stop,
      AuthUi,
      onModel,
      transform,
      transform_get,
      Pattern,
      onchange,
      confirm,
      getUart,
      checked,
      onFinish
    }
  }
})
</script>
