<template>
    <a-row style="background-color: #162130ce;">
      <a-col :span="24">
        <a-card title="设备信息维护">
          <a-row justify="space-around">
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.sn.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.sn" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.device_name.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.device_name" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
            <!-- <a-col :span="6"> -->
            <!--   <a-input :addonBefore="LabelCaption.ip_addr.label" v-model:value="model.ip_addr" /> -->
            <!-- </a-col> -->
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.device_no.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.device_no" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>

            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.net_id.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.net_id" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
          </a-row>

          <a-row justify="space-around" style="margin-top:2%">
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.device_type.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-select ref="select" v-model:value="model.device_type" style="width:200px;margin-left:10px" :placeholder="LabelCaption.device_type.label">
                    <a-select-option v-for="option in DeviceTypeOptions" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
                  </a-select>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.mac_addr.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.mac_addr" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
            <!-- <a-col :span="6"> -->
            <!--   <a-input :addonBefore="LabelCaption.ip_addr.label" v-model:value="model.ip_addr" /> -->
            <!-- </a-col> -->
            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.apversion.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.apversion" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>

            <a-col :span="6">
              <a-row :span="24">
                <a-col :span="6" align="right"> {{LabelCaption.work_mode.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-select ref="select" v-model:value="model.work_mode" style="width:200px;margin-left:10px" :placeholder="LabelCaption.work_mode.label">
                    <a-select-option v-for="option in WorkModeOptions" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
                  </a-select>
                </a-col>
              </a-row>
            </a-col>
          </a-row>

          <template #actions>
            <a-row type="flex" justify="start" align="top">
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="getOnlineList">在线设备</a-button>
                  <a-button type="primary" @click="getOfflineList">离线设备</a-button>
                </a-space>
              </a-col>
              <a-col :span="10">
                <a-space>

                </a-space>
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="getList">查询</a-button>
                  <a-button style="background: orange; color: white;" @click="reset">清除条件</a-button>
                  <a-button type="primary" @click="pageDirectAction.goToAdd()">新增设备</a-button>
                </a-space>
              </a-col>
            </a-row>

          </template>
        </a-card>
      </a-col>

      <a-col :span="24">
        <a-table :data-source="dataSource.content" :columns="DeviceinfoColumns" :pagination="pagination" bordered size="middle" :scroll="{ y: 550 }" @change="pageChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'device_type'">
              <!--     render: () => <Select> -->
              <!--    <Option value='1'>手动</Option> -->
              <!--    <Option value='2'>背负</Option> -->
              <!--    <Option value='3'>车载</Option> -->
              <!--  </Select> -->
              <a-select v-model:value="record.device_type" style="width: 100%" disabled="false">
                <a-select-option v-for="option in DeviceTypeOptions" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
              </a-select>

            </template>
            <template v-if="column.key === 'action'">
              <a-row :gutter="[6, 6]" align="center">
                <a-row :gutter="[6, 6]">
                  <a-col>
                    <a-button type="primary" size="small" @click="pageDirectAction.goToDetail(record)">详情</a-button>
                  </a-col>
                  <a-col>
                    <a-button type="primary" size="small" @click="pageDirectAction.goToMod(record)">编辑</a-button>
                  </a-col>
                </a-row>
                <a-row :gutter="[6, 6]">
                  <a-col>
                    <a-button type="primary" size="small" @click="pageDirectAction.goToSet(record)">设置</a-button>
                  </a-col>
                  <a-col>
                    <a-button type="danger" size="small" @click="confirmDel(record)">删除</a-button>
                  </a-col>
                </a-row>
              </a-row>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
</template>

<script>
import { onMounted, defineComponent, ref, createVNode } from 'vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import {
  WorkModeOptions,
  DeviceTypeOptions
} from '@/views/deviceinfo/constant/options'
import { EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
export default defineComponent({
  name: 'Deviceinfo-Index',
  setup () {
    // 对应后台数据表
    const model = ref(new Deviceinfo())
    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const pageDirectAction = new PageDirectAction()
    let onlineList = []
    let offlineList = []

    // 定义分页选项
    const pagination = ref(
      {
        // 位置
        position: ['bottomCenter'],
        responsive: true,
        // 当前页
        current: 1,
        // 默认每页显示多少条
        defaultPageSize: 20,
        // 总记录数
        total: 0
      }
    )

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // ****************** 分页查询 *******************
    const getListSuccess = (data) => {
      onlineList = []
      offlineList = []
      if (data.error_code === ErrorInfo.Success) {
        dataSource.value = data.data
        pagination.value.current = data.data.number + 1
        pagination.value.total = data.data.totalElements
        data.data.content.forEach((item, index, arr) => {
          if (item.in_network === 1) {
            onlineList.push(item)
          } else {
            offlineList.push(item)
          }
        })
        console.info(onlineList)
        console.info(offlineList)
      }
    }

    const getListFinally = () => {
      console.info('OK')
    }

    const getList = () => {
      requestAction.getList(baseRequestData.value, getListSuccess, callbackError, getListFinally)
    }

    const getOnlineList = () => {
      dataSource.value.content = onlineList
    }

    const getOfflineList = () => {
      dataSource.value.content = offlineList
    }

    // 分页事件
    const pageChange = (page, pageSize) => {
      baseParam.value.page_num = page.current
      getList()
    }
    // ****************** 分页查询 *******************

    const goToDetail = (record) => {
      console.info(record)
    }

    // 清除查询条件
    const reset = () => {
      getList()
      model.value.init()
    }

    // ****************** 删除 *******************
    const confirmDel = (record) => {
      Modal.confirm({
        title: '删除设备信息',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确认删除本条设备信息吗？',
        okText: '删除',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            delDevice(record)
          }).catch(() => console.log('异常!'))
        }
      })
    }

    const delDevice = (record, resolve) => {
      const model = new Deviceinfo()
      Object.assign(model, record)
      const baseRequestData = new BaseRequestData(new BaseParam(), model)
      requestAction.delOne(baseRequestData, delDeviceSuccess, null, null)
    }

    const delDeviceSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('设备信息删除成功!')
      } else {
        message.success('设备信息删除失败，请重试!')
      }

      getList()
    }
    // ****************** 删除 *******************

    onMounted(() => {
      getList()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      WorkModeOptions,
      DeviceTypeOptions,
      DeviceinfoColumns,
      EditOutlined,
      pagination,
      getList,
      getOnlineList,
      getOfflineList,
      goToDetail,
      pageChange,
      reset,
      confirmDel,
      pageDirectAction
    }
  }
})
</script>

<style scoped>
</style>
