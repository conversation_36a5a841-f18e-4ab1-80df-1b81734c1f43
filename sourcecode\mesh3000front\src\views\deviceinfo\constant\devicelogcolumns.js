/* import { moment } from 'moment'  */// 这个moment方法。框架里本来就有引入就好
/*  */
/* const formatterTime = (val) => { */
/*   return val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '' */
/* } */
/*  */
/* import { InNetworkOptions } from '@/views/deviceinfo/constant/options' */
/*  */
/* export default function formatter (val, dict) { */
/*   return val ? dict[val] : '' */
/* } */

export const getFullDate = (date) => {
  const dateAndTime = date.split('T')
  return dateAndTime[0].split('-').reverse().join('-')
}

export const DeviceLogColumns = [
  {
    title: '设备ID',
    dataIndex: 'mesh_id',
    key: 'mesh_id',
    align: 'center'
  },
  {
    title: '设备名称',
    dataIndex: 'mesh_name',
    key: 'mesh_name',
    align: 'center'
  },
  {
    title: '设备SN',
    dataIndex: 'mesh_sn',
    key: 'mesh_sn',
    align: 'center'
  },
  {
    title: '时间',
    dataIndex: 'occurrence_time',
    key: 'occurrence_time',
    align: 'center',
    render: (date) => getFullDate(date)
  },
  {
    title: '网络状态',
    dataIndex: 'netstate',
    key: 'net_state',
    align: 'center'
    /* Text: (val) => { return formatter(val, InNetworkOptions) } */
  },
  {
    title: '剩余电量(%)',
    dataIndex: 'remaining_battery',
    key: 'remaining_battery',
    align: 'center'
  },
  {
    title: '主板温度(℃)',
    dataIndex: 'mainboard_tem',
    key: 'mainboard_tem',
    align: 'center'
  },
  {
    title: '事件描述',
    dataIndex: 'event_description',
    key: 'event_description',
    align: 'left'
  }
]
