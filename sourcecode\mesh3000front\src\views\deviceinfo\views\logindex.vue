<template>
  <div style="background-color: #162130ce;">
    <a-row>
      <a-col :span="24">
        <a-card title="设备日志信息">
          <a-row justify="space-around">
            <a-col :span="6">
              <a-row :span="22">
                <a-col :span="6" align="right"> {{LabelCaption.id.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.mesh_id" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="6">
              <a-row :span="22">
                <a-col :span="6" align="right"> {{LabelCaption.sn.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.mesh_sn" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>
            <!-- <a-col :span="6"> -->
            <!--   <a-input :addonBefore="LabelCaption.ip_addr.label" v-model:value="model.ip_addr" /> -->
            <!-- </a-col> -->
            <a-col :span="6">
              <a-row :span="22">
                <a-col :span="6" align="right"> {{LabelCaption.device_name.label+":"}} </a-col>
                <a-col :span="18" align="left">
                  <a-input v-model:value="model.mesh_name" style="width:200px;margin-left:10px" />
                </a-col>
              </a-row>
            </a-col>

            <a-col :span="6">
              <a-row :span="22">
                <a-col :span="6" align="right"> {{LabelCaption.rang_time.lable}}</a-col>
                <a-col :span="18" align="left">
                  <a-range-picker v-model:value="rangpickdate" style="margin-left:10px" :disabled-date="disabledDate" :disabled-time="disabledRangeTime" :show-time="{
                               hideDisabledOptions: true,
                               defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                             }" format="YYYY-MM-DD HH:mm:ss" />
                </a-col>
              </a-row>
            </a-col>
          </a-row>

          <a-row justify="space-around" style="margin-top:2%">
            <a-col :span="6">
            </a-col>
          </a-row>

          <template #actions>
            <a-row type="flex" justify="end" align="top">
              <a-col :span="6">
                <a-space>
                  <a-button type="primary" @click="getList(1)">查询</a-button>
                  <a-button style="background: orange; color: white;" @click="reset">清除条件</a-button>
                </a-space>
              </a-col>
            </a-row>

          </template>
        </a-card>
      </a-col>

      <a-col :span="24">
        <a-table :data-source="dataSource.content" :columns="DeviceLogColumns" :customRow="customRow" :pagination="pagination" bordered size="middle" :scroll="{ y: 750 }" @change="pageChange">
          <!--           <template v-if="column.key === 'net_state'">
              <a-labe>"nnnn"</a-labe>
          </template> -->
        </a-table>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { onMounted, defineComponent, ref, createVNode } from 'vue'
import { DeviceLogInfoAction } from '@/views/deviceinfo/action/deviceLogAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { DevicesLog } from '@/views/deviceinfo/model/DevicesLog'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceLogColumns } from '@/views/deviceinfo/constant/devicelogcolumns'
import { WorkModeOptions, DeviceTypeOptions } from '@/views/deviceinfo/constant/options'
import { EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'Deviceinfo-Index',
  setup () {
    // 对应后台数据表
    const model = ref(new DevicesLog())
    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new DeviceLogInfoAction()
    const pageDirectAction = new PageDirectAction()
    const rangpickdate = ref()
    const customRow = (record, index) => {
      return {
        style: {
        }
      }
    }

    // 定义分页选项
    const pagination = ref(
      {
        // 位置
        position: ['bottomCenter'],
        responsive: true,
        // 当前页
        current: 1,
        // 默认每页显示多少条
        defaultPageSize: 20,
        // 总记录数
        total: 0
      }
    )

    /* const onShowSizeChange = (current, pageSize) => { */
    /*   baseParam.value.page_size = pageSize */
    /*   baseParam.value.current = current */
    /* } */

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // ****************** 分页查询 *******************
    const getListSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        dataSource.value = data.data
        pagination.value.current = data.data.number + 1
        pagination.value.total = data.data.totalElements
      }
    }

    const getListFinally = () => {
      console.info('OK')
    }

    const getList = (mode) => {
      /* console.info(startdate) */
      /* console.info(starttime) */
      // console.info(model.value.sp[0])
      if (rangpickdate.value !== undefined) {
        baseParam.value.start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
        baseParam.value.end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
      }
      // 查询时page_num值应设定为1，否则无数据返回
      if (mode === 1) {
        baseRequestData.value.base_param.page_num = 1
      }
      requestAction.getList(baseRequestData.value, getListSuccess, callbackError, getListFinally)
      // console.info(x)
    }

    // 分页事件
    const pageChange = (page, pageSize) => {
      baseParam.value.page_num = page.current
      baseParam.value.page_size = page.pageSize
      getList()
    }

    /* const showSizeChange = (current, size) => { */
    /*   baseParam.value.page_num = current */
    /*   baseParam.value.page_size = size */
    /* } */
    // ****************** 分页查询 *******************

    const goToDetail = (record) => {
      console.info(record)
    }

    // 清除查询条件
    const reset = () => {
      getList()
      model.value.init()
      rangpickdate.value = undefined
      baseParam.value.start_time = null
      baseParam.value.end_time = null
    }

    // ****************** 删除 *******************
    const confirmDel = (record) => {
      Modal.confirm({
        title: '删除设备信息',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确认删除本条设备信息吗？',
        okText: '删除',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            delDevice(record)
          }).catch(() => console.log('异常!'))
        }
      })
    }

    const delDevice = (record, resolve) => {
      const model = new DevicesLog()
      Object.assign(model, record)
      const baseRequestData = new BaseRequestData(new BaseParam(), model)
      requestAction.delOne(baseRequestData, delDeviceSuccess, null, null)
    }

    const delDeviceSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('设备信息删除成功!')
      } else {
        message.success('设备信息删除失败，请重试!')
      }

      getList()
    }
    // ****************** 删除 *******************

    onMounted(() => {
      getList()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      rangpickdate,
      baseParam,
      LabelCaption,
      WorkModeOptions,
      DeviceTypeOptions,
      DeviceLogColumns,
      EditOutlined,
      pagination,
      dayjs,
      /* startdate, */
      /* starttime, */
      /* enddate, */
      /* endtime, */
      /* RangeValue, */
      getList,
      goToDetail,
      pageChange,
      reset,
      confirmDel,
      pageDirectAction,
      customRow
    }
  }
})
</script>

<style scoped>
</style>
