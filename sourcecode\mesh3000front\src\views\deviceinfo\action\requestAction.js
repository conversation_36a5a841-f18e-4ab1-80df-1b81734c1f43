import baseRequest from '@/request/request'
class RequestAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo'
    this.urlGetOne = `${this.BASE_API_URL}/get/getOne`
    this.urlGetList = `${this.BASE_API_URL}/get/getList`
    this.urlAddOne = `${this.BASE_API_URL}/add/addOne`
    this.urlDelOne = `${this.BASE_API_URL}/del/delOne`
    this.urlModOne = `${this.BASE_API_URL}/mod/modOne`
    this.urlDhcpConfigGet = `${this.BASE_API_URL}/config/dhcp/query`
    this.urlDhcpConfigSet = `${this.BASE_API_URL}/config/dhcp/set`
  }

  /**
   * 新增
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  addOne = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlAddOne, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 查询单条记录
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  getOne = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetOne, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 分页查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   */
  getList = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetList, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 删除数据
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   */
  delOne = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDelOne, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 修改数据
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   */
  modOne = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlModOne, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getConfig = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDhcpConfigGet, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  setConfig = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlDhcpConfigSet, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }
}

export { RequestAction }
