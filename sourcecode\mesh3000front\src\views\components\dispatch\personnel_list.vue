<template>
    <div class="back">
        <dv-border-box-13 style="padding: 3%">
            <div  v-if="list.length == 0" style="top:45%;left: 45%; position: absolute;">
                <a-row justify="center" align="middle"><loding2 ></loding2></a-row>
            </div>
            <a-row justify="end" align="middle">
                <a-col justify="center" align="end" :span="12">
                    <div @click="changeShowMode">
                        <button2 id="bt-change" :button_name="button_name"></button2>
                    </div>
                </a-col>
            </a-row>
            <a-row>
                <!-- <a-divider style="height: 2px; background-color: #0c458a" /> -->
            </a-row>
      <a-row :gutter="12">
            <a-col v-for="(item,index) in list" v-bind:key="index" :span="6" style="padding-top: 10px;height: 40%">
                <div style="background-img: url('/src/assets/img/phone.png'); position: relative;"
                     @click="!list[index].isOffline ? clickCard(index) : null"
                     @mouseenter="!list[index].isOffline ? mouseenterCard(index) : null"
                     @mouseleave="!list[index].isOffline ? mouseleaveCard(index) : null">
                    <a-card  style="text-align: center;padding: 0%;"  :bordered="true" :title="list[index].name"  :id="index">
                        <p v-if = "list[index].userBusy" style="text-align: center">对端号码：{{list[index].number}}</p>
                        <a-row style="justify-content: center;">

                            <a-col v-if="list[index].isRing" >
                                <img class="shake-animation" src="../../../../src/assets/img/ring.png" alt="Smiley face" width="92" height="30" style="justify-content: center;">
                            </a-col>
                            <a-col v-if="list[index].isHostRing" >
                                <img class="shake-animation" src="../../../../src/assets/img/ring.png" alt="Smiley face" width="92" height="30" style="justify-content: center;">
                            </a-col>
                            <a-col v-if="list[index].isHostHearCallBackRing" >
                                <img src="../../../../src/assets/img/hearCallBackRing.png"  width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].isListenIn" >
                                <img src="../../../../src/assets/img/listen.png"  width="40" height="40">
                            </a-col>
                            <a-col v-if="list[index].voiceCall_out || list[index].voiceCall_in">
                                <img  src="../../../../src/assets/img/phone.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].voiceCall_out">
                                <img  src="../../../../src/assets/img/out.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].voiceCall_in">
                                <img  src="../../../../src/assets/img/in.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].videoCall_in || list[index].videoCall_out">
                                <img  src="../../../../src/assets/img/camera.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].videoCall_out">
                                <img  src="../../../../src/assets/img/out.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if="list[index].videoCall_in">
                                <img  src="../../../../src/assets/img/in.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                            <a-col v-if = "!list[index].userBusy">
                                <img  src="../../../../src/assets/img/phone2.png" alt="Smiley face" width="42" height="42">
                            </a-col>
                        </a-row>
                        <a-row>
                            <div v-if="list[index].isRing" class="status-indicator" style="width: 100%;text-align: center">
                                被叫，振铃中...
                            </div>
                            <div v-if="list[index].isHostRing" class="status-indicator" style="width: 100%;text-align: center">
                                主叫，振铃中...
                            </div>
                            <div v-if="list[index].isHostHearCallBackRing" class="status-indicator" style="width: 100%;text-align: center">
                                主叫，听回铃音...
                            </div>
                            <div v-if="list[index].isListenIn" class="status-indicator" style="width: 100%;text-align: center">
                                监听中...
                            </div>
                            <div v-if="list[index].voiceCall_out || list[index].voiceCall_in" class="status-indicator" style="width: 100%;text-align: center">
                                语音通话中...
                            </div>
                            <div v-if="list[index].videoCall_in || list[index].videoCall_out" class="status-indicator" style="width: 100%;text-align: center">
                                视频通话中...
                            </div>
                            <!-- 在线状态显示 -->
                            <div v-if="!list[index].userBusy && !list[index].isOffline" class="status-online" style="width: 100%;text-align: center;margin-top: 10%">
                                在线
                            </div>
                            <!-- 离线状态显示 -->
                            <div v-if="list[index].isOffline" class="status-offline-text" style="width: 100%;text-align: center;margin-top: 10%">
                                离线
                            </div>
                        </a-row>
                    </a-card>
                    <!-- 离线用户遮罩层 -->
                    <div v-if="list[index].isOffline" class="offline-mask">
                        <div class="offline-text">离线</div>
                    </div>
                </div>
            </a-col>
      </a-row>
    </dv-border-box-13>
    </div>
  </template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { defineComponent, ref, onMounted, watch } from 'vue'
import loding2 from '@/views/components/dispatch/loding2.vue'
import button2 from '@/views/components/dispatch/button2.vue'

export default defineComponent({
  props: ['userList', 'userStatusChange'],
  components: {
    loding2,
    button2
  },
  setup (props, { emit }) {
    const list = ref([])

    const userList = ref([])

    // 已经选择的用户数据
    const selectList = ref([])

    // 切换模式按钮的标志变量
    const changeMode = ref(false)

    // 切换显示模式按钮上的文字
    const button_name = ref('列表模式')

    /**
     * @name 点击切换显示模式的按钮
     */
    const changeShowMode = () => {
      changeMode.value = !changeMode.value
      changeShowModeButtonText()
    }

    /**
     * @name 改变切换现实模式按钮上面的文字
     */
    const changeShowModeButtonText = () => {
      if (changeMode.value) {
        button_name.value = '卡片模式'
      } else {
        button_name.value = '列表模式'
      }

      // 切换完显示后向父组件传值
      emit('bt-change-mode-click', button_name.value)
    }

    /**
     * @name 点击卡片的事件
     * @param i 点击卡片的ID，通过这个I就可以知道点击了那个卡片
     */
    const clickCard = (i) => {
      // 将点击的卡片添加到选择用户列表中
      // 当点击时卡片状态为选中状态，则需要将其置为未选中状态
      if (list.value[i].select) {
        list.value[i].select = false
        document.getElementById(i).classList.remove('card-selected')
        // 如果取消选择则也需要将对应的元素从列表中剔除
        // 单选则直接将选择的数组清空
        selectList.value.length = 0
      } else {
        // 如果原本没有选择，则点击过后是需要加入选择列表的
        // 将其他的列表中的其他用户样式取消,并将其他所有用户选择状态设为false
        for (let j = 0; j < list.value.length; j++) {
          document.getElementById(j).classList.remove('card-selected')
          list.value[j].select = false
        }
        list.value[i].select = true
        document.getElementById(i).classList.add('card-selected')

        // 将其他元素进行清空，只保留最后一个点击的用户卡片
        selectList.value.length = 0
        selectList.value.push(list.value[i])
      }
      emit('selectUser', selectList)
    }
    /**
     * @name 鼠标移动到卡片上的事件
     * @param i 鼠标放到卡片上的ID,通过这个ID就可以知道鼠标放到了那个卡片上
     */
    function mouseenterCard (i) {
      document.getElementById(i).style.borderColor = 'aqua'
      document.getElementById(i).style.borderWidth = '3px'
      // 鼠标进入时添加高亮样式
      document.getElementById(i).classList.add('highlight')
    }

    /**
     * @name 鼠标移出卡片的事件
     * @param i 鼠标移出卡片的ID，通过这个ID就可以知道鼠标从那个卡片上移出了。
     */
    function mouseleaveCard (i) {
      document.getElementById(i).style.borderColor = ''
      document.getElementById(i).style.borderWidth = ''
    }

    onMounted(() => {
      // 监听 props.userList 的变化，当 props.userList 发生变化时，更新所有卡片状态。
      watch(() => props.userList, (newValue, oldValue) => {
        list.value.length = 0
        userList.value.length = 0
        userList.value = newValue
        userList.value.forEach((element) => {
          // 用户忙线状态变量
          element.userBusy = false
          // 列表被叫振铃状态变量
          element.isRing = false
          // 列表主叫振铃状态变量
          element.isHostRing = false
          // 列表听主叫听回铃音，状态变量
          element.isHostHearCallBackRing = false
          // 列表听主叫听回铃音，状态变量
          element.isListenIn = false
          // 语音通话被叫状态变量
          element.voiceCall_in = false
          // 语音通话主叫状态变量
          element.voiceCall_out = false
          // 视频通话被叫状态变量
          element.videoCall_in = false
          // 视频通话主叫状态变量
          element.videoCall_out = false
          //  语音会议状态变量
          element.voiceMeeting = false
          // 视频会议状态变量
          element.videoMeeting = false
          // 对端号码
          element.peerNumber = ''

          // 将获取到的列表用户信息拷贝给list数组变量，这样以便于卡片视图的渲染
          list.value.push(element)

          console.log('获取到用户列表元素：=============************=============')
          console.log(element)
          // 每切换列表视图后，同步所有用户状态视图
          updateUserCardStatus(element)
        })
      })

      function updateUserCardStatus (element) {
        // 获取用户状态码
        let user_status_code = ''
        user_status_code = element.user_status_code

        // 获取对端号码
        let peer_number = ''
        peer_number = element.peer_number

        // 获取用户事件
        let user_status = ''
        user_status = element.call_status

        // 判断用户事件
        switch (user_status_code) {
          /**
             * 座机通话状态
             */
          // 振铃
          case 1:
            if (user_status.includes('发起呼叫')) {
              console.log('主叫，听回铃音')
              console.log(peer_number)
              hostRingStatusCard(element)
            } else if (user_status.includes('听回铃音')) {
              console.log('主叫，听回铃音')
              console.log(peer_number)
              hearCallBackRingStatusCard(element)
            } else if (user_status.includes('监听')) {
              console.log('监听')
              console.log(peer_number)
              listenInStatusCard(element)
            } else {
              console.log('被叫，振铃中')
              console.log(peer_number)
              ringStatusCard(element)
            }
            break
            //   case 1:
            //     console.log('振铃')
            //     console.log(peer_number)
            //     ringStatusCard(element)
            //     break
            // 被叫通话中
          case 2:
            console.log('被叫通话中')
            console.log(peer_number)
            calledOnCall(element)
            break
            // 主叫通话
          case 3:
            if (user_status.includes('监听')) {
              console.log('监听')
              console.log(peer_number)
              listenInStatusCard(element)
            } else {
              console.log('主叫通话')
              console.log(peer_number)
              callingOnCall(element)
            }
            break
            // 已注册(空闲状态)
          case 5:
            console.log('已注册')
            notBusy(element)
            break
            // 未注册
          case 6:
            console.log('未注册')
            offlineStatus(element)
            break
            // 号码不存在
          case 8:
            console.log('号码不存在')
            break
            /**
             * 座机会议状态
             */
        }
      }

      // 监听 props.userStatusChange 的变化，当 props.userStatusChange 发生变化时，更新对应卡片状态。
      watch(() => props.userStatusChange, (newValue, oldValue) => {
        console.log('123')
        // console.log(newValue)
        // 循环取出用户状态数据，更新用户卡片列表状态
        newValue.forEach(element => {
          // 当获取到用户状态改变时候，获取用户事件对象
          let user_status_jsonObj = ''
          user_status_jsonObj = element
          //   console.log('==========用户状态数据：')
          //   console.log(user_status_jsonObj)
          uploadCardDisplay(user_status_jsonObj)
        })
      })

      /**
       * 根据用户状态列表去更新卡片显示
       */
      function uploadCardDisplay (user_status_jsonObj) {
        // 获取用户UUID
        let user_uuid = ''
        user_uuid = user_status_jsonObj.uuid

        // 获取用户事件
        let user_status = ''
        user_status = user_status_jsonObj.call_status

        // 获取用户状态码
        let user_status_code = ''
        user_status_code = user_status_jsonObj.user_status_code

        // 获取用户事件描述
        let call_status = ''
        call_status = user_status_jsonObj.call_status

        // 获取对端号码
        let peer_number = ''
        peer_number = user_status_jsonObj.peer_number

        // 根据UUID获取对应卡片的用户
        let card_user = ''

        list.value.forEach(element => {
        //   console.log('==================列表=================')
        //   console.log(element)
        //   console.log('接收到的uuid')
        //   console.log(user_uuid)
          if (element.uuid === user_uuid) {
            console.log('xxxxxxxxxx找到的卡片对象xxxxxxxxxxxx')
            console.log(element)
            card_user = element
          }
        })
        console.log('*******************')
        console.log(getUserCardObjByuserUUID(user_uuid))
        console.log('获取到的数组：')
        console.log(user_status_jsonObj)
        console.log(user_status_jsonObj.call_status)
        console.log(user_status_jsonObj.uuid)
        console.log('用户UUID', user_uuid)
        console.log('用户名称', name)
        console.log('用户状态：', user_status)
        console.log('对端号码：', peer_number)
        console.log('用户状态码：', user_status_code)
        console.log('用户状态描述：', call_status)
        // 判断用户事件
        switch (user_status_code) {
          /**
             * 座机通话状态
             */
          // 振铃
          case 1:
            if (user_status.includes('发起呼叫')) {
              console.log('主叫，发起呼叫')
              console.log(peer_number)
              hostRingStatusCard(card_user)
            } else if (user_status.includes('听回铃音')) {
              console.log('主叫，听回铃音')
              console.log(peer_number)
              hearCallBackRingStatusCard(card_user)
            } else if (user_status.includes('监听')) {
              console.log('监听')
              console.log(peer_number)
              listenInStatusCard(card_user)
            } else {
              console.log('被叫，振铃中')
              console.log(peer_number)
              ringStatusCard(card_user)
            }
            break
            // 被叫通话中
          case 2:
            console.log('被叫通话中')
            console.log(peer_number)
            calledOnCall(card_user)
            break
            // 主叫通话
          case 3:
            if (user_status.includes('监听')) {
              console.log('监听')
              console.log(peer_number)
              listenInStatusCard(card_user)
            } else {
              console.log('主叫通话')
              console.log(peer_number)
              callingOnCall(card_user)
            }
            break
            // 已注册(空闲状态)
          case 5:
            console.log('已注册')
            notBusy(card_user)
            break
            // 未注册
          case 6:
            console.log('未注册')
            offlineStatus(card_user)
            break
            // 号码不存在
          case 8:
            console.log('号码不存在')
            break
            /**
             * 座机会议状态
             */
        }
      }

      /**
       * 根据获取到的UUID来获取用户卡片数据，从而根据更新对应用户卡片状态
       */
      function getUserCardObjByuserUUID (user_uuid) {
        list.value.forEach(element => {
        //   console.log('==================列表=================')
        //   console.log(element)
        //   console.log('接收到的uuid')
        //   console.log(user_uuid)
          if (element.key === user_uuid) {
            // console.log('xxxxxxxxxx找到的卡片对象xxxxxxxxxxxx')
            // console.log(element)
            return element
          }
          return element
        })
      }

      /**
       * @description 用户卡片的振铃状态
       * @param card_user 需要设振铃状态的用户卡片对象
       */
      function ringStatusCard (card_user) {
        console.log('被叫振铃操作的卡片对象', card_user)
        console.log(card_user)
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将振铃状态置为活动
        card_user.isRing = true
        card_user.userBusy = true
      }

      /**
       * @description 用户卡片的主叫，振铃状态
       * @param card_user 需要设主叫振铃状态的用户卡片对象
       */
      function hostRingStatusCard (card_user) {
        console.log('主叫,振铃操作的卡片对象', card_user)
        console.log(card_user)
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将振铃状态置为活动
        card_user.isHostRing = true
        card_user.userBusy = true
      }
      /**
       * @description 用户卡片的主叫听回铃音状态
       * @param card_user 需要设听回铃音状态的用户卡片对象
       */
      function hearCallBackRingStatusCard (card_user) {
        console.log('主叫听回铃音操作的卡片对象', card_user)
        console.log(card_user)
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将振铃状态置为活动
        card_user.isHostHearCallBackRing = true
        card_user.userBusy = true
      }

      /**
       * @description 用户卡片监听状态
       * @param card_user 需要设听监听状态的用户卡片对象
       */
      function listenInStatusCard (card_user) {
        console.log('监听操作的卡片对象', card_user)
        console.log(card_user)
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将监听状态置为活动
        card_user.isListenIn = true
        card_user.userBusy = true
      }
      /**
       * @description 用户卡片的被叫，通话中状态
       * @param card_user 需要设被叫状态的用户卡片对象
       */
      function calledOnCall (card_user) {
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将被叫通话中状态置为活动
        card_user.voiceCall_in = true
        card_user.userBusy = true
      }

      /**
       * @description 用户卡片的主叫，通话中状态
       * @param card_user 需要设主叫状态的用户卡片对象
       */
      function callingOnCall (card_user) {
        console.log('用户卡片')
        console.log(card_user)
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)

        // 将被叫通话中状态置为活动
        card_user.voiceCall_out = true
        card_user.userBusy = true
      }

      /**
       * @description 用户卡片的已注册（空闲）状态
       * @param card_user 需要设主叫状态的用户卡片对象
       */
      function notBusy (card_user) {
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)
      }

      /**
       * @description 用户卡片的未注册（离线）状态
       * @param card_user 需要设离线状态的用户卡片对象
       */
      function offlineStatus (card_user) {
        // 首先将对应卡片状态复位
        resetUserStatus(card_user)
        // 设置离线状态
        card_user.isOffline = true
      }

      /**
       * @description 在卡片设置对应状态前应需要之前所有的状态全部清除，然后再将对应状态置为活动状态。
       * @param card_user  需要复位用户状态的用户卡片对象
       */
      function resetUserStatus (card_user) {
        console.log('=============card================')
        console.log(card_user)
        card_user.userBusy = false
        // 列表被叫振铃状态变量
        card_user.isRing = false
        // 列表听主叫振铃，状态变量
        card_user.isHostRing = false
        // 列表听主叫听回铃音，状态变量
        card_user.isHostHearCallBackRing = false
        // 列表监听，状态变量
        card_user.isListenIn = false
        // 语音通话被叫状态变量
        card_user.voiceCall_in = false
        // 语音通话主叫状态变量
        card_user.voiceCall_out = false
        // 视频通话被叫状态变量
        card_user.videoCall_in = false
        // 视频通话主叫状态变量
        card_user.videoCall_out = false
        //  语音会议状态变量
        card_user.voiceMeeting = false
        // 视频会议状态变量
        card_user.videoMeeting = false
        // 离线状态变量
        card_user.isOffline = false
      }
    })
    return {
      list,
      selectList,
      loding2,
      button2,
      clickCard,
      mouseleaveCard,
      mouseenterCard,
      button_name,
      changeShowMode
    }
  }

})
</script>
<style scoped>
.back{
    padding-left: 30px;
    padding-right: 30px;
    height: 100%;
}

/* 科技感卡片样式 */
:deep(.ant-card) {
    background: linear-gradient(135deg, #0a1428 0%, #1a2332 50%, #0f1419 100%) !important;
    border: 1px solid #00d4ff !important;
    border-radius: 12px !important;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.ant-card::before) {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent) !important;
    transition: left 0.6s ease !important;
}

:deep(.ant-card:hover) {
    border-color: #00ffff !important;
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.4), 0 0 40px rgba(0, 212, 255, 0.2) !important;
    transform: translateY(-2px) !important;
}

:deep(.ant-card:hover::before) {
    left: 100% !important;
}

:deep(.ant-card-head) {
    background: linear-gradient(135deg, #1a2f5a 0%, #2d4a73 100%) !important;
    border-bottom: 1px solid #00bcd4 !important;
    border-radius: 7px 7px 0 0 !important;
}

:deep(.ant-card-head-title) {
    color: #00e5ff !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

:deep(.ant-card-body) {
    background: transparent !important;
    color: #ffffff !important;
    padding: 12px !important;
}

/* 选中状态样式 */
.card-selected {
    border: 2px solid #00ff88 !important;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.5) !important;
    background: linear-gradient(135deg, #0f4c3a 0%, #1e6b5a 100%) !important;
}

/* 状态指示器样式，与页面风格一致 */
.status-indicator {
    background: #00e676 !important;
    border-radius: 4px !important;
    color: #000 !important;
    font-weight: bold !important;
    font-size: 12px !important;
    padding: 4px 0 !important;
}

/* 在线状态样式 - 绿色框 */
.status-online {
    background: #00e676 !important;
    border: 2px solid #00c853 !important;
    border-radius: 6px !important;
    color: #000 !important;
    font-weight: bold !important;
    font-size: 12px !important;
    padding: 6px 8px !important;
    box-shadow: 0 0 8px rgba(0, 230, 118, 0.4) !important;
    animation: pulse-green 2s infinite !important;
}

/* 离线状态文字样式 - 灰色底色 */
.status-offline-text {
    background: #757575 !important;
    border: 2px solid #424242 !important;
    border-radius: 6px !important;
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 12px !important;
    padding: 6px 8px !important;
    opacity: 0.8 !important;
}

.status-offline {
    background: #ff1744 !important;
    color: #fff !important;
}

/* 图标样式 */
img {
    transition: all 0.2s ease !important;
}

.shake-animation {
    display: inline-block;
    animation: shake 0.82s infinite;
}

.search {
    width: 70%;
    margin-top: 1%;
    margin-left: 1%;
}

@keyframes shake {
    0% { transform: translateY(0); }
    20% { transform: translateY(-5px); }
    40% { transform: translateY(5px); }
    60% { transform: translateY(-5px); }
    80% { transform: translateY(5px); }
    100% { transform: translateY(0); }
}

/* 离线用户遮罩样式 */
.offline-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 15;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: all;
    cursor: not-allowed;
}

.offline-text {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
    background: rgba(117, 117, 117, 0.8);
    padding: 8px 16px;
    border-radius: 6px;
    border: 1px solid #757575;
}

/* 离线遮罩层样式 */
.offline-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10;
    border-radius: 8px;
}

/* 绿色脉冲动画 */
@keyframes pulse-green {
    0% {
        box-shadow: 0 0 8px rgba(0, 230, 118, 0.4);
    }
    50% {
        box-shadow: 0 0 15px rgba(0, 230, 118, 0.8);
    }
    100% {
        box-shadow: 0 0 8px rgba(0, 230, 118, 0.4);
    }
}
</style>
