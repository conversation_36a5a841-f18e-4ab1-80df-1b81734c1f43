class Uart {
  constructor () {
    /*
     *uart转ip数据传输功能 0：关闭comuart转ip数据传输功能 1：开启comuart转ip数据传输功能
     */
    this.enabled = null

    /*
      *串口类型：两个串口对应的命令不同 0 ：uart0 1：comuart
      */
    this.comType = 1

    /*
      *表示配置uart波特率 可选参数，字符串表示 B4800 B9600 B115200 B460800
      */
    this.baudRate = null

    /*
      *奇偶位 0 :  不设置，默认 1 : 奇数位 2 : 偶数位
      */
    this.party = null

    /*
      *停止位个数 0 :  不设置，默认 1 : 1位  2:  2位
      */
    this.stop = null

    /*
      *工作模式 1 : UDP  2: TCP CLIENT 3:  TCP server
      */
    this.mode = null

    /*
      * 表示UDP模式或TCP server本地监听端口号，或者TCP CLIENT 远端目的端口号，设置0，表示无效
      */
    this.localPort = null

    /*
      *表示UDP模式本地监听IP地址（一般填写0.0.0.0表示本地任意地址），或者TCP CLIENT 远端目的IP地址。IP地址位置为*******，表示无效。
      */
    this.localAddr = null

    /*
      *UDP模式远端目的端口号，设置0，表示无效。Tcp模式下设置无效。
      */
    this.remotePort = null

    /*
      *表示UDP模式下远端目的IP地址，IP地址位置为*******，表示无效。Tcp模式下设置无效。
      */
    this.remoteAddr = null
  }

  init () {
    /*
     *uart转ip数据传输功能 0：关闭comuart转ip数据传输功能 1：开启comuart转ip数据传输功能
     */
    this.enabled = null

    /*
       *串口类型：两个串口对应的命令不同 0 ：uart0 1：comuart
       */
    this.comType = 1

    /*
       *表示配置uart波特率 可选参数，字符串表示 B4800 B9600 B115200 B460800
       */
    this.baudRate = null

    /*
       *奇偶位 0 :  不设置，默认 1 : 奇数位 2 : 偶数位
       */
    this.party = null

    /*
       *停止位个数 0 :  不设置，默认 1 : 1位  2:  2位
       */
    this.stop = null

    /*
       *工作模式 1 : UDP  2: TCP CLIENT 3:  TCP server
       */
    this.mode = null

    /*
       * 表示UDP模式或TCP server本地监听端口号，或者TCP CLIENT 远端目的端口号，设置0，表示无效
       */
    this.localPort = null

    /*
       *表示UDP模式本地监听IP地址（一般填写0.0.0.0表示本地任意地址），或者TCP CLIENT 远端目的IP地址。IP地址位置为*******，表示无效。
       */
    this.localAddr = null

    /*
       *UDP模式远端目的端口号，设置0，表示无效。Tcp模式下设置无效。
       */
    this.remotePort = null

    /*
       *表示UDP模式下远端目的IP地址，IP地址位置为*******，表示无效。Tcp模式下设置无效。
       */
    this.remoteAddr = null
  }
}

export { Uart }
