<template>
        <div>
              <a-card title="设备版本信息">
                <a-spin :spinning="model.configData.sn===null" tip="加载中..." size="large">
                  <a-form
                  :model="model"
                  name="nest-messages"
                  layout="horizontal"
                  @finish="confirm"
                  :validate-messages="validate">
                  <a-row justify="center" >
                    <a-col span="16">
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.sn.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">{{model.configData.sn}}</a-col>
                   </a-row>
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.paversion.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">{{model.configData.apversion}}</a-col>
                   </a-row>
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.cpversion_pl.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">{{model.configData.cpversion_pl}}</a-col>
                   </a-row>
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.mac_addr.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">{{model.configData.mac_addr}}</a-col>
                   </a-row>
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.cpversion_hls.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">{{model.configData.cpversion_hls}}</a-col>
                   </a-row>
                   <a-row style="margin-top:15px">
                        <a-col :span="8" align="right">{{LabelCaption.recover.label}}:</a-col>
                        <a-col :span="8" align="left" style="margin-left:2%">
                            <a-button danger type="primary" @click="recover">恢复出厂设置</a-button>
                        </a-col>
                   </a-row>
                   </a-col>
                  </a-row>
                  <a-divider />
                  <a-row justify="space-around" >
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" @click="getDeviceinfoversion">刷新</a-button>
                    </a-form-item>
                  </a-col>
                  </a-row>

                  </a-form>
                  </a-spin>
              </a-card>
        </div>

</template>

<script>
import { onMounted, defineComponent, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { DeviceVersionInfoAction } from '@/views/deviceinfo/action/deviceVersionInfoAction'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { DeviceVersionInfo } from '../../model/DevicesVersioninfo'

export default defineComponent({
  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const deve = new DeviceVersionInfo()
    model.configData = deve
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const deviceVersionInfoAction = new DeviceVersionInfoAction()
    const pageDirectAction = new PageDirectAction()
    // const wifi_set_value = ref(2)

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
    }

    const recover = () => {
      deviceVersionInfoAction.recover(baseRequestData, getVersionSuccess, callbackError, null)
    }

    const getDeviceinfoversion = () => {
      deviceVersionInfoAction.query(baseRequestData, getVersionSuccess, callbackError, null)
    }

    const getVersionSuccess = (data) => {
      model.configData = data.data.configData
    }

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getDeviceinfoversion()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      recover,
      getDeviceinfoversion,
      deve

    }
  }
})
</script>
