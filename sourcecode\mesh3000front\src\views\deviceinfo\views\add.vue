<template>
  <div>
    <a-row >
      <a-col :span="24">
        <a-card title="设备信息新增">
          <a-row>
            <a-form
              :model="model"
              name="basic"

              layout="horizontal"
              @finish="onFinish"
              @finishFailed="onFinishFailed"
            >
              <a-row :gutter="24" wrap="false">
                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.sn.label"
                    :name="LabelCaption.sn.name"
                    :rules="[{ required: true, message: '请输入SN!' }]"
                  >
                    <a-input v-model:value="model.sn" />
                  </a-form-item>
                </a-col>

                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.device_name.label"
                    :name="LabelCaption.device_name.name"
                    :rules="[{ required: true, message: '请输入设备名称!' }]"
                  >
                    <a-input v-model:value="model.device_name" />
                  </a-form-item>
                </a-col>

                  <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.id.label"
                    :name="LabelCaption.id.name"
                                      >
                    <a-input v-model:value="model.device_no" />
                  </a-form-item>
                </a-col>

                <a-col :span="6">
                  <a-form-item
                    :label="LabelCaption.device_type.label"
                    :name="LabelCaption.device_type.name"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.device_type"
                      style="width: 100%"
                    >
                      <a-select-option
                        v-for="option in DeviceTypeOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="6">
                 <a-form-item
                   :label="LabelCaption.ip_addr.label"
                   :name="LabelCaption.ip_addr.name"
                   :rules="[{ required: true, message: '请输入IP!' },Pattern('IP')]"
                 >
                   <a-input v-model:value="model.ip_addr" />
                 </a-form-item>
               </a-col>

              </a-row>

              <a-row justify="end">
                <a-col :span="4">
                  <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
                    <a-space>
                      <a-button type="primary" html-type="submit">保存</a-button>
                      <a-button
                        type="primary"
                        @click="pageDirectAction.goToIndex()"
                        >返回</a-button
                      >
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, createVNode } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { AddAction } from '@/views/deviceinfo/action/AddAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
// import { Add } from '@/views/deviceinfo/model/Add'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'

import {
  BandWidthOptions,
  WorkModeOptions,
  TrunkOptions,
  RoutingHealthIndexOptions,
  InNetworkOptions,
  DistanceOptions,
  HDMIinOptions,
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  name: 'Deviceinfo-Add',
  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const addRequestAction = new AddAction()
    const pageDirectAction = new PageDirectAction()
    // const add = new Add()
    // model.configData = add

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // ****************** 新增 *******************
    const onFinish = (values) => {
      confirmAdd()
    }

    const onFinishFailed = (errorInfo) => {
      message.info('请填写带星的项目!')
    }

    const confirmAdd = (record) => {
      Modal.confirm({
        title: '新增设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认新增本条设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            addDeviceinfo(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const addDeviceinfoSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('新增本条设备信息成功!')
        pageDirectAction.goToIndex()
      } else {
        message.success(`新增本条设备信息失败!${data.error_code}`)
      }
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        alert('name设置成功')
      }
    }

    const addDeviceinfo = () => {
      requestAction.addOne(
        baseRequestData.value,
        addDeviceinfoSuccess,
        callbackError,
        null
      )
      addRequestAction.set(
        baseRequestData.value,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 新增 *******************

    return {
      model,
      baseParam,
      LabelCaption,
      BandWidthOptions,
      WorkModeOptions,
      TrunkOptions,
      RoutingHealthIndexOptions,
      InNetworkOptions,
      DistanceOptions,
      HDMIinOptions,
      DeviceTypeOptions,
      WfModeOptions,
      DeviceinfoColumns,
      pageDirectAction,
      onFinish,
      onFinishFailed,
      Pattern
    }
  }
})
</script>

<style scoped></style>
