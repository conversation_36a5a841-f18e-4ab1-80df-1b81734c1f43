<template>
    <a-card title="GPS设置">
     <a-row :span="24">
          <a-col :span="24">
            <a-row >
                <a-col  align="center" span="8">
                    定位开关：
                </a-col>
                <a-col  span="3">
                    <a-switch v-model:checked= "checked"  @change="onchange"  />
                </a-col>
                <!-- <a-col  align="center" span="3">
                    <a-button @click="confirm " type="primary">确定</a-button>
                </a-col> -->
            </a-row>
            <a-row style="margin-top: 3%;">
                <a-col v-show="checked" align="center" span="8">
                    定位模式：
                </a-col>
                <a-col  span="3">
                    <a-select @change="onModeChange" v-show="checked" style="width: 90%;" v-model:value="model.configData.mode" v-bind:placeholder="请选择">
                        <a-select-option v-for="option in GPSMode" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
                    </a-select>
                </a-col>
            </a-row>
          </a-col>
     </a-row>

    </a-card>
  </template>

<script>
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { GPSSetAction } from '@/views/deviceinfo/action/gpsSetAction'
// import { getGps } from '@/views/deviceinfo/action/getGps'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { Gps } from '@/views/deviceinfo/model/Gps'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
import { GPSMode } from '@/views/deviceinfo/constant/options'

export default defineComponent({
  setup () {
    // 开关控件显示
    const checked = ref(false)
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const gps = new Gps()
    model.configData = gps

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const gPSSetAction = new GPSSetAction()
    const pageDirectAction = new PageDirectAction()

    // 组件状态进行改变时候，改变model的值
    const onchange = () => {
      if (checked.value === false) {
        model.configData.type = 0
        set()
      } else model.configData.type = 1
    }

    // 模式更改时
    const onModeChange = () => {
      set()
    }

    // 当model进行更改时候，改变组件状态
    const onModel = () => {
      if (model.configData.type === 0) {
        checked.value = false
      } else {
        checked.value = true
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('GPS模式设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`GPS模式设置失败!${data.error_code}`)
      }
    }

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      gPSSetAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前GPS参数 *******************
    const getGPS = () => {
      gPSSetAction.query(baseRequestData, getGPSSuccess, callbackError, null)
    //   getGps.query(baseRequestData, getGpsSuccess, callbackError, null)
    }

    const getGPSSuccess = (data) => {
      model.configData = data.data.configData
      onModel()
    }

    // const getGpsSuccess = (data) => {
    //   console.log(data)
    // }
    // ****************** 设备当前GPS参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getGPS()
      // getDeviceinfoById()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      checked,
      GPSMode,
      onModeChange,
      onModel,
      confirm,
      Pattern,
      getGPS,
      onchange,
      AuthUi
    }
  }
})
</script>
