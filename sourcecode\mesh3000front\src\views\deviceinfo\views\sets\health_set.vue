<template>
  <div>
    <a-row>

      <a-col :span="24">

        <a-card title="温度设置">

          <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
            <a-row justify="space-around" style="margin-top:2%">
              <a-col :span="6">
                <a-form-item label="温度上报开关">
                  <a-switch v-model:checked="checked" @change="onchange" style="margin-left:10px" />
                </a-form-item>
              </a-col>

              <a-col :span="6">
                <a-form-item :label="LabelCaption.threshold.label" :name="['configData', 'temperatureThreshold']" :rules="[{ required: true, message: '请输入温度数值' },pattern=Pattern('Temp')]">
                  <a-input type="number" v-model:value="model.configData.temperatureThreshold" style="width:180px" />
                </a-form-item>
              </a-col>

              <a-col :span="6">
                <a-form-item :label="LabelCaption.voltagethreshold.label" :name="['configData', 'voltageThreshold']" :rules="[{ required: true, message: '请输入电压数值' },pattern=Pattern('Voltage')]">
                  <a-input  v-model:value="model.configData.voltageThreshold" style="width:180px" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row justify="space-around" style="margin-top:2%">
              <a-col :span="6">
                <a-form-item :label="LabelCaption.boardtemperature.label" :name="['configData', 'boardtemperature']">
                  <a-input v-model:value="model.configData.boardTemperature" disabled="ture" style="margin-left:10px;width:180px" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item :label="LabelCaption.batterytemperature.label" :name="['configData', 'batterytemperature']">
                  <a-input v-model:value="model.configData.batteryTemperature" disabled="ture" style="margin-left:10px;width:180px" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item :label="LabelCaption.voltage.label" :name="['configData', 'voltage']">
                  <a-input v-model:value="model.configData.voltage" disabled="ture" style="margin-left:10px;width:180px" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row justify="space-around" style="margin-top:2%">
              <a-col :span="6">
                <a-form-item :label="LabelCaption.remaining_battery.label" :name="['configData', 'capacity']">
                  <a-input v-model:value="model.configData.capacity" disabled="ture" style="margin-left:10px;width:180px" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
              </a-col>
              <a-col :span="6">
              </a-col>
            </a-row>
            <a-divider />
            <a-row justify="center">
              <a-col :span="2">
                <a-form-item>
                  <a-button type="primary" html-type="submit" v-if="AuthUi.温度设置.statusW">保存</a-button>
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item>
                  <a-button type="primary" @click="getHealth">刷新</a-button>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- <template #actions> -->
            <!--   <a-row type="flex" justify="end" align="top"> -->
            <!--     <a-col :span="4"> -->
            <!--       <a-space> -->
            <!--         <a-button type="primary" @click="confirm">确定</a-button> -->
            <!--       </a-space> -->
            <!--       <a-space> -->
            <!--         <a-button type="primary" @click="pageDirectAction.goToIndex()">返回</a-button> -->
            <!--       </a-space> -->
            <!--     </a-col> -->
            <!--   </a-row> -->
            <!-- </template> -->
          </a-form>

        </a-card>
      </a-col>
    </a-row>
  </div>

</template>

<script>
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { HealthActive } from '@/views/deviceinfo/action/healthActive'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { Health } from '@/views/deviceinfo/model/Health'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { ErrorInfo } from '@/common/errorInfo'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
export default defineComponent({
  setup () {
    // 开关控件显示
    const checked = ref(false)
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const health = new Health()
    model.configData = health
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const healthRequestAction = new HealthActive()
    const pageDirectAction = new PageDirectAction()
    // const wifi_set_value = ref(2)

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
    }

    const set = () => {
      healthRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('温度信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`温度信息设置失败!${data.error_code}`)
      }
    }

    const confirm = (record) => {
      /* model.configData.Channel = parseInt(model.configData.Channel, '') */
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
            onModel()
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }
    // 组件状态进行改变时候，改变model的值
    const onchange = () => {
      if (checked.value === false) {
        model.configData.enabled = 0
      } else model.configData.enabled = 1
    }

    // 当model进行更改时候，改变组件状态
    const onModel = () => {
      if (model.configData.enabled === 0) {
        checked.value = false
      } else {
        checked.value = true
      }
    }

    const getHealth = () => {
      healthRequestAction.query(baseRequestData, getHealthSuccess, callbackError, null)
    }

    const getHealthSuccess = (data) => {
      model.configData = data.data.configData
      console.info(model.configData)
    }

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getHealth()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      checked,
      AuthUi,
      Pattern,
      onModel,
      onchange,
      getHealth,
      confirm
    }
  }
})
</script>
