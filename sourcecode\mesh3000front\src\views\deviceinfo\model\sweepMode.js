class SweepMode {
  constructor () {
    /* cc_id
     * 扫频类型：0：结束扫频  1：开始仅PCC扫频  2：开始仅SCC扫频 3：开始 PCC+SCC扫频；
     */
    this.type = null
    /* threshold;
     * RSSI阀值
     */
    this.threshold = null
    /* total_duration
     *扫频过程总时长 值[0：65535]秒
     */
    this.total_duration = null
    /* pcc_tnumber
     *扫描pcc频点个数 最大64
     */
    this.pcc_tnumber = null
    /* pcc_bandwidth
     * pcc 带宽 0: 1.4M 1: 3M 2: 5M 3: 10M 5: 20M
     */
    this.pcc_bandwidth = null
    /* scc_tnumber
     *扫描scc频点个数 最大64
     */
    this.scc_tnumber = null
    /* scc_bandwidth
     *scc 带宽 0: 1.4M 1: 3M 2: 5M 3: 10M 5: 20M
     */
    this.scc_bandwidth = null

    this.pcc_freq = []
    /*
     *pcc频点总个数M1
     */
    this.m_pcc = null
    this.m_scc = null
    this.pcc_param = []
    this.scc_param = []
    /**
     * 要扫频设备的id
     */
    this.id = null
  }

  init () {
    /* cc_id
     * 扫频类型：0：结束扫频  1：开始仅PCC扫频  2：开始仅SCC扫频 3：开始 PCC+SCC扫频；
     */
    this.type = null
    /* threshold;
     * RSSI阀值
     */
    this.threshold = null
    /* total_duration
     *扫频过程总时长 值[0：65535]秒
     */
    this.total_duration = null
    /* pcc_tnumber
     *扫描pcc频点个数 最大64
     */
    this.pcc_tnumber = null
    /* pcc_bandwidth
     * pcc 带宽 0: 1.4M 1: 3M 2: 5M 3: 10M 5: 20M
     */
    this.pcc_bandwidth = null
    /* scc_tnumber
     *扫描scc频点个数 最大64
     */
    this.scc_tnumber = null
    /* scc_bandwidth
     *scc 带宽 0: 1.4M 1: 3M 2: 5M 3: 10M 5: 20M
     */
    this.scc_bandwidth = null
    this.pcc_freq = []
    /*
     *pcc频点总个数M1
     */
    this.m_pcc = null
    this.m_scc = null
    this.pcc_param = []
    this.scc_param = []
    /**
     * 要扫频设备的id
     */
    this.id = null
  }
}

export { SweepMode }
