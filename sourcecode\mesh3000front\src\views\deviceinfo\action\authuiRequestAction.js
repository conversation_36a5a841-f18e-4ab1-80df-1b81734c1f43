import baseRequest from '@/request/request'
import axios from 'axios'
class AuthUiRequestAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo/config'
    this.urladdauthui = `${this.BASE_API_URL}/addauthui`
    this.urlmodauthui = `${this.BASE_API_URL}/modauthui`
    this.urlgetauthui = `${this.BASE_API_URL}/getauthui`
    this.urldelauthui = `${this.BASE_API_URL}/delauthui`
  }

  /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    addAuthui = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      axios.post(this.urladdauthui, baseRequestData)
        .then((response) => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }

   modAuthui = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlmodauthui, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   getAuthui = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlgetauthui, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

  delAuthui = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urldelauthui, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getAuthList = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlgetAuthList, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }
}

export { AuthUiRequestAction }
