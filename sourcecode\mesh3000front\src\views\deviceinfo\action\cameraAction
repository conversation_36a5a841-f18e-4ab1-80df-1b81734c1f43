import baseRequest from '@/request/request'
class CameraRequestAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo/config/camera'
    this.urlSet = `${this.BASE_API_URL}/set`
    this.urlquery = `${this.BASE_API_URL}/query`
    this.urldelete = `${this.BASE_API_URL}/delete`
    this.urlquery_one = `${this.BASE_API_URL}/query_one`
  }

  /**
   * 设置
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   set = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlSet, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlquery, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 删除
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    delete = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      baseRequest
        .post(this.urldelete, baseRequestData)
        .then(response => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }

    /**
   * 查询单条
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
      query_one = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
        baseRequest
          .post(this.urlquery_one, baseRequestData)
          .then(response => {
            if (typeof successCallback === 'function') {
              successCallback(response.data)
            }
          })
          .catch(error => {
            if (typeof errorCallback === 'function') {
              errorCallback(error)
            }
          })
          .finally(() => {
            if (typeof finalllyCallback === 'function') {
              finalllyCallback()
            }
          })
      }
}

export { CameraRequestAction }
