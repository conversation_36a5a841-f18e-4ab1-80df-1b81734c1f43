<template>
  {{a}}
  <div id="left_mid" style="width: 100%; height: 100%;position: relative;"></div>
</template>

<script>
import { defineComponent, onMounted, watch, ref } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['deviceTotal'],
  setup (props) {
    const trafficWay = [
      {
        name: '在网设备',
        value: 30
      },
      {
        name: '离线设备',
        value: 20
      },
      { name: '在线不在网设备', value: 20 }
    ]

    const totalDev = ref(10)

    const data = []
    const color = ['#63e1f2', '#ff3000', '#fdb36a']

    let chartDom2 = null
    let myChart2 = null

    for (let i = 0; i < trafficWay.length; i++) {
      data.push(
        {
          value: trafficWay[i].value,
          name: trafficWay[i].name,
          itemStyle: {
            normal: {
              borderWidth: 18,
              shadowBlur: 20,
              borderColor: color[i],
              shadowColor: color[i]
            }
          }
        }
      )
    }

    let seriesOption = [
      {
        name: '',
        type: 'pie',
        clockWise: false,
        radius: ['80%', '80%'],
        hoverAnimation: false,
        itemStyle: {
          normal: {
            label: {
              show: true,
              position: 'outside',

              formatter: function (params) {
                // const percent = 0
                // let total = 0
                // for (let i = 0; i < trafficWay.length; i++) {
                //   total += trafficWay[i].value
                // }
                // if (params.name !== '') {
                //   return params.name + '\t' + params.value + '台'
                // } else {
                //   return ''
                // }
              }
            },
            labelLine: {
              length: 10,
              length2: 20,
              show: true,
              color: '#00ffff'
            }
          }
        },
        data: data
      }
    ]
    let option = {
      backgroundColor: '#021124',
      color: color,
      title: [{
        text: '设备总数',
        top: '35%',
        textAlign: 'center',
        left: '49.50%',
        backgroundColor: '#163253',
        borderRadius: 100,
        textStyle: {
          color: '#fff',
          fontSize: 20,
          fontWeight: '400'
        }
      }, {
        text: totalDev.value,
        top: '53%',
        textAlign: 'center',
        left: '45%',
        textStyle: {
          color: '#f6ea2f',
          fontSize: 25,
          fontWeight: '800',
          fontStyle: 'italic'
        }
      }, {
        text: '台',
        top: '53.5%',
        textAlign: 'center',
        left: '50.5%',
        textStyle: {
          color: '#fff',
          fontSize: 16,
          fontWeight: '400'
        }
      }],
      tooltip: {
        show: true
      },

      toolbox: {
        show: true
      },
      series: seriesOption
    }

    // 更新数据
    const getNewDeviceData = () => {
      for (let i = 0; i < trafficWay.length; i++) {
        data.push(
          {
            value: trafficWay[i].value,
            name: trafficWay[i].name,
            itemStyle: {
              normal: {
                borderWidth: 18,
                shadowBlur: 20,
                borderColor: color[i],
                shadowColor: color[i]
              }
            }
          }
        )
      }
      seriesOption = []
      seriesOption = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          radius: ['80%', '80%'],
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: 'outside',

                formatter: function (params) {
                // const percent = 0
                // let total = 0
                // for (let i = 0; i < trafficWay.length; i++) {
                //   total += trafficWay[i].value
                // }
                  if (params.name !== '') {
                    return params.name + '\t' + params.value + '台'
                  } else {
                    return ''
                  }
                }
              },
              labelLine: {
                length: 10,
                length2: 20,
                show: true,
                color: '#00ffff'
              }
            }
          },
          data: data
        }
      ]
      option = {
        backgroundColor: '#021124',
        color: color,
        title: [{
          text: '设备总数',
          top: '35%',
          textAlign: 'center',
          left: '49.50%',
          backgroundColor: '#163253',
          borderRadius: 100,
          textStyle: {
            color: '#fff',
            fontSize: 20,
            fontWeight: '400'
          }
        }, {
          text: totalDev.value,
          top: '53%',
          textAlign: 'center',
          left: '45%',
          textStyle: {
            color: '#f6ea2f',
            fontSize: 25,
            fontWeight: '800',
            fontStyle: 'italic'
          }
        }, {
          text: '台',
          top: '53.5%',
          textAlign: 'center',
          left: '50.5%',
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: '400'
          }
        }],
        tooltip: {
          show: true
        },

        toolbox: {
          show: true
        },
        series: seriesOption
      }
      //   console.log('--------seriesOption------------')
      //   console.log(seriesOption)
      //   console.log('--------option----------------')
      //   console.log(option)
      //   console.log(trafficWay)

      //   alert(totalDev.value)
      // 更新图表配置
      myChart2.setOption(option, true)
    }
    onMounted(() => {
    //   setTimeout(() => {
      chartDom2 = null
      myChart2 = null

      chartDom2 = document.getElementById('left_mid')
      echarts.init(chartDom2, 'dark').dispose()
      myChart2 = echarts.init(chartDom2, 'dark')
      // 初始化图表
      myChart2.setOption(option)

      // 监听 props.a 的变化，当 props.a 发生变化时，更新柱状图数据
      watch(() => props.deviceTotal, (newValue, oldValue) => {
        // 在这里根据新的 props.a 值更新柱状图数据
        totalDev.value = newValue.type0
        // alert(totalDev.value)
        trafficWay[0].value = newValue.type1
        trafficWay[1].value = newValue.type3
        trafficWay[2].value = newValue.type2
        data.length = 0
        getNewDeviceData()
      })
    //   }, 1000)
    })
  }
})
</script>
