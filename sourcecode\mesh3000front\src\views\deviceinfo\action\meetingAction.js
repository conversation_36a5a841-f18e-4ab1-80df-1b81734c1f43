import baseRequest from '@/request/request'
class MeetingAction {
  constructor () {
    this.BASE_API_URL = '/meeting'
    this.urlGetUserList = `${this.BASE_API_URL}/get_user_list`
    this.urlHoldMeeting = `${this.BASE_API_URL}/hold_meeting`
    this.urlGetMeetList = `${this.BASE_API_URL}/manager/get_meet_list`
    this.urlGetConfig = `${this.BASE_API_URL}/get_config`
  }

  /**
   * 获取用户列表响应
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  get_user_list = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetUserList, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 召开会议、预定会议
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  hold_meeting = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlHoldMeeting, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  /**
   * 获取预约会议列表响应
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  get_meet_list = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetMeetList, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  get_config = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlGetConfig, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }
}

export { MeetingAction }
