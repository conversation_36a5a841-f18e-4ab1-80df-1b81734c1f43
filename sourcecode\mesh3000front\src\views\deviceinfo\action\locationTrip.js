/**
 * 查询轨迹接口
 */
import baseRequest from '@/request/request'
class LocationTrip {
  constructor () {
    this.BASE_API_URL = '/meshdevlocation'
    this.urlquery = `${this.BASE_API_URL}/trip`
    this.urlquery2 = `${this.BASE_API_URL}/getnow`
    this.urlquery3 = `${this.BASE_API_URL}/trip_timeseg`
  }

  /**
   * 查询轨迹
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  query = (
    baseRequestData,
    successCallback,
    errorCallback,
    finalllyCallback
  ) => {
    baseRequest
      .post(this.urlquery, baseRequestData)
      .then((response) => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch((error) => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  };

   /**
   * 查询轨迹(实时)
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   query2 = (
     baseRequestData,
     successCallback,
     errorCallback,
     finalllyCallback
   ) => {
     baseRequest
       .post(this.urlquery2, baseRequestData)
       .then((response) => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch((error) => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   };

      /**
   * 查询轨迹(实时)
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
      query3 = (
        baseRequestData,
        successCallback,
        errorCallback,
        finalllyCallback
      ) => {
        baseRequest
          .post(this.urlquery3, baseRequestData)
          .then((response) => {
            if (typeof successCallback === 'function') {
              successCallback(response.data)
            }
          })
          .catch((error) => {
            if (typeof errorCallback === 'function') {
              errorCallback(error)
            }
          })
          .finally(() => {
            if (typeof finalllyCallback === 'function') {
              finalllyCallback()
            }
          })
      };
}

export { LocationTrip }
