<template>
<a-card title="Mesh设置">
    <a-form
                :model="model"
                name="nest-messages"
                layout="horizontal"
                @finish="confirm"
                :validate-messages="validate">
                <!-- 第一行 -->
                <a-row justify="space-around" align="left" :span="24" style="margin-top:2%" >
                    <!-- 跳频开关 -->
                    <a-col :span="4" >
	<a-form-item
                    :label="LabelCaption.mesh_FreqHopping.label"
                  >

                   <a-checkbox @change="changeHoppingFre" v-model:checked="model.configData.hoppingFre" />
        </a-form-item>
                    </a-col>

                     <!-- 中心频率 -->
                    <a-col :span="4">
       <a-form-item
                    :label="LabelCaption.mesh_CenterFreq.label"
                    :name="['configData', 'centerFreq']"
                  >
                    <a-select
                      ref="select"
                      @change="changeCenterFreBandOut"
                      v-model:value="model.configData.pcenterFreq"
                      v-bind:placeholder="LabelCaption.mesh_CenterFreq.label"
                      :disabled="model.configData.hoppingFre"
                    >
                      <a-select-option
                        v-for="option in CenterFreqOption"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                    </a-col>

                    <!-- 带宽 -->
                     <a-col :span="4">
                <a-form-item
                    :label="LabelCaption.mesh_Bandwidth.label"
                    :name="['configData', 'pbandWidth']"
                  >
                    <a-select
                      ref="select"
                      @change="changeCenterFreBandOut"
                      v-model:value="model.configData.pbandWidth"
                      v-bind:placeholder="LabelCaption.work_mode.label"
                    >
                      <a-select-option
                        v-for="option in BandWidthOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                    </a-col>

                    <!-- 输出功率 -->
                     <a-col :span="4" >
                <a-form-item
                    :label="LabelCaption.mesh_Outputrate.label"
                    :name="['configData', 'ptxPower']"
                    :rules="[{ required: true, message: '请输入输出功率' },Pattern('Outputrate')]"
                  >
                   <a-input type="number" @change="changePPower" v-model:value="model.configData.ptxPower" />
                  </a-form-item>
                    </a-col>
                </a-row>
                <!-- 第二行 -->
               <a-row justify="space-around" align="left" :span="24" v-show="true" style="margin-top:2%">
                    <!-- 载波聚合 -->
                    <a-col :span="4" >
		<a-form-item
                    :label="LabelCaption.mesh_CA.label"
                  >
                  <a-select
                      ref="select"
                      v-model:value="model.configData.camode"
                      v-bind:placeholder="LabelCaption.mesh_CA.label"
                      @change="changeCA"
                      :disabled=false
                    >
                      <a-select-option
                        v-for="option in CAMode"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
        </a-form-item>
                    </a-col>

                     <!-- 聚合频率 -->
                   <a-col :span="4">
       <a-form-item
                    :label="LabelCaption.mesh_CAFreq.label"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.configData.scenterFreq"
                      v-bind:placeholder="LabelCaption.mesh_CAFreq.label"
                      @change="changeCenterFreBandOut"
                      :disabled=false
                    >
                      <a-select-option
                        v-for="option in CenterFreqOption"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                    </a-col>

                     <!-- 聚合宽带 -->
                     <a-col :span="4" >
                 <a-form-item
                    :label="LabelCaption.mesh_CABandwidth.label"
                  >
                    <a-select
                    ref="select"
                    v-model:value="model.configData.sbandWidth"
                      v-bind:placeholder="LabelCaption.mesh_CABandwidth.label"
                      @change="changeCenterFreBandOut"
                      :disabled=false
                    >
                      <a-select-option
                        v-for="option in BandWidthOptions"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                    </a-col>

                     <!-- 聚合输出功率 -->
                     <a-col :span="4" >
                <a-form-item
                    :name="['configData', 'stxPower']"
                    :rules="[{ required: true, message: '请输入输出功率' },Pattern('Outputrate')]"
                    :label="LabelCaption.mesh_CAOutputrate.label"
                  >
                   <a-input  v-model:value="model.configData.stxPower"  @change="changeSPower" :disabled=fasle />
                  </a-form-item>
                    </a-col>

                </a-row>
                <!-- 第三行 -->
                <a-row justify="space-around" align="left" :span="24" style="margin-top:2%">
                 <!-- 监听模式 -->
                    <a-col :span="4" >
       <a-form-item
                    :label="LabelCaption.mesh_WorkMode.label"
                  >
                  <!-- <a-switch  v-model:checked="model.configData.workMode" -->
                    <a-checkbox @change="changelisten" v-model:checked="model.configData.workMode"/>
        </a-form-item>
                    </a-col>

                    <!-- 注册间隔 -->
                     <a-col :span="4">
                <a-form-item
                    :label="LabelCaption.mesh_RegisterPeriod.label"
                    :name="['configData', 'registerPeriod']"
                    :rules="[Pattern('RegisterPeriod')]"
                  >
                   <a-input @change="changelisten" v-model:value="model.configData.registerPeriod"/>
                  </a-form-item>
                    </a-col>

                     <!-- 距离 -->
                    <a-col :span="4">
                         <a-form-item
                    :label="LabelCaption.mesh_Distance.label"
                    :name="['configData', 'distance']"
                  >
                    <a-select
                      ref="select"
                      @change="changeRegisterDistensKey"
                      v-model:value="model.configData.distance"
                    >
                      <a-select-option
                        v-for="option in DistanceOption"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                    </a-col>

                    <!-- 空口密钥 -->
                     <a-col :span="4">
                <a-form-item
                    :label="LabelCaption.mesh_Key.label"
                    :name="['configData', 'meshKey']"
                    :rules="[{ required: true, message: '请输入空口密钥' },Pattern('MeshKey')]"
                  >
                   <a-input @change="changeRegisterDistensKey" v-model:value="model.configData.meshKey" />
                  </a-form-item>
                    </a-col>
                </a-row>

                <!-- 第四行
                <a-row justify="space-around" align="left" :span="24" style="margin-top:2%">
                 加密模式
                    <a-col :span="4" >
                      <a-form-item
                        :label="LabelCaption.mesh_encryptMode.label"
                        :name="['configData', 'distance']"
                      >
                        <a-select
                          ref="select"
                          @change="changeEncryptModeKey"
                          v-model:value="model.configData.encryptMode" >
                          <a-select-option
                            v-for="option in encryptModeOption"
                            v-bind:key="option.value"
                            :value="value"
                            >{{ option.name }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                </a-row> -->

               <a-divider />

                 <!-- 第四行 -->
                 <a-row align="left" :span="4" v-show="false" style="margin-top:2%">

                     <!-- 节点白名单开关 -->
                    <a-col :span="4" style="margin-left:4%" >
       <a-form-item
                    :label="LabelCaption.mesh_WhiteListEnabled.label"
                  >
                  <a-checkbox   :disabled=true v-model:checked="model.configData.model"/>
        </a-form-item>
                    </a-col>

                    <a-col>
                        <a-form-item v-show="model.configData.model">
                   <a-row>
                    <a-input style="width:60%"/>
                    <a-button >
                        删除记录
                    </a-button >
                   </a-row>

                  </a-form-item>
                  <a-form-item v-bind="formItemLayoutWithOutLabel">
      <a-button v-show="model.configData.model">
        添加记录
      </a-button>
    </a-form-item>
                    </a-col>
<a-divider />
                </a-row>

                <!-- 提交按钮 -->
                <a-row style="margin-left:2%">
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" html-type="submit" v-if="AuthUi.Mesh设置.statusW">保存</a-button>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" @click="getmeshSet ">刷新</a-button>
                    </a-form-item>
                  </a-col>
                   <!-- 频点规划 -->
                    <a-col :span="5" >
       <a-form-item>
                  <!-- <a-button type="primary" style="margin-left:20%" @click="showModal" disabled="true">频点规划</a-button> -->
                   <a-modal

                   :closable=false
                   okType="primary"
                   okText="确认"
                   cancelText="取消"
                    title="频点规划"
                    @ok="handleOk">
                    <a-row>
                    <a-col span=10>
                        <a-row style="margin-top:5%">
                            <a-form-item
                             :name="['configData', 'meshKey']"
                             >
                                <a-input :addon-before="LabelCaption.mesh_BeginFreq.label" addon-after="Mhz"/>
                            </a-form-item>
                        </a-row>
                        <a-row >
                            <a-col>
                            <a-form-item
                             :name="['configData', 'meshKey']"

                             >
                                <a-input :addon-before="LabelCaption.mesh_EndFreq.label" addon-after="Mhz"/>
                            </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row >
                            <a-form-item
                             :name="['configData', 'meshKey']"
                             >
                                <a-input :addon-before="LabelCaption.mesh_SpaceFreq.label" addon-after="Mhz"/>
                            </a-form-item>
                        </a-row>
                        <a-row >
                            <a-space>
                            <a-button  >批量添加</a-button>
                             <a-form-item
                             :name="['configData', 'meshKey']"
                             >
                              <a-popover
                              title="添加单条" trigger="click">
                                <template #content>
                                <a-input  v-model:value="LabelCaption.mesh_EndFreq.label"/>
                                <a-space :size="15">
                                <a @click="addCenterFreqList">确定</a>
                                <a @click="hide">取消</a>
                                </a-space>
                                </template>
                                <a-button>添加单条</a-button>
                            </a-popover>
                            </a-form-item>
                            </a-space>
                        </a-row>
                    </a-col>
                    <a-col span=10>
                       <a-card
                       size="small"
                       title="中心频率列表"
                        style="width: 200px;
                         height: 200px;
                          overflow:scroll;
 ,                      overflow-x:hidden;
                           margin-left:20%;">
                            <a-form-item
                             v-for="(n,i) in model.configData.pccfreq"
                                v-bind:key="n.value"
                                :name="['configData', 'pccfreq']">
                                <a-space>
                                {{model.configData.pccfreq[i]}}
                             <a-button type="link" @click="deletepccfreq(i)">
                        删除
                    </a-button >
                    </a-space>
                            </a-form-item>

                        </a-card>
                    </a-col>
                    </a-row>
                   </a-modal>
        </a-form-item>
                    </a-col>
                  </a-row>
                  </a-form>

</a-card>
</template>

<script>

import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { MashSetAction } from '@/views/deviceinfo/action/meshSetAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { MeshSet } from '@/views/deviceinfo/model/MeshSet'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

import {
  WfModeOptions,
  CenterFreqOption,
  BandWidthOptions,
  DistanceOption,
  encryptModeOption,
  CAMode
} from '@/views/deviceinfo/constant/options'
export default defineComponent({

  setup () {
    // 添加中心频率气泡卡片
    const visible2 = ref(false)
    // 中心频率
    const addCenterFreq = ref(0)

    const hide = () => {
      visible2.value = false
    }

    // 是否显示弹窗
    const visible = ref(false)
    // 当点击按钮后显示弹窗
    const showModal = () => {
      visible.value = true
    }
    // 点击确定后的操作
    const handleOk = e => {
      console.log(e)
      visible.value = false
    }
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const meshSet = new MeshSet()
    model.configData = meshSet
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const mashSetAction = new MashSetAction()
    const pageDirectAction = new PageDirectAction()

    // 修改跳频开关状态
    const changeHoppingFre = () => {
      model.configData.changeHoppFreq = true
    }
    // 修改中心频率、带宽、输出功率
    const changeCenterFreBandOut = () => {
      model.configData.changeCenter = true
    }
    // 主路输出功率
    const changePPower = () => {
      model.configData.changePPower = true
    }
    // 辅路输出功率
    const changeSPower = () => {
      model.configData.changeSPower = true
    }
    // 修改CA的时候将changeCA变量变为true
    const changeCA = () => {
      model.configData.changeAC = true
      // 改变的是本地的ca节点，仅对本地节点生效
      model.configData.catype = 0
      // 改变的是全网的ca节点，对全网节点生效
      // model.configData.changeCA.catype = 0
    }
    // 修改监听模式
    const changelisten = () => {
      model.configData.changeListen = true
    }
    // 修改注册间隔、距离、空口秘钥
    const changeRegisterDistensKey = () => {
      model.configData.changeRegister = true
    }
    // 修改加密方式
    const changeEncryptModeKey = () => {
      model.configData.changeEncrypt = true
      console.info(model.configData.encryptMode)
    }
    // 获取跳频状态
    const getHoppingFre = () => {
      if (model.configData.hoppingFre === 1) {
        // eslint-disable-next-line no-const-assign
        model.configData.hoppingFre = true
      } else {
        model.configData.hoppingFre = false
      }
    }

    // 设定跳频状态
    const setHoppingFre = () => {
      if (model.configData.hoppingFre === true) {
        // eslint-disable-next-line no-const-assign
        model.configData.hoppingFre = 1
      } else {
        model.configData.hoppingFre = 0
      }
    }
    // ****************** 添加一条记录 *******************
    const addCenterFreqList = () => {
    // 如果第一次添加则自动创建一条记录
      if (model.configData.pccfreq == null) {
        model.configData.pccfreq = []
        model.configData.pccfreq.push(Number(addCenterFreq))
      } else {
        model.configData.pccfreq.push(Number(addCenterFreq))
        // console.log('添加了一条记录')
      }
      visible2.value = false
    }

    // ****************** 删除一条记录 *******************
    const deletepccfreq = (i) => {
      model.configData.pccfreq.splice(i, 1)
    //   console.log('删除了一条记录')
    }

    // 获取监听模式状态
    const getWorkMode = () => {
      if (model.configData.workMode === 1) {
        // eslint-disable-next-line no-const-assign
        model.configData.workMode = true
      } else {
        model.configData.workMode = false
      }
    }

    // 设定监听模式状态
    const setWorkMode = () => {
      if (model.configData.workMode === true) {
        // eslint-disable-next-line no-const-assign
        model.configData.workMode = 1
      } else {
        model.configData.workMode = 0
      }
    }

    // 如果registerPeriod为null或0则将其设定为10
    const minRegisterPeriod = () => {
      if (model.configData.registerPeriod === null || model.configData.registerPeriod === 0) {
        // eslint-disable-next-line no-const-assign
        model.configData.registerPeriod = 10
      }
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
            setHoppingFre()
            setWorkMode()
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('设备信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置设备信息信息失败!${data.error_code}`)
      }
    }

    const set = () => {
    //  model.configData.Enabled === true ? 1 : 0
      mashSetAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前mesh参数 *******************
    const getmeshSet = () => {
      mashSetAction.query(baseRequestData, getmeshSetSuccess, callbackError, null)
    }

    const getmeshSetSuccess = (data) => {
      model.configData = data.data.configData
      // this.model = data.data
      //   console.log(model.configData.NetFilterType)
      //     console.log(data.data.configData)
      // 如果获取到的trunk与Syngp为null则将两个值设为0
      if (model.configData.trunk === null) {
        model.configData.trunk = 0
      }
      if (model.configData.syngp === null) {
        model.configData.syngp = 1
      }
      model.configData.powerMode = 1
      minRegisterPeriod()
    }
    // ****************** 设备当前mesh参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getmeshSet()
      // getDeviceinfoById()
    })

    const onFinish = (values) => {
      console.log('Success:', values)
    }

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      WfModeOptions,
      CenterFreqOption,
      BandWidthOptions,
      DistanceOption,
      encryptModeOption,
      visible,
      visible2,
      addCenterFreq,
      CAMode,
      AuthUi,
      changePPower,
      changeSPower,
      changeRegisterDistensKey,
      changeEncryptModeKey,
      changelisten,
      changeCenterFreBandOut,
      changeHoppingFre,
      addCenterFreqList,
      hide,
      deletepccfreq,
      handleOk,
      showModal,
      minRegisterPeriod,
      setHoppingFre,
      getHoppingFre,
      getWorkMode,
      setWorkMode,
      confirm,
      Pattern,
      onFinish,
      getmeshSet,
      changeCA
    }
  }
})
</script>
