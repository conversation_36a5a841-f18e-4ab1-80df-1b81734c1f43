<template>
<a-card title="扫频" style="height:100%">

    <a-row>
            <a-col span="6">
            <a-row  style="margin-top: 2%;">
                <a-col :span="8">
                    阈值(dbm)：
                </a-col>
                <a-col :span="16">
                    <a-input  :disabled="iconLoading" placeholder="阈值默认为-88dbm " v-model:value="model.configData.threshold"></a-input>
                </a-col>
            </a-row>
            <a-row style="margin-top: 2%;">
                <a-col :span="8">
                    节点：
                </a-col>
                <a-col :span="16">
                    <a-select style="width: 100%;" :disabled="iconLoading" placeholder="请选择节点" v-model:value="model.configData.id" @change = "deviceChange()">
                        <a-select-option v-for="option in Device" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
                    </a-select>
                </a-col>
            </a-row>
            <a-row style="margin-top: 2%;">
                <a-col :span="8">
                    带宽(Mhz)：
                </a-col>
                <a-col :span="16">
                    <a-select style="width: 100%;" :disabled="iconLoading" placeholder="请选择带宽"  v-model:value="model.configData.pcc_bandwidth">
                        <a-select-option v-for="option in SweepBandWidth" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
                    </a-select>
                </a-col>
            </a-row>
            <a-row style="margin-top: 6%;">
                <a-col :span="12" align="center">
                    <a-button type="primary" :loading="iconLoading" @click="enterIconLoading" :disabled="rowKeysStatus.length === 0" >扫描</a-button>
                </a-col>
                <a-col :span="12" align="center">
                    <a-button @click="stopSweep()" :span="12" :disabled="!iconLoading">停止扫描</a-button>
                </a-col>
            </a-row>
            <a-row style="margin-top: 6%;">
                <a-table
                :bordered="true"
                :scroll="{ y: 380 }"
                :pagination="false"
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :columns="columns"
                :data-source="frequencyData"
                >
                <template #title>频点选择列表</template>
            </a-table>
            </a-row>
            </a-col>
            <a-col :span="18">
                <a-row style="height: 50%;">
                    <a-col :span="12" align="center">
                        <div id="main_id" style="width: 90%; height: 90%;"></div>
                    </a-col>
                    <a-col :span="12" align="center">
                        <div id="main_id3" style="width: 90%; height: 90%;"></div>
                    </a-col>
                </a-row>
                <a-row style="height: 50%; margin-top: 3%;">
                    <a-col :span="12" align="center">
                        <div id="main_id2" style="width: 90%; height: 90%;"></div>
                    </a-col>
                    <a-col :span="12" align="center">
                        <div id="main_id4" style="width: 90%; height: 90%;"></div>
                    </a-col>
                </a-row>

            </a-col>
        </a-row>
</a-card>
</template>

<script>

import { onMounted, computed, toRefs, defineComponent, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
import * as echarts from 'echarts'

import { SweepBandWidth } from '@/views/deviceinfo/constant/options'
import { SweepMode } from '../../model/sweepMode'
import { SweepAction } from '../../action/sweepAction'
const columns = [
  {
    title: '频点',
    dataIndex: 'name',
    align: 'center',
    width: 300
  }
]
const frequencyData = [
  { key: 0, value: 14300, name: '1430' },
  { key: 1, value: 14400, name: '1440' },
  { key: 2, value: 14500, name: '1450' },
  { key: 3, value: 14600, name: '1460' },
  { key: 4, value: 14700, name: '1470' },
  { key: 5, value: 14800, name: '1480' },
  { key: 6, value: 14900, name: '1490' },
  { key: 7, value: 15000, name: '1500' },
  { key: 8, value: 15100, name: '1510' },
  { key: 9, value: 15200, name: '1520' }
]

// for (let i = 0; i < 50; i++) {
//   data.push({
//     key: i,
//     name: `1490${i}`
//   })
// }
export default defineComponent({

  setup () {
    let option = ({})
    let frequency = {}
    let option1 = ({})
    let frequency1 = {}
    const getListInterval = []
    const selectFreq = []
    const pccParamListShowTotal = []
    const sccParamListShowTotal = []
    const pccParamListShow = []
    const sccParamListShow = []
    const pccInterfereShow = []
    const sccInterfereShow = []
    const pccParamListShow2 = []
    const sccParamListShow2 = []
    const pccInterfereShow2 = []
    const sccInterfereShow2 = []
    const rowKeysStatus = ref([])
    option = {
      backgroundColor: '#162130ce',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '10%',
        bottom: '3%',
        containLabel: true
      },
      yAxis: {
        show: true,
        type: 'value',
        inverse: true,
        name: '底噪',
        nameLocation: 'start'
      },
      xAxis: {
        type: 'category',
        data: [],
        name: '频率',
        nameLocation: 'end'
      },
      series: [
        {
          name: 'PCC天线',
          type: 'line',
          inverse: true,
          smooth: true,
          data: pccParamListShow
        },
        {
          name: 'SCC天线',
          type: 'line',
          smooth: true,
          data: sccParamListShow
        }
      ]
    }
    option1 = {
      backgroundColor: '#162130ce',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '10%',
        bottom: '3%',
        containLabel: true
      },
      yAxis: {
        show: true,
        type: 'value',
        inverse: true,
        name: '底噪',
        nameLocation: 'start'
      },
      xAxis: {
        type: 'category',
        data: [],
        name: '频率',
        nameLocation: 'end'
      },
      series: [
        {
          name: 'PCC天线',
          type: 'line',
          inverse: true,
          smooth: true,
          data: pccParamListShow2
        },
        {
          name: 'SCC天线',
          type: 'line',
          smooth: true,
          data: sccParamListShow2
        }
      ]
    }
    frequency = {
      backgroundColor: '#102030',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '10%',
        bottom: '5%',
        containLabel: true
      },
      yAxis: {
        show: true,
        type: 'value',
        inverse: false,
        name: '突发干扰比',
        nameLocation: 'start'
      },
      xAxis: {
        type: 'category',
        data: [],
        name: '频率',
        nameLocation: 'end'
      },
      series: [
        {
          name: 'PCC突发干扰比',
          type: 'line',
          inverse: false,
          data: pccInterfereShow,
          smooth: true
        },
        {
          name: 'SCC突发干扰比',
          type: 'line',
          data: sccInterfereShow,
          smooth: true
        }
      ]
    }
    frequency1 = {
      backgroundColor: '#102030',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '10%',
        bottom: '5%',
        containLabel: true
      },
      yAxis: {
        show: true,
        type: 'value',
        inverse: false,
        name: '突发干扰比',
        nameLocation: 'start'
      },
      xAxis: {
        type: 'category',
        data: [],
        name: '频率',
        nameLocation: 'end'
      },
      series: [
        {
          name: 'PCC突发干扰比',
          type: 'line',
          inverse: false,
          data: pccInterfereShow2,
          smooth: true
        },
        {
          name: 'SCC突发干扰比',
          type: 'line',
          data: sccInterfereShow2,
          smooth: true
        }
      ]
    }

    const Device = ref([])

    const iconLoading = ref(false)
    const enterIconLoading = () => {
      sweep()
      getListInterval.value = setInterval(function () {
        // 在请求前先确定路由是否为当前扫频页面
        const routerPage = window.location.href
        if (routerPage === null) {
          alert('未知错误')
        }
        if (routerPage.indexOf('sweep_set') !== -1) {
          getSweep()
          drawChart()
        } else {
          // 如果退出页面则直接清掉定时器
          clearInterval(getListInterval.value)
        }
      }, 3000)
      iconLoading.value = {
        delay: 0
      }
      setTimeout(() => {
        iconLoading.value = false
        clearInterval(getListInterval.value)
      }, 60000)
    }

    const state = reactive({
      selectedRowKeys: [],
      // Check here to configure the default column
      loading: false
    })
    const hasSelected = computed(() => state.selectedRowKeys.length > 0)
    const start = () => {
      state.loading = true
      // ajax request after empty completing
      setTimeout(() => {
        state.loading = false
        state.selectedRowKeys = []
      }, 1000)
    }
    const onSelectChange = selectedRowKeys => {
      // 清空暂存的频点列表
      selectFreq.length = 0
      console.log('selectedRowKeys changed: ', selectedRowKeys)
      for (let i = 0; i < selectedRowKeys.length; i++) {
        selectFreq[i] = frequencyData[selectedRowKeys[i]].value
      }
      state.selectedRowKeys = selectedRowKeys
      rowKeysStatus.value = selectedRowKeys
      option.xAxis.data = selectFreq
      frequency.xAxis.data = selectFreq
      option1.xAxis.data = selectFreq
      frequency1.xAxis.data = selectFreq
      console.log(selectFreq)
    }

    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const sweepMode = new SweepMode()
    model.configData = sweepMode
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const sweepAction = new SweepAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    const setSweepSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('开始扫频')
        // pageDirectAction.goToIndex()
      }
    }

    const setSweep = () => {
    //  model.configData.Enabled === true ? 1 : 0
      sweepAction.set(
        baseRequestData,
        setSweepSuccess,
        callbackError,
        null
      )
    }

    const deviceChange = () => {
    }

    // 获取扫频结果
    const getSweepResult = (data) => {
      const pccParamList = data.data.configData.pcc_param
      const sccParamList = data.data.configData.scc_param
      // 获取pcc数据
      if (pccParamList !== null) {
        // 将null元素剔除
        for (let i = 0; i < pccParamList.length; i++) {
          if (pccParamList[i] !== null) {
            pccParamListShowTotal[i] = pccParamList[i].split('|')
            pccParamListShow[i] = pccParamListShowTotal[i][1]
            pccParamListShow2[i] = pccParamListShowTotal[i][2]
            pccInterfereShow[i] = pccParamListShowTotal[i][3]
            pccInterfereShow2[i] = pccParamListShowTotal[i][4]
          }
        }
      }
      // 获取cc数据
      if (sccParamList !== null) {
        // 将null元素剔除
        for (let i = 0; i < sccParamList.length; i++) {
          if (sccParamList[i] !== null) {
            sccParamListShowTotal[i] = sccParamList[i].split('|')
            sccParamListShow[i] = sccParamListShowTotal[i][1]
            sccParamListShow2[i] = sccParamListShowTotal[i][2]
            sccInterfereShow[i] = sccParamListShowTotal[i][3]
            sccInterfereShow2[i] = sccParamListShowTotal[i][4]
          }
        }
        console.log(sccParamListShow)
      }
    }
    // 绘制图表的方法
    const drawChart = () => {
      // 当获取到的数据不为空的时候再进行渲染
      if (pccParamListShow.length !== 0 && pccParamListShow2.length !== 0) {
        const chartDom = window.document.getElementById('main_id')
        const chartDom2 = window.document.getElementById('main_id2')
        const chartDom3 = window.document.getElementById('main_id3')
        const chartDom4 = window.document.getElementById('main_id4')
        const myChart = echarts.init(chartDom, 'dark')
        const myChart2 = echarts.init(chartDom2, 'dark')
        const myChart3 = echarts.init(chartDom3, 'dark')
        const myChart4 = echarts.init(chartDom4, 'dark')
        option && myChart.setOption(option)
        option && myChart2.setOption(frequency)
        option && myChart3.setOption(option1)
        option && myChart4.setOption(frequency1)
      }
    }
    const sweep = () => {
      model.configData.type = 1
      model.configData.total_duration = 60
      model.configData.pcc_freq = selectFreq
      model.configData.scc_freq = selectFreq
      model.configData.pcc_tnumber = model.configData.pcc_freq.length
      model.configData.scc_tnumber = model.configData.scc_freq.length
      model.id = model.configData.id

      //   for (let i = 0; i < state.selectedRowKeys.length; i++) {
      //     model.configData.pcc_freq[i] =
      //     console.log('selectedRowKeys changed: ', model.configData.pcc_freq)
      //   }
      // 将类型设备默认PCC+SCC扫频
      model.configData.type = 3

      // 扫描过程总时长默认为45S
      model.configData.total_duration = 45
      setSweep()
      getSweep()
    }

    const getSweep = () => {
      sweepAction.query(baseRequestData, getSweepResult, callbackError, null)
    }

    const stopSweep = () => {
      model.configData.type = 0
      setSweep()
      iconLoading.value = false
      clearInterval(getListInterval.value)
    }

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListFinally = () => {

    }

    const getListSuccess = (data) => {
      Device.value.length = 0
      for (let i = 0; i < data.data.content.length; i++) {
        if (data.data.content[i].in_network !== 2 && data.data.content[i].in_network !== null) {
          Device.value.push({
            name: data.data.content[i].device_name,
            value: data.data.content[i].id
          })
        }
      }
      // 将默认选择的节点设置为列表第一项
      if (Device.value.length > 0) {
        model.configData.id = Device.value[0].value
        model.id = model.configData.id
      }

      // 将带宽设置为默认20M
      model.configData.pcc_bandwidth = 5
      model.configData.scc_bandwidth = 5

      // 将阈值设为默认-88
      model.configData.threshold = -88
    }

    // ****************** 设备当前mesh参数 *******************

    onMounted(() => {
      selectFreq.length = 0
      pccParamListShowTotal.length = 0
      sccParamListShowTotal.length = 0
      pccParamListShow.length = 0
      sccParamListShow.length = 0
      pccInterfereShow.length = 0
      sccInterfereShow.length = 0
      pccParamListShow2.length = 0
      sccParamListShow2.length = 0
      pccInterfereShow2.length = 0
      sccInterfereShow2.length = 0
      clearInterval(getListInterval.value)
      getList()
    })

    return {
      loading: ref(false),
      iconLoading,
      enterIconLoading,
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      SweepBandWidth,
      AuthUi,
      Device,
      sweep,
      getSweep,
      confirm,
      stopSweep,
      Pattern,
      frequencyData,
      columns,
      hasSelected,
      rowKeysStatus,
      ...toRefs(state),
      // func
      start,
      deviceChange,
      onSelectChange
    }
  }
})
</script>
