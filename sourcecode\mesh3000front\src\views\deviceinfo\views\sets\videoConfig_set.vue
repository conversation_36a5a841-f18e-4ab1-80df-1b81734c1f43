<template>
        <div>
          <a-row>

            <a-col :span="24">

              <a-card title="视频参数设置">

                  <a-form
                  :model="model"
                  name="nest-messages"
                  layout="horizontal"
                  @finish="confirm"
                  :validate-messages="validate">

                  <a-row justify="space-around">
                <a-card size="small"   style="width: 500px" >
                <a-row justify="space-around" >

                <a-form-item
                  label="分辨率"
                  :name="['configData', 'Resolution']"
                >
                  <a-select
                    ref="select"
                    v-model:value="model.configData.Resolution"
                    style="width:300px;margin-left:8%"
                  >
                    <a-select-option
                      v-for="option in Resolution"
                      v-bind:key="option.value"
                      :value="value"
                      >{{ option.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>

                </a-row>

                  <a-row justify="space-around" >

                 <!-- <a-form-item
                   label="帧率"
                   :name="['configData', 'Framerate']"
                 >
                   <a-select
                     ref="select"
                     v-model:value="model.configData.Framerate"
                     style="width:300px;margin-left:10%"
                   >
                     <a-select-option
                       v-for="option in Framerate"
                       v-bind:key="option.value"
                       :value="value"
                       >{{ option.name }}</a-select-option
                     >
                   </a-select>
                 </a-form-item> -->

                </a-row>

                <a-row justify="space-around" >

                <a-form-item
                  label="编码"
                  :name="['configData', 'Encoding']"
                >
                  <a-select
                    ref="select"
                    v-model:value="model.configData.Encoding"
                    style="width:300px;margin-left:10%"
                  >
                    <a-select-option
                      v-for="option in Encoding"
                      v-bind:key="option.value"
                      :value="value"
                      >{{ option.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>

                </a-row>
                 <a-row justify="space-around" >
                  <a-form-item
                    label="视频传输录像"
                    :name="['configData', 'Appmode']"
                  >
                    <a-select
                      ref="select"
                      v-model:value="model.configData.Appmode"
                      style="width:300px"
                    >
                      <a-select-option
                        v-for="option in AppMode"
                        v-bind:key="option.value"
                        :value="value"
                        >{{ option.name }}</a-select-option
                      >
                    </a-select>
                  </a-form-item>

                  </a-row>
                 </a-card>
                 </a-row>

                 <a-row justify="center" style="margin-top:2%">

                  <a-form-item label="视频采集">
                    <a-switch v-model:checked="checked" @change="onchange" style="margin-left:2%"/>
                        </a-form-item>

                         <a-form-item label="HDMI输入" style="margin-left:5%">
                        <a-radio v-model:value="model.configData.HdmiIn" disabled="true"></a-radio>
                        </a-form-item>
                  </a-row>

                <a-row justify="center">
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" html-type="submit">保存</a-button>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <a-form-item >
                    <a-button type="primary" @click="getVideoConfig">刷新</a-button>
                    </a-form-item>
                  </a-col>
                </a-row>

                  <!-- <template #actions> -->
                  <!--   <a-row type="flex" justify="end" align="top"> -->
                  <!--     <a-col :span="4"> -->
                  <!--       <a-space> -->
                  <!--         <a-button type="primary" @click="confirm">确定</a-button> -->
                  <!--       </a-space> -->
                  <!--       <a-space> -->
                  <!--         <a-button type="primary" @click="pageDirectAction.goToIndex()">返回</a-button> -->
                  <!--       </a-space> -->
                  <!--     </a-col> -->
                  <!--   </a-row> -->
                  <!-- </template> -->
                  </a-form>

              </a-card>
            </a-col>
          </a-row>
        </div>

</template>

<script>
import { onMounted, defineComponent, createVNode, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { VideoConfigRequestAction } from '@/views/deviceinfo/action/videoConfigRequestAction'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { Video } from '@/views/deviceinfo/model/Video'
import { QuestionOutlined } from '@ant-design/icons-vue'
import { ErrorInfo } from '@/common/errorInfo'
import {
  Encoding,
  Resolution,
  VideoMode,
  AppMode,
  Framerate
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  setup () {
    // 开关控件显示
    const checked = ref(false)
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const video = new Video()
    model.configData = video
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const videoConfigRequestAction = new VideoConfigRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
    }

    const set = () => {
      videoConfigRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('视频信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置视频信息失败!${data.error_code}`)
      }
    }

    // 组件状态进行改变时候，改变model的值
    const onchange = () => {
      if (checked.value === false) {
        model.configData.Mode = 0
      } else model.configData.Mode = 1
    }

    // 当model进行更改时候，改变组件状态
    const onModel = () => {
      if (model.configData.Mode === 0) {
        checked.value = false
      } else {
        checked.value = true
      }
    }

    const confirm = (record) => {
      /* model.configData.Channel = parseInt(model.configData.Channel, '') */
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve) => {
            resolve()
            set(record)
            onModel()
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const getVideoConfig = () => {
      videoConfigRequestAction.query(baseRequestData, getVideoCongifSuccess, callbackError, null)
    }

    const getVideoCongifSuccess = (data) => {
      model.configData = data.data.configData
      console.info(model.configData)
    }

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getVideoConfig()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      video,
      Encoding,
      Resolution,
      VideoMode,
      AppMode,
      Framerate,
      checked,
      onModel,
      onchange,
      getVideoConfig,
      confirm
    }
  }
})
</script>
