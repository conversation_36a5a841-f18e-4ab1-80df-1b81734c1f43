<template>
    <div style="position: absolute; width: 100%; height: 100%; background-color: #162130ef;">
        <div id="myChart" style="width:99%;height: 99%;position : absolute"></div>
    </div>
  <!-- <a-space>
                        <a-button type="primary" @click="draw()">draw</a-button>
                      </a-space>
                      <a-space>
                        <a-button type="primary" @click="drawClear()">clear</a-button>
                      </a-space> -->
  <!-- <a-space>
                        <a-button type="primary" @click="draw_repeat()">draw_repeat</a-button>
                      </a-space> -->
  <!-- <a-space>
                        <a-button type="primary" @click="clear_repeat()">clear_repeat</a-button>
                      </a-space> -->

  <a-drawer id="drawer1" height="30%" title="设备详细信息" placement="bottom" :visible="bottom_visible" @close="detail_onClose">
    <div id="drawer" >
      <a-descriptions :bordered="true" :column=5>
        <a-descriptions-item label="设备类型" style="background-color: #162130ef;">{{drawer_data.device_type}}</a-descriptions-item>
        <a-descriptions-item label="MAC地址" style="background-color: #162130ef;">{{drawer_data.mac_addr}}</a-descriptions-item>
        <a-descriptions-item label="工作模式" style="background-color: #162130ef;">{{drawer_data.work_mode}}</a-descriptions-item>
        <a-descriptions-item label="电池电压" style="background-color: #162130ef;">{{drawer_data.voltage}}</a-descriptions-item>
        <a-descriptions-item label="剩余电量" style="background-color: #162130ef;" :span="2">{{drawer_data.remaining_battery}}</a-descriptions-item>
        <a-descriptions-item label="电池温度" style="background-color: #162130ef;">{{drawer_data.batterytemperature}}</a-descriptions-item>
        <a-descriptions-item label="主板温度" style="background-color: #162130ef;">{{drawer_data.hosttemperature}}</a-descriptions-item>
        <a-descriptions-item label="经纬度" style="background-color: #162130ef;">{{drawer_data.longitude}},{{drawer_data.latitude}}</a-descriptions-item>
      </a-descriptions>

    </div>
  </a-drawer>

</template>

<script>
import { onMounted, onUnmounted, defineComponent, ref, reactive, toRefs } from 'vue'
// import { Modal, message } from 'ant-design-vue'
import { message } from 'ant-design-vue'
// import { QuestionOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
// import { DhcpRequestAction } from '@/views/deviceinfo/action/dhcpRequestAction'
import { TopoRequestAction } from '@/views/deviceinfo/action/topoRequestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'

import * as echarts from 'echarts'
import { TitleComponent, TooltipComponent } from 'echarts/components'
import { GraphChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'

export default defineComponent({
  name: 'Topo_draw',
  setup () {
    echarts.use([TitleComponent, TooltipComponent, GraphChart, CanvasRenderer])
    // 对应后台数据表
    let model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    model.configData = 'json'
    let repeat_count = 5

    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const topoRequestAction = new TopoRequestAction()
    const pageDirectAction = new PageDirectAction()
    const ico_red = require('@/assets/img/red.png')
    const ico_green = require('@/assets/img/green.png')
    const ico_blue = require('@/assets/img/blue.png')
    const state = reactive({
      checked1: false,
      checked2: false,
      checked3: false
    })
    const bottom_visible = ref(false)

    const drawer_data = ref({})
    let myChart = null
    let option = null
    let node_count = 0
    let onLine_node_count = 0
    let node_x = 0
    let node_y = 0
    const radis = 200
    const left_margin = 60
    const top_margin = 60
    let nodes = []
    // let json = {
    //   content: [{ name: 'Node 1' }, { name: 'Node 2' }, { name: 'Node 3' }, { name: 'Node 4' },
    //     { name: 'Node 5' }, { name: 'Node 6' }, { name: 'Node 7' }, { name: 'Node 8' }]
    // }
    let s1_nodes = []
    let s2_nodes = []
    let s_nodes = []
    let s_links = []
    let snNameList = []
    let repeat = 0
    // let line_style

    // const echart = new Echarts()

    const enter_repeat_count = (data) => {
      console.info('enter_repeat_count:' + data)
      repeat_count = data
      clear_repeat()
      draw_repeat()
    }

    // 回调函数错误处理
    const callbackError = () => {
      message.error('数据异常，请重试', 3)
    }
    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data
        // getDhcp()
        preDraw()
        getTopo()
      }
    }

    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }

    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }

    const getTopo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      topoRequestAction.query(baseRequestData, getTopoSuccess, callbackError, null)
    }

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListFinally = () => {
      console.log('getListFinally')
    }

    const getListSuccess = (data) => {
      console.log(data.data.content[0])

      model.id = data.data.content[0].id
      console.info('model.id:' + model.id)
      getDeviceinfoById()
    }

    const getNameBySn = (data) => {
      let name = null
      snNameList.forEach((item, index, arr) => {
        if (item.sn === data) {
          name = item.device_name
        }
      })
      return name
    }

    const getTopoSuccess = (data) => {
      // model.configData = data.data.configData
      // this.model = data.data
      // const obj = model.configData
      const obj = data.data.configData
      console.info(data.data.configData)
      nodes = []
      s_nodes = []
      s1_nodes = []
      s2_nodes = []
      s_links = []
      snNameList = []
      onLine_node_count = 0
      let t_lineStyle = {}
      let t_color = '#000066'
      let t_width = 2
      // console.info(obj[5566])

      // console.info(obj.length)

      // console.info(Object.keys(obj)[0])
      // console.info(obj[Object.keys(obj)[0]])

      // for (const key in obj) {
      //   console.info(Object.keys(obj)[key])
      //   console.info(obj[Object.keys(obj)[key]])
      // }
      t_lineStyle = {
        width: t_width,
        curveness: 0.2,
        color: t_color
      }

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].sn != null) {
          const strtmp = obj[key].sn
          let str = ''
          for (let i = 0; i < strtmp.length; i++) {
            if ((strtmp[i] >= '0') && (strtmp[i] <= '9')) {
              str += strtmp[i]
            }
          }
          obj[key].sn = str
          snNameList.push({
            sn: obj[key].sn,
            device_name: obj[key].device_name
          })
          if (obj[key].in_network === 1) {
            s1_nodes.push(obj[key])
          } else {
            s2_nodes.push(obj[key])
          }
        }
      })

      s_nodes = s1_nodes.concat(s2_nodes)

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].in_network === 1) {
          onLine_node_count++
          console.info('sn[' + obj[key].sn + '] 在线！')
          // 寻找邻接节点，并放到nodes队列
          const adjacentNode = obj[key].adjacentNodeSNR
          if (adjacentNode.AdjacentNodeNumber > 0) {
            // for(let i=0;i<adjacentNode.)
            adjacentNode.linkCharacteristics.forEach((item, index, arr) => {
              console.info(item)
              const snr = (item.ANT1SNR + item.ANT2SNR) / 2
              if (snr > 20) {
                t_color = 'green'
              } else if (snr < 10) {
                t_color = 'red'
              } else {
                t_color = 'orange'
              }

              if (item.Distance > 50) {
                t_width = 2
              } else {
                t_width = 4
              }

              t_lineStyle = {
                width: t_width,
                curveness: 0.2,
                color: t_color
              }
              // console.info('id: ' + item.id + ' sn: ' + item.sn)
              if (item.AdjacentNodeSn != null) {
                // 离线节点不画连接线
                let if_push = true
                s2_nodes.forEach((item1, index, arr) => {
                  if (item1.sn === item.AdjacentNodeSn) {
                    if_push = false
                  }
                })

                if (if_push === true) {
                  s_links.push({
                  // source: item.SN,
                  // target: item.AdjacentNodeSn,
                    source: getNameBySn(item.SN),
                    target: getNameBySn(item.AdjacentNodeSn),
                    symbolSize: [1, 10],
                    label: {
                      show: false
                    },
                    lineStyle: t_lineStyle,
                    s_data: item
                  })
                }
              }
            })
          }
        } else {
          console.info('sn[' + obj[key].sn + '] 离线！')
        }
      })

      console.info('s_nodes: ' + JSON.stringify(s_nodes))
      console.info('s_links: ' + JSON.stringify(s_links))

      draw()

      // 插入到nodes队列
      // model.configData.forEach((item, index, arr) => {
      //   console.info(item)
      //   // console.info('id: ' + item.id + ' sn: ' + item.sn)
      // })
    }

    const drawClear = () => {
      option = {

        series: [
          {

            data: null,
            links: []
          }
        ]
      }
      option && myChart.setOption(option)

      // myChart.setOption(option)
    }

    const preDraw = () => {
      myChart.setOption({
        title: {
        //   text: 'Mesh拓扑图'
        },
        animation: false,
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        dataZoom: [
          {
            type: 'inside',
            show: true
          }
        ],

        series: [
          {
            type: 'graph',
            layout: 'none',
            symbolSize: 50,
            roam: true,
            label: {
              show: true,
              position: 'bottom',
              formatter: '{b}',
              fontSize: 30,
              fontWeight: 'bold',
              fontFamily: 'Arial',
              shadowColor: 'transparent',
              textShadowColor: 'transparent',
              color: 'white'
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            edgeLabel: {
              fontSize: 30
            },
            data: null,
            links: [],
            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0.3
            }
          }
        ]
      })
    }

    const switchChange = () => {
      if (state.checked1) {
        draw_repeat()
      } else {
        clear_repeat()
        // draw()
      }
    }

    const draw_repeat = () => {
      // let count = 0
      repeat = setInterval(() => {
        getTopo()
        // count++
        // if (count >= 5)count = 0
      }, repeat_count * 1000)
    }

    const clear_repeat = () => {
      clearInterval(repeat)
    }

    window.goToDetail = function (data) {
      console.log('goToDetail: ' + data)
      bottom_visible.value = true
      s_nodes.forEach((item, index, arr) => {
        if (item.sn === data) {
          drawer_data.value = item
          if (item.device_type === 1) {
            drawer_data.value.device_type = '手持式设备'
          } else if (item.device_type === 2) {
            drawer_data.value.device_type = '背负式设备'
          } else if (item.device_type === 3) {
            drawer_data.value.device_type = '车载式设备'
          } else if (item.device_type === 4) {
            drawer_data.value.device_type = '机载式设备'
          }
          console.info(item)
        }
      })
    }

    const detail_onClose = () => {
      bottom_visible.value = false
    }

    const draw = () => {
      // 根据接口传回的拓扑数据绘制，需获得mesh设备数目，连接关系

      // nodes = []
      // node_count = json.content.length
      drawClear()
      node_count = s_nodes.length
      console.log('node_count: ' + node_count)
      // node_count = 8
      let u_angel = 0
      let angel = 0
      // let index_node = 0
      const angel_val = 360 / onLine_node_count
      let ico_url // 节点图标
      let net_state
      let band_width
      let sband_width
      let tmp_online = 0
      let tmp_offline = 0
      let angel3_count = 0
      // const angel4_count = 0

      // angel = ico_url
      // json.content.forEach((item, index, arr) => {

      s_nodes.forEach((item, index, arr) => {
        // item.name = 'Node ' + index
        // 判断网络状态，设定节点图标颜色

        if (item.in_network === 1) { // 在线
          ico_url = 'image://' + ico_green
          net_state = '在线'

          if (onLine_node_count === 3) {
            u_angel = angel_val * angel3_count + 90
            if (u_angel === 90) {
              node_x = left_margin + radis
              node_y = top_margin
            }
            if ((u_angel > 180) && (u_angel < 270)) {
              angel = u_angel - 180

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            if ((u_angel > 270) && (u_angel < 360)) {
              angel = 360 - u_angel

              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            angel3_count++
          } else {
            u_angel = angel_val * tmp_online
            if (u_angel === 0) {
              node_x = left_margin
              node_y = top_margin + radis
            }
            if ((u_angel > 0) && (u_angel < 90)) {
              angel = u_angel
              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis * (1 - (Math.sin(angel * Math.PI / 180)))
            }
            if (u_angel === 90) {
              node_x = left_margin + radis
              node_y = top_margin
            }
            if ((u_angel > 90) && (u_angel < 180)) {
              angel = 180 - u_angel

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis * (1 - (Math.sin(angel * Math.PI / 180)))
            }
            if (u_angel === 180) {
              node_x = left_margin + radis + radis
              node_y = top_margin + radis
            }
            if ((u_angel > 180) && (u_angel < 270)) {
              angel = u_angel - 180

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            if (u_angel === 270) {
              node_x = left_margin + radis
              node_y = top_margin + radis + radis
            }
            if ((u_angel > 270) && (u_angel < 360)) {
              angel = 360 - u_angel

              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            tmp_online++
          }
        } else {
          node_x = left_margin + 3 * radis
          node_y = (tmp_offline + 1) * 60
          if (item.in_network === 0) { // 在线不在网
            ico_url = 'image://' + ico_blue
            net_state = '在线不在网'
          } else if ((item.in_network === 2) || (item.in_network === null)) { // 离线
            ico_url = 'image://' + ico_red
            net_state = '离线'
          }
          tmp_offline++
        }

        // 带宽值转换
        if (item.pband_width === 0) {
          band_width = '1.4'
        } else if (item.pband_width === 1) {
          band_width = '3'
        } else if (item.pband_width === 2) {
          band_width = '5'
        } else if (item.pband_width === 3) {
          band_width = '10'
        } else if (item.pband_width === 5) {
          band_width = '20'
        }
        if (item.sband_width === 0) {
          sband_width = '1.4'
        } else if (item.sband_width === 1) {
          sband_width = '3'
        } else if (item.sband_width === 2) {
          sband_width = '5'
        } else if (item.sband_width === 3) {
          sband_width = '10'
        } else if (item.sband_width === 5) {
          sband_width = '20'
        }
        // index_node = index + 1
        // console.log('nodes' + index_node + ' u_angel:' + u_angel + ' angel:' + angel + ' node_x:' + node_x + ' node_y:' + node_y)
        nodes.push({
          // name: item.sn,
          name: item.device_name,
          x: node_x,
          y: node_y,
          s_data: item,
          symbol: ico_url,
          netState: net_state,
          band_width: band_width,
          sband_width: sband_width,
          symbolSize: [50, 50],
          sn: item.sn
        })
      })

      option = {
        tooltip: {
          trigger: 'item', //
          triggerOn: 'mousemove', // 什么时候触发提示小图标，点击click的时候，或者鼠标滑过的时候，默认是mousemove鼠标滑过
          /* formatter可以以字符串模板方式写，也可以用回调函数写，不过字符串模板略有限制，我们使用回调函数会灵活点 */
          enterable: true,
          formatter: function (params) {
            let data = ''
            let res
            if (params.dataType === 'edge') {
              data = '方向：' + params.data.source + '-->' + params.data.target
              res = // 字符串形式的html标签会被echarts转换渲染成数据，这个res主要是画的tooltip里的上部分的标题部分
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                data +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '通信距离: ' + params.data.s_data.Distance + ' 米' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '数据发送速率: ' + params.data.s_data.SendingRate + ' Mbps' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '空中时间占比: ' + params.data.s_data.AirTime +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '误码百分比: ' + params.data.s_data.BLER + '% ' +
                ' </p></div>' +
                // "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                //   '数据发送速率: ' + params.data.s_data.SendingRate +
                // ' </p></div>' +
                // "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                //   '数据发送速率: ' + params.data.s_data.SendingRate +
                // ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '天线1/天线2信噪比: ' + params.data.s_data.ANT1SNR + '/' + params.data.s_data.ANT2SNR +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '信号强度1/信号强度2: ' + params.data.s_data.ANT1RSSI + '/' + params.data.s_data.ANT2RSSI +
                ' </p></div>'
            }
            if (params.dataType === 'node') {
              // data = params.data.name + ' x:' + params.data.x.toPrecision(8) + ' y:' + params.data.y.toPrecision(8)
              data = '设备名称: ' + params.data.s_data.device_name
              res = // 字符串形式的html标签会被echarts转换渲染成数据，这个res主要是画的tooltip里的上部分的标题部分
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                data + ' ' + `<button onclick="goToDetail('${params.data.sn}')">${params.data.sn}</button>` +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '网络状态: ' + params.data.netState +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                'IP地址: ' + params.data.s_data.ip_addr +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '网络ID: ' + params.data.s_data.net_id +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '电压: ' + params.data.s_data.voltage + ' V' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主中心频点: ' + params.data.s_data.pcenter_freq + ' Mhz' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主带宽: ' + params.data.band_width + ' M' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '辅中心频点: ' + params.data.s_data.scenter_freq + ' Mhz' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '辅带宽: ' + params.data.sband_width + ' M' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '邻节点数量: ' + params.data.s_data.relevantParameters.AdjacentNodeNumber +
                ' </p></div>'
            }
            // console.log('params:', params)

            return res
          }

        },
        series: [
          {
            // label: {
            //   show: false
            // },
            data: nodes,
            links: s_links
          }
        ]
      }
      // option && myChart.setOption(option)
      // myChart.clear()
      setTimeout(() => {
        // myChart.setOption(option)
        myChart.setOption(option)
      }, 500)
    }

    // ****************** 设备当前DHCP参数 *******************

    onMounted(() => {
      // model.id = pageDirectAction.getCurrentRouteValue()
      // getList()
      setTimeout(() => {
        // myChart.setOption(option)
        getList()
      }, 1000)
      // console.info('model.id:' + model.id)
      // getDeviceinfoById()

      // window.goToDetail = function (data) {
      //   console.log('goToDetail: ' + data)
      //   bottom_visible.value = true
      // }

      myChart = echarts.init(
        window.document.getElementById('myChart')
      )
      // setTimeout(() => {
      //   preDraw()
      //   getTopo()
      // }, 1000)
    })

    onUnmounted(() => {
      console.log('echart组件被销毁')
      myChart.dispose()
      myChart = undefined
      document.getElementById('myChart').remove()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      draw,
      getTopo,
      myChart,
      drawClear,
      preDraw,
      // draw_repeat,
      clear_repeat,
      draw_repeat,
      switchChange,
      bottom_visible,
      detail_onClose,
      enter_repeat_count,
      drawer_data,
      getNameBySn,
      ...toRefs(state)

    }
  }
})

</script>
