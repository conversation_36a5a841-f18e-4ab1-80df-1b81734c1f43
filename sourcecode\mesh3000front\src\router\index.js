import { createRouter, createWebHashHistory } from 'vue-router'
// import HomeView from '../views/HomeView.vue'
import MainLayout from '../views/MainLayout.vue'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/login.vue')
  },
  {
    path: '/view',
    component: () => import(/* webpackChunkName: "about" */ '../views/deviceView.vue')
  },
  {
    path: '/',
    name: 'mainLayout',
    component: MainLayout,
    children: [
      {
        path: '/',
        component: () => import(/* webpackChunkName: "about" */ '../views/map/MapView.vue')
      },
      {
        path: '/mapOffLine',
        component: () => import(/* webpackChunkName: "mapOffLine" */ '../views/map/MapViewOffLine.vue')
      },
      {
        path: '/SeaMapView',
        component: () => import(/* webpackChunkName: "mapOffLine" */ '../views/map/SeaMapView.vue')
      },
      {
        path: '/deviceinfo_index',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/index.vue')
      },
      {
        path: '/deviceinfo_add',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/add.vue')
      },
      {
        path: '/deviceinfo_mod',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/mod.vue')
      },
      {
        path: '/deviceinfo_detail',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/detail.vue')
      },
      {
        path: '/deviceinfo_logindex',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/logindex.vue')
      },
      {
        path: '/operateinfo_logindex',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/operatelogindex.vue')
      },
      {
        path: '/meetindex',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/meet/MeetView.vue')
      },
      {
        path: '/closeMeet',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/meet/CloseMeet.vue')
      },
      {
        path: '/joinMeet',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/meet/JoinMeet.vue')
      },
      {
        path: '/fsindex',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/fs_sip/fs_main.vue')
      },
      {
        path: '/sysinfo_set',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/systeminfo.vue')
      },
      {
        path: '/deviceinfo_set',
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/set.vue'),
        children: [
          {
            path: '/wifi_set',
            title: 'WiFi',
            vis: AuthUi.WiFi.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/wifi_set.vue')
          },
          {
            path: '/bluetooth',
            title: '蓝牙设置',
            vis: AuthUi.蓝牙设置.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/bluetooth.vue')
          },
          {
            path: '/dhcp_set',
            title: 'DHCP',
            vis: AuthUi.DHCP.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/dhcp_set.vue')
          },
          {
            path: '/firewall_set',
            title: '防火墙',
            vis: AuthUi.防火墙.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/firewall_set.vue')
          },
          {
            path: '/uart_set',
            title: 'Uart0',
            vis: AuthUi.Uart0.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/uart_set.vue')
          },
          {
            path: '/uart_set2',
            title: 'Comart',
            vis: AuthUi.Comart.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/uart_set2.vue')
          },
          {
            path: '/accessMode_set',
            title: '接入模式',
            vis: AuthUi.接入模式.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/accessMode_set.vue')
          },
          {
            path: '/voice_set',
            title: '语音',
            vis: AuthUi.语音.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/voice_set.vue')
          },
          {
            path: '/health_set',
            title: '温度设置',
            vis: AuthUi.温度设置.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/health_set.vue')
          },
          {
            path: '/devicesVersioninfo',
            title: '设备版本信息',
            vis: AuthUi.设备版本信息.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/devicesVersioninfo.vue')
          },
          {
            path: '/meshDeviceInfo_set',
            title: 'Mesh设备信息',
            vis: AuthUi.Mesh设备信息.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/meshDeviceInfo_set.vue')
          },
          {
            path: '/mesh_set',
            title: 'Mesh设置',
            vis: AuthUi.Mesh设置.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/mesh_set.vue')
          },
          {
            path: '/Gps_set',
            title: 'GPS设置',
            vis: AuthUi.GPS设置.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/Gps_set.vue')
          },
          {
            path: '/mesh_at',
            title: 'AT调试',
            vis: AuthUi.AT调试.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/atTest_set.vue')
          },
          {
            path: '/videoConfig_set',
            title: '视频参数设置',
            vis: AuthUi.视频参数设置.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/videoConfig_set.vue')
          },
          {
            path: '/video_play',
            title: 'HDMI视频播放',
            vis: AuthUi.HDMI视频播放.vis,
            component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/video/video_play.vue')
          }

        ]
      },
      {
        path: '/topo_draw',
        title: '网络拓扑',
        vis: AuthUi.left_item_topo.vis,
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/draw/topo_draw.vue')
      },
      {
        path: '/sweep_set',
        title: '扫频',
        vis: AuthUi.扫频.vis,
        component: () => import(/* webpackChunkName: "deviceinfoIndex" */ '../views/deviceinfo/views/sets/sweep_set.vue')
      },
      {
        path: '/video',
        vis: AuthUi.视频播放.vis,
        component: () => import(/* webpackChunkName: "about" */ '../views/deviceinfo/views/video/video_index.vue')
      },
      {
        path: '/video_hk',
        vis: AuthUi.海康视频播放.vis,
        component: () => import(/* webpackChunkName: "about" */ '../views/deviceinfo/views/video/video_hk.vue')
      }

    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 在这里判断是否登录，如果没登录跳转到登录页面
router.beforeEach((to, from, next) => {
  if (to.fullPath === '/login') {
    next()
  } else {
    if (sessionStorage.getItem('login_state') === null) {
      router.push({
        path: '/login'
      })
    } else {
      next()
    }
  }
})

export default router
