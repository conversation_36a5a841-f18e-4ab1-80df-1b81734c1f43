<template>
<a-card title="视频播放">
    <a-row justify="space-around" :span="24">
        <a-col :span="24">
                <video id="video" controls autoplay muted style="width: 100%;height: 100%"></video>
        </a-col>
    </a-row>
</a-card>
</template>

<script>
import { onMounted, defineComponent, reactive } from 'vue'
import { message } from 'ant-design-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { MeshDeviceInfoRequestAction } from '@/views/deviceinfo/action/meshDeviceInfoActive'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { MeshDeviceInfo } from '@/views/deviceinfo/model/MeshDeviceInfo'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { ServerAddr, DeviceRtspPort, getServerAddr, setServerAddr } from '@/views/deviceinfo/views/video/videoConfig'
import { MeshNetID } from '@/views/deviceinfo/constant/options'
import { WebRtcStreamer } from '@/views/deviceinfo/views/video/webrtcstreamer'
export default defineComponent({
  setup () {
    setServerAddr.setServerAddr('1231231')
    console.log(getServerAddr.getServerAddr())
    /**
     * 利用mesh设备信息得到设备的IP地址
     */
    // 对应后台数据表
    let rtspUrl = ''
    let model = reactive(new Deviceinfo())
    const meshdeviceinfo = new MeshDeviceInfo()
    model.configData = meshdeviceinfo

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const meshDeviceinfoRequestAction = new MeshDeviceInfoRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = () => {
      message.error('数据异常，请重试', 3)
    }

    // ****************** 设备当前meshdeviceinfof参数 *******************
    const getMeshDeviceInfo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      meshDeviceinfoRequestAction.query(baseRequestData, getMeshDeviceInfoSuccess, callbackError, null)
    }

    const getMeshDeviceInfoSuccess = (data) => {
      model = data.data
      //   console.log(model.ip_addr)
      rtspUrl = 'rtsp://' + model.ip_addr + DeviceRtspPort.deviceRtspPort // 获取到设备的IP地址
      //   console.log(rtspUrl)
      /**
       * 当页面加载完成的时候加载视频，并进行播放
       */
      // 加载options
      const options = 'rtptransport=tcp'
      // 绑定元素，加载webRtcServer
      window.webRtcServer = new WebRtcStreamer('video', ServerAddr.serverAddr)
      // 创建连接,（视频流，音频流，配置选项）
      window.webRtcServer.connect(rtspUrl, rtspUrl, options)
    }

    /** 当关闭页面的时时候断开连接，节省资源 */
    window.onbeforeunload = function () {
      this.webRtcServer.disconnect()
    }

    // ****************** 获取到设备当前的Mesh设备信息 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getMeshDeviceInfo()
      // getDeviceinfoById()
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      MeshNetID,
      confirm,
      getMeshDeviceInfo
    }
  }
})
</script>
