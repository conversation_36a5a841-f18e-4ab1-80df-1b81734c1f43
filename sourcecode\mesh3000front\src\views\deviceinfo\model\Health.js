class Health {
  constructor () {
    this.enabled = null
    this.temperaturethreshold = null
    this.voltagethreshold = null
    this.boardtemperature = null
    this.batterytemperature = null
    this.voltage = null
    this.capacity = null
  }

  init () {
    this.enabled = null
    this.temperaturethreshold = null
    this.voltagethreshold = null
    this.boardtemperature = null
    this.batterytemperature = null
    this.voltage = null
    this.capacity = null
  }
}

export { Health }
