
<template >
<div style="position: fixed; width: 100%; height: 100%;">
  <div id="container"></div>

  <a-button type="primary"  size="large" v-if="!showCard" @click="ShowCard()" style="margin-left: 1%; margin-top: 1%;">
      <MenuUnfoldOutlined/> 选项
    </a-button>

    <a-button v-if = "!startRule" type="primary"  size="large"  @click="rule()" style="margin-left: 1%; margin-top: 1%;">
      <MenuUnfoldOutlined/> 测距
    </a-button>

    <a-button v-if = "startRule" type="primary"  size="large"  @click="unrule()" style="margin-left: 1%; margin-top: 1%;">
      <MenuUnfoldOutlined/> 取消测距
    </a-button>

  <a-card v-if="showCard" hoverable style="width: 600px;background-color: #162130ce;font-size: large;" title="模式切换">
    <template #extra><a-button type="primary" @click="ShowCard()">收起</a-button></template>
    <a-tabs  @change="changeTable(activeKey)" v-model:activeKey="activeKey">
      <a-tab-pane key="1">
        <template #tab>
          <span>
            <EnvironmentOutlined />
            定位模式
          </span>
        </template>
        <a-row span="24">
              <a-col span="8" align="center">
                <a-button type='primary'>刷新列表</a-button>
              </a-col>
              <a-col span="8" align="center">
                <a-button type='primary' @click="allPosition()">定位全部</a-button>
              </a-col>
              <a-col span="8" align="center">
                <a-button type='primary' @click="stopFollow()" >自由视角</a-button>
              </a-col>
            </a-row>
        <a-table
        style="margin-top:2%;background-color: #ffffff00;"
    :pagination="pagination"
    class="ant-table-striped"
    size="middle"
    :columns="columns"
    :data-source="dataGps"
    bordered>
    <template #operation="{ record}">
                <div class="editable-row-operations">
                  <span>
                    <a-button  @click="follow((record.key))" type="primary" >追踪</a-button>
                  </span>
                </div>
              </template>
    </a-table>
      </a-tab-pane>
      <a-tab-pane key="2">
        <template #tab>
          <span>
            <NodeIndexOutlined />
            轨迹模式
          </span>
        </template>
        <a-row span="24">
                <a-col span="4" align="center">
                    选择设备：
                </a-col>
                <a-col span="16" align="start">
                    <a-select
                    v-model:value="model.sn"
                    placeholder="请选择节点"
                     style="width:60%">
                        <a-select-option
                            v-for="option in deviceByList"
                            v-bind:key="option.sn" :value="sn">
                            {{ option.name }}
                        </a-select-option>
                    </a-select>
                </a-col>
        </a-row>
        <a-row span="24" style="margin-top: 4%;">
               <a-col span="4" align="center">
                    时间选择：
                </a-col>
                <a-col span="15" align="center">
                    <!--  <a-date-picker
                        v-model:value="baseParam.start_time"
                        :disabled-date="disabledStartDate"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                        placeholder="开始时间"
                        @openChange="handleStartOpenChange"
                         />
                </a-col>
                <a-col span="2" align="center">
                    --
                </a-col>
                <a-col span="8" align="center">
                    <a-date-picker
                    v-model:value="baseParam.end_time"
                        :disabled-date="disabledEndDate"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                        placeholder="结束时间"
                        :open="endOpen"
                        @openChange="handleEndOpenChange"
                    /> -->
                    <a-range-picker  v-model:value="rangpickdate" style="margin-left:10px" :disabled-date="disabledDate" :disabled-time="disabledRangeTime" :show-time="{
                               hideDisabledOptions: true,
                               defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                             }" format="YYYY-MM-DD HH:mm:ss" />
                </a-col>
                <a-col span="5" align="center">
                    <a-button  @click="timeseq()">查询</a-button>
                </a-col>
        </a-row>
        <a-table
        style="margin-top:2%;background-color: #ffffff00;"
    :pagination="pagination"
    class="ant-table-striped"
    size="middle"
    :columns="columnsTrip"
    :data-source="dataTrip"
    bordered>
    <template #operation="{ record}">
                <div class="editable-row-operations">
                  <span>
                    <a-button  @click="drawTrip((record.key))" type="primary" >绘制</a-button>
                  </span>
                </div>
              </template>
    </a-table>
        <!-- 轨迹模式 -->

      </a-tab-pane>
      <!-- <a-tab-pane key="3">
        <template #tab>
            <ForkOutlined />
          <span>
            拓扑模式
          </span>
        </template>
        拓扑模式
      </a-tab-pane> -->
      <a-tab-pane key="4">
        <template #tab>
            <SettingOutlined />
          <span>
            图层选项
          </span>
        </template>
        <a-row>
                <a-col align="start" span="7">
                    <span>
                     轨迹动画速度：
                    </span>
                </a-col>
                <a-col align="start" span="8">
                    <a-select
                    v-model:value="markerSpead"
                    placeholder="请选择"
                     style="width:60%">
                        <a-select-option key="1" value= 1>
                            X1
                        </a-select-option>
                        <a-select-option key="2" value= 10>
                            X10
                        </a-select-option>
                        <a-select-option key="3" value= 50>
                            X50
                        </a-select-option>
                        <a-select-option key="4" value= 100>
                            X100
                        </a-select-option>
                    </a-select>
                </a-col>
            </a-row>
            <a-row style="margin-top: 2%;">
                <a-col align="start" span="7">
                    <span>
                     加载模式：
                    </span>
                </a-col>
                <a-col align="start" span="8">
                    <a-select
                      v-model:value="mapOnline"
                      placeholder="请选择"
                      style="width:60%"
                      @change="mapModeSelect"
                    >
                        <a-select-option value= 1>
                            在线地图
                        </a-select-option>
                        <a-select-option value= 0>
                            离线地图
                        </a-select-option>
                    </a-select>
                </a-col>
            </a-row>

            <!-- <a-row style="margin-top: 2%;">
                <a-col align="start" span="7">
                    <span>
                     地图模式：
                    </span>
                </a-col>
                <a-col align="start" span="8">
                    <a-select
                    v-model:value="mapMode"
                    placeholder="请选择"
                     style="width:60%">
                        <a-select-option value= 1>
                            2D
                        </a-select-option>
                        <a-select-option value= 0>
                            3D
                        </a-select-option>
                    </a-select>
                </a-col>
            </a-row> -->
      </a-tab-pane>
    </a-tabs>

  </a-card>
  <a-card v-if="showDiscription" style=" position:fixed; width: 60%; right:2%; bottom: 2%;">

    <a-descriptions :title="descriptionDeviceInfo.deviceName" span="24">
        <template #extra>
        <a-button @click="showDis" type="primary">
            关闭面板
        </a-button>
    </template>
    <a-descriptions-item label="主路发射功率">{{descriptionDeviceInfo.ptxPower}}dbm</a-descriptions-item>
    <a-descriptions-item label="辅路发射功率">{{descriptionDeviceInfo.stxPower}}dbm</a-descriptions-item>
    <a-descriptions-item label="主路中心频率">{{descriptionDeviceInfo.pcenterFreq}}</a-descriptions-item>
    <a-descriptions-item label="辅路中心频率">{{descriptionDeviceInfo.scenterFreq}}</a-descriptions-item>
    <a-descriptions-item label="电池电量">{{descriptionDeviceInfo.remainingBattery}}%</a-descriptions-item>
    <a-descriptions-item label="主机温度">{{descriptionDeviceInfo.hosttemperature}}℃</a-descriptions-item>
    <a-descriptions-item label="在网状态">{{descriptionDeviceInfo.inNetwork}}</a-descriptions-item>
    <a-descriptions-item label="经度" >{{descriptionDeviceInfo.jing}}</a-descriptions-item>
    <a-descriptions-item label="纬度">{{descriptionDeviceInfo.wei}}</a-descriptions-item>
    <a-descriptions-item label="海拔">{{descriptionDeviceInfo.gao}}m</a-descriptions-item>
  </a-descriptions>
  </a-card>
    <a-card v-if="showTripCtrl" style=" position:fixed; width: 300px; right:2%; bottom: 2%;">
    <a-row justify="space-around">
        <a-col>
            <a-button size="large"  @click="startAnimation()">开始动画</a-button>
        </a-col>
        <a-col>
            <a-button size="large" @click="stopAnimation()">停止动画</a-button>
        </a-col>
    </a-row>
  </a-card>
</div>

</template>
<script>
import { defineComponent, onMounted, ref, reactive, watch, onUnmounted } from 'vue'
import { EnvironmentOutlined, NodeIndexOutlined, SettingOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { LocationTrip } from '@/views/deviceinfo/action/locationTrip'
import { TopoRequestAction } from '@/views/deviceinfo/action/topoRequestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import AMapLoader from '@amap/amap-jsapi-loader'
import { shallowRef } from '@vue/reactivity'
import Router from '@/router/index'
import iconBule from '@/assets/img/blue3.png'
import dayjs from 'dayjs'

import {
  DebugModeOption
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  components: {
    EnvironmentOutlined,
    NodeIndexOutlined,
    SettingOutlined,
    MenuUnfoldOutlined
    // ForkOutlined
  },
  setup () {
    const Device = ref([])

    // 开始时间
    const startValue = ref()
    // 结束时间
    const endValue = ref()

    const endOpen = ref(false)

    // 全部在线设备的名称及sn
    const allDeviceName = ref([])

    // 定位模式表格配置项
    const pagination = {
      pageSize: 5 // 每页最大五行
    }
    // 定位模式的columns
    const columns = [
      {
        title: '设备名称',
        dataIndex: 'deviceName',
        width: '40%',
        align: 'center',
        slots: {
          customRender: 'deviceName'
        }
      },
      {
        title: '定位模式',
        dataIndex: 'mode',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'mode'
        }
      },
      {
        title: '星数',
        dataIndex: 'star',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'star'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 轨迹模式的columns
    const columnsTrip = [
      {
        title: '序号',
        dataIndex: 'index',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'index'
        }
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        width: '35%',
        align: 'center',
        slots: {
          customRender: 'startTime'
        }
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        width: '35%',
        align: 'center',
        slots: {
          customRender: 'endTime'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    window._AMapSecurityConfig = {
      securityJsCode: '82f23caa24603b4945140897b8b5be64'
    }
    // 对应后台数据表
    let model = reactive(new Deviceinfo())
    model.configData = 'json'

    const rangpickdate = ref()

    // 定位表格数据
    const dataGps = ref()
    dataGps.value = []

    // 轨迹表格数据
    const dataTrip = ref()
    dataTrip.value = []

    // 轨迹数据
    const lineArr = ref([])

    // 轨迹中设备下拉框 设备列表中全部设备的名称及sn
    const deviceByList = ref([])

    // 显示卡片
    const showCard = ref(false)

    // 定位追踪模式
    let positionMode = 0

    // 追踪Marker
    let followMarker = null
    // 追踪经纬度下标
    let followNum = null
    // 追踪文字标识
    let followText = null

    // 显示轨迹动画控制板
    const showTripCtrl = ref(false)

    // 经纬度
    const jing = ref(0)
    const wei = ref(0)

    // 是否已经添加点标志
    const addFlag = ref([])

    // new AMap.Map 对象
    const map = shallowRef(null)

    // 轨迹marker
    const marker = ref()

    // 轨迹Marker速度
    const markerSpead = ref(50)

    // 地图在线离线模式，默认在线
    const mapOnline = ref('在线地图')

    // 轨迹map
    const mapTrip = ref()

    // 测距插件
    const mouseTool = ref()

    // 测距标记
    const startRule = ref(false)

    // AMap
    // const aMap = ref()

    // linearr
    // const lineArr = ref([[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549], [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528], [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184], [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718], [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]])

    // 图标对象
    const icon = ref()

    // // 开始定时器标志
    // const flag = ref(0)

    // 定时器
    const getBegin = ref()

    // 蓝点icon
    const ico_blue = require('@/assets/img/blue3.png')

    // 设备定位数据
    const dataPosition = ref([])

    const infoWindow = ref()

    // 设备信息详情显示控制
    const showDiscription = ref()

    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const topoRequestAction = new TopoRequestAction()
    const locationTripAction = new LocationTrip()
    const pageDirectAction = new PageDirectAction()

    const showDis = () => {
      showDiscription.value = false
    }

    const disabledStartDate = startValue => {
      if (!startValue || !endValue.value) {
        return false
      }
      return startValue.valueOf() > endValue.value.valueOf()
    }

    const disabledEndDate = endValue => {
      if (!endValue || !startValue.value) {
        return false
      }

      return startValue.value.valueOf() >= endValue.valueOf()
    }

    const handleStartOpenChange = open => {
      if (!open) {
        endOpen.value = true
      }
    }

    const handleEndOpenChange = open => {
      endOpen.value = open
    }

    watch(startValue, () => {
      console.log('startValue', startValue.value)
    })
    watch(endValue, () => {
      console.log('endValue', endValue.value)
    })

    // 切换标签页触发
    const changeTable = (activeKey) => {
      addFlag.value = []
      showTripCtrl.value = false
      clearInterval(getBegin.value)
      switch (activeKey) {
        case '1':
          initMap()
          getDeviceinfoById()
          getBegin.value = setInterval(function () {
            if ((Router.currentRoute.value.fullPath).slice(0, 8) === '/') {
              getTopo()
              set()
            } else {
              clearInterval(getBegin.value)
            }
          }, 3000)
          break
        case '2':
          showTripCtrl.value = true
          //   getTrip()
          getTopo()
          // 获取全部设备的名称
          getAllDeviceName()
          break
        case '3':
          selectTopo()
          break
        case '4':
          // alert(4)
          break
      }
    }

    // 进入拓扑标签页时候显示网络拓扑
    const selectTopo = () => {
      const map = new AMap.Map('container', {
        zoom: 3.2,
        pitch: 32,
        showLabel: true,
        viewMode: '3D',
        center: [59.890102, 29.256014],
        mapStyle: 'amap://styles/grey'
      })

      //   // 文字图层
      //   const labelLayer = new AMap.LabelsLayer({
      //     rejectMapMask: true,
      //     collision: true,
      //     animation: true
      //   })
      //   map.add(labelLayer)

      const loca = new Loca.Container({
        map
      })

      const linkLayer = new Loca.LinkLayer({
        zIndex: 20,
        opacity: 1,
        visible: true,
        zooms: [2, 22]
      })

      const scatterLayer1 = new Loca.ScatterLayer({
        zIndex: 10,
        opacity: 1,
        visible: true,
        zooms: [2, 22]
      })
      const scatterLayer2 = new Loca.ScatterLayer({
        zIndex: 10,
        opacity: 0.8,
        visible: true,
        zooms: [2, 22]
      })
      const scatterLayer3 = new Loca.ScatterLayer({
        zIndex: 10,
        opacity: 0.8,
        visible: true,
        zooms: [2, 22]
      })
      const centerPoint = new Loca.GeoJSONSource({
        data: {
          type: 'FeatureCollection',
          features: [
            {
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [116.39, 39.9]
              }
            }
          ]
        }
      })
      scatterLayer3.setSource(centerPoint)
      scatterLayer3.setStyle({
        size: [300000, 300000],
        unit: 'meter',
        texture: 'https://a.amap.com/Loca/static/static/center-point.png'
      })
      loca.add(scatterLayer3)

      let lineGeoMap
      let scatterGeoMap

      const filterGeoJSON = (json, type) => {
        const newJSON = {
          type: 'FeatureCollection',
          features: [...json.features.filter((item) => item.properties.type === type)]
        }
        return new Loca.GeoJSONSource({
          data: newJSON
        })
      }
      fetch('https://a.amap.com/Loca/static/static/diplomacy-point.json')
        .then((res) => res.json())
        .then((data) => {
          scatterGeoMap = data
          //   setLabelsLayer(scatterGeoMap[50])
          const source1 = filterGeoJSON(scatterGeoMap[50], 0)
          const source2 = filterGeoJSON(scatterGeoMap[50], 1)
          scatterLayer1.setSource(source1)
          scatterLayer2.setSource(source2)
          scatterLayer1.setStyle({
            size: [500000, 500000],
            unit: 'miter',
            animate: true,
            duration: 1000,
            texture: 'https://a.amap.com/Loca/static/static/green.png'
          })
          scatterLayer2.setStyle({
            size: [500000, 500000],
            unit: 'miter',
            animate: true,
            duration: 1000,
            texture: 'https://a.amap.com/Loca/static/static/orange.png'
          })
          loca.add(scatterLayer1)
          loca.add(scatterLayer2)
          loca.animate.start()
        })

      fetch('https://a.amap.com/Loca/static/static/diplomacy-line.json')
        .then((res) => res.json())
        .then((data) => {
          lineGeoMap = Object.entries(data).reduce((accu, curr) => {
            const [key, geo] = curr
            accu[key] = new Loca.GeoJSONSource({
              data: geo
            })
            return accu
          }, {})
          linkLayer.setSource(lineGeoMap[50])
          linkLayer.setStyle({
            lineColors: function (index, item) {
              return item.link.properties.type === 0 ? ['#25CDEA', '#12BFBF'] : ['#FFD87B', '#FF4F00']
            },
            height: function (index, item) {
              return item.distance / 3
            },
            smoothSteps: function (index, item) {
              return 200
            }
          })
          loca.add(linkLayer)
        })

      const items = document.querySelectorAll('.item')

      for (let i = 0; i < items.length; i++) {
        (function (j) {
          items[j].onclick = () => {
            const element = items[j]
            const key = element.children[0].dataset.year
            document.querySelector('div.item.active').classList.remove('active')
            element.classList.add('active')
            linkLayer.setSource(lineGeoMap[key])
            // setLabelsLayer(scatterGeoMap[key])
            scatterLayer1.setSource(filterGeoJSON(scatterGeoMap[key], 0))
            scatterLayer2.setSource(filterGeoJSON(scatterGeoMap[key], 1))
          }
        })(i)
      }
    }

    // 获取全部设备的名称
    const getAllDeviceName = () => {
      for (let i = 0; i < dataGps.value.length; i++) {
        allDeviceName.value[i] = {
          deviceName: dataGps.value[i].deviceName,
          sn: dataGps.value[i].sn
        }
      }
    //   console.log(allDeviceName)
    }

    // 显示卡片
    const ShowCard = () => {
      showCard.value = !showCard.value
      // 打开选项则关闭设备信息详情
      showDiscription.value = false
    }

    // 测距功能
    const rule = () => {
      startRule.value = true
      AMap.plugin('AMap.MouseTool', function () {
        mouseTool.value = new AMap.MouseTool(map.value) // 创建鼠标工具插件实例
      })
      // 在插件的回调函数中使用功能
      mouseTool.value.rule({
        startMarkerOptions: { // 设置量测起始点标记属性对象 可缺省
          icon: new AMap.Icon({
            size: new AMap.Size(19, 31), // 图标大小
            imageSize: new AMap.Size(19, 31),
            image: '//webapi.amap.com/theme/v1.3/markers/b/start.png'
          }),
          offset: new AMap.Pixel(-9, -31)
        },
        endMarkerOptions: { // 设置量测结束点标记属性对象 可缺省
          icon: new AMap.Icon({
            size: new AMap.Size(19, 31), // 图标大小
            imageSize: new AMap.Size(19, 31),
            image: '//webapi.amap.com/theme/v1.3/markers/b/end.png'
          }),
          offset: new AMap.Pixel(-9, -31)
        },
        midMarkerOptions: { // 设置拖拽路线插件途经点点标记属性对象 可缺省
          icon: new AMap.Icon({
            size: new AMap.Size(19, 31), // 图标大小
            imageSize: new AMap.Size(19, 31),
            image: '//webapi.amap.com/theme/v1.3/markers/b/mid.png'
          }),
          offset: new AMap.Pixel(-9, -31),
          color: 'red'
        },
        lineOptions: { // 可缺省
          strokeStyle: 'solid',
          strokeColor: '#FF33FF',
          strokeOpacity: 1,
          strokeWeight: 2
        }
      })
    }

    // 取消测距
    const unrule = () => {
      startRule.value = false
      mouseTool.value.close(true)// 关闭，并清除覆盖物
    }

    const mapModeSelect = value => {
      console.log(`selected ${value}`)
      console.log(mapOnline.value)
      window.mapOnline = mapOnline.value
      if (mapOnline.value === '1') {
        Router.push({
          path: '/'
        })
      } else if (mapOnline.value === '0') {
        Router.push({
          path: 'mapOffLine'
        })
      }
    }

    // 回调函数错误处理
    const callbackError = () => {
    }
    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data
      }
    }

    const getNowSuccess = (data) => {
      console.log(data)
    }

    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }

    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }

    const getTopo = () => {
      topoRequestAction.query(baseRequestData, getTopoSuccess, callbackError, null)
    }

    const getNow = () => {
      // alert('getNow')
      locationTripAction.query2(baseRequestData, getNowSuccess, callbackError, null)
    }

    const timeseq = () => {
      dataTrip.value.length = 0
      //   model.sn = 'CBFCT0807094'
      if (rangpickdate.value !== undefined) {
        baseParam.start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
        baseParam.end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
      }
      locationTripAction.query3(baseRequestData, timeseqSuccess, callbackError, null)
    }

    // 获取轨迹数据
    const getLocationTrip = () => {
      locationTripAction.query(baseRequestData, getLocationTripSuccess, callbackError, null)
    }

    // const selectDeviceChange = key => {
    //   alert(key)
    // }

    // 获取轨迹成功回调
    const getLocationTripSuccess = (data) => {
      test(data)
    //   console.log(data.data[0].Time)
    //   console.log('获取轨迹成功')
    }

    // 将时间戳转为YYYY-MM-DD HH:mm:ss 格式
    const formatData = (timestamp) => {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const sec = date.getSeconds()

      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + sec
    }

    // 获取分段轨迹成功回调
    const timeseqSuccess = (data) => {
      let k = 0
      for (let i = 0; i < data.data.length; i += 2) {
        dataTrip.value[k] = {
          key: i / 2,
          index: (i / 2) + 1,
          startTime: formatData(data.data[i].Time),
          endTime: formatData(data.data[i + 1].Time)
        }
        k++
      }
    }

    // 点击分段轨迹后面的绘制按钮，绘制相应轨迹
    const drawTrip = (key) => {
      baseParam.start_time = dataTrip.value[key].startTime
      baseParam.end_time = dataTrip.value[key].endTime
      getLocationTrip()
      stopAnimation()
    }

    // 获取拓扑成功回调

    const getTopoSuccess = (data) => {
      writeDataGps(data)
      writeDataPosition(data)
      /**
       * 使用高德经纬度转换API
       */
      //   AMap.convertFrom([jing.value, wei.value], 'gps', function (status, result) {
      // if (result.info === 'ok') {
      //   jing.value = result.locations[0].lng
      //   wei.value = result.locations[0].lat
      //   const lnglats = result.locations // Array.<LngLat>
      // }
      //   })
    }

    /**
     * gps定位数据方法，包含：设备名称、模式、星数
     * @param {*} data 请求返回的数据源
     */
    const writeDataGps = (data) => {
      let i = ''
      let k = 0
      for (i in data.data.configData) {
        let modeNameTemp = '/'
        if (data.data.configData[i].location_mode !== 0 && data.data.configData[i].location_mode !== null) {
          // 做模式参数与模式名称进行映射
          switch (data.data.configData[i].location_mode) {
            case 1:
              modeNameTemp = 'GPS模式'
              break
            case 2:
              modeNameTemp = '北斗模式'
              break
            case 3:
              modeNameTemp = 'GPS+北斗'
              break
          }
          dataGps.value[k] = {
            key: k.toString(),
            deviceName: data.data.configData[i].device_name,
            mode: modeNameTemp,
            star: data.data.configData[i].satellite_number,
            sn: data.data.configData[i].sn
          }
          k++
        }
      }
    }
    /**
     * 获取设备管理中设备维护的所有设备
     */
    const getDeviceByList = (dataDevice) => {
      deviceByList.value.length = 0
      for (let i = 0; i < dataDevice.data.content.length; i++) {
        deviceByList.value.push({
          name: dataDevice.data.content[i].device_name,
          sn: dataDevice.data.content[i].sn
        })
      }
    }

    /**
     * GPS定位经纬度信息方法，包含 设备名称、经纬度、高程
     * description： 此方法在每次获取定位信息的时候调用一次，data为响应数据源。
     */
    const writeDataPosition = (data) => {
      // configData遍历变量
      let i = ''
      //   // 循环变量，用于遍历定位dataPosition数组下标
      //   let k = 0
      // marker的临时变量，暂存new Marker使用
      let markerTemp
      let text
      // 是否可以创建标志变量
      let canCreatMarkerFlag = true

      for (i in data.data.configData) {
        // 首先遍历dataPostiton，判断对应的设备是否存在，如果不存在则将对应设备的Mark进行创建
        for (let j = 0; j < dataPosition.value.length; j++) {
          if (data.data.configData[i].device_name === dataPosition.value[j].deviceName) {
            // 将对应设备dataPosition的经纬度、高程、GPS模式状态信息进行设置
            dataPosition.value[j].wei = data.data.configData[i].latitude
            dataPosition.value[j].jing = data.data.configData[i].longitude
            dataPosition.value[j].altitude = data.data.configData[i].altitude
            dataPosition.value[j].locationMode = data.data.configData[i].location_mode
            dataPosition.value[j].sn = data.data.configData[i].sn
            dataPosition.value[j].ptxPower = data.data.configData[i].ptx_power
            dataPosition.value[j].stxPower = data.data.configData[i].stx_power
            dataPosition.value[j].scenterFreq = data.data.configData[i].scenter_freq
            dataPosition.value[j].pcenterFreq = data.data.configData[i].pcenter_freq
            dataPosition.value[j].remainingBattery = data.data.configData[i].remaining_battery
            dataPosition.value[j].inNetwork = data.data.configData[i].in_network
            dataPosition.value[j].hosttemperature = data.data.configData[i].hosttemperature

            // 如果找到了则将创建Marker的标志变量设为false——不用new Marker (dataPositon中存在)
            canCreatMarkerFlag = false
            // 停止继续循环遍历
            break
          }
        }
        // 如果遍历完dataPosition数组还没有找到相同的设备，那么将Marker添加进来
        // 并且GPS模式不能是关闭状态
        if (canCreatMarkerFlag === true && data.data.configData[i].location_mode !== 0 && data.data.configData[i].location_mode !== null) {
          markerTemp = new AMap.Marker({
            position: new AMap.LngLat(data.data.configData[i].longitude, data.data.configData[i].latitude), // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
            icon: iconBule, // 添加 Icon 图标 URL
            // title: '123123', // 鼠标移入时候系统自带的提示框
            clickable: true, // 可否单击
            offset: new AMap.Pixel(-25, -25), // 设置点标记偏移量
            markerName: data.data.configData[i].device_name // 将设备名称和对应的mark关联起来
          })

          // 为Marker绑定点击事件
          markerTemp.on('click', clickPoint)

          text = new AMap.Text({
            position: new AMap.LngLat(data.data.configData[i].longitude, data.data.configData[i].latitude),
            anchor: 'top-center',
            text: data.data.configData[i].device_name,
            style: {
              'font-weight': 'bold',
              'background-color': '#00ffdd5d',
              color: '#aafff4d7'
            },
            offset: new AMap.Pixel(0, 30) // 设置点标记偏移量
          })
          dataPosition.value.push({
            deviceName: data.data.configData[i].device_name,
            jing: data.data.configData[i].longitude,
            wei: data.data.configData[i].latitude,
            altitude: data.data.configData[i].altitude,
            mark: markerTemp,
            text: text,
            locationMode: data.data.configData[i].location_mode,
            sn: data.data.configData[i].sn,
            ptxPower: data.data.configData[i].ptx_power,
            stxPower: data.data.configData[i].stx_power,
            scenterFreq: data.data.configData[i].scenter_freq,
            pcenterFreq: data.data.configData[i].pcenter_freq,
            remainingBattery: data.data.configData[i].remaining_battery,
            inNetwork: data.data.configData[i].in_network,
            hosttemperature: data.data.configData[i].hosttemperature
          })
        }
        // 刷新数据集
        if (showDiscription.value) {
          // 在dataPosition中找到对应的设备信息，并且将其中对应的信息创建信息体，填入展示表格
          for (let d = 0; d < dataPosition.value.length; d++) {
            // 在dataPostion中找到点击的数据集
            if (dataPosition.value[d].deviceName === descriptionDeviceInfo.value.deviceName) {
              wirteDescriptionDeviceInfo(d)
            }
          }
        }
      }
    }

    const getTrip = () => {
    //   console.log(rangpickdate)
      if (rangpickdate.value !== undefined) {
        baseParam.start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
        baseParam.end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
      }
      //   alert(baseParam.start_time)
      //   alert(baseParam.end_time)
      getLocationTrip()
    }

    const initMap = () => {
      AMapLoader.load({
        key: '98735df7de2b211d80c997642a64aa8d', // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.MoveAnimation'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        Loca: { // 是否加载 Loca， 缺省不加载
          version: '2.0.0' // Loca 版本，缺省 1.3.2
        }
      }).then((AMap) => {
        map.value = new AMap.Map('container', { // 设置地图容器id
          viewMode: '2D', // 是否为3D地图模式
          zoom: 5, // 初始化地图级别
          center: [108.952194, 34.217998], // 初始化地图中心点位置
          mapStyle: 'amap://styles/grey'
        })
        // 初始化地图变量
        initMapValue()
      }).catch(e => {
        console.log(e)
      })
    }

    /**
 * 初始化对象，在加载地图的时候完成对象的创建
 */
    const initMapValue = () => {
      // 创建 AMap.Icon (定位的图标)实例：
      icon.value = new AMap.Icon({
        size: new AMap.Size(50, 50), // 图标尺寸
        image: iconBule, // Icon的图像
        offset: new AMap.Pixel(-25, -25), // 设置点标记偏移量
        imageSize: new AMap.Size(50, 50) // 根据所设置的大小拉伸或压缩图片
      })

      // 创建信息窗体
      infoWindow.value = new AMap.InfoWindow({
        content: '123123123123123'// 信息窗体的内容
      })
    }

    const descriptionDeviceInfo = ref({
      deviceName: '',
      stxPower: '',
      ptxPower: '',
      scenterFreq: '',
      pcenterFreq: '',
      rssi: '--',
      snr: '--',
      remainingBattery: '',
      template: '--',
      inNetwork: '',
      jing: '',
      wei: '',
      gao: ''

    })

    /**
     * 描述：覆盖物的（定位图标）点击事件
     * 对象：定位点
     */
    const clickPoint = (e) => {
    // 关闭选项卡片
      showCard.value = false
      // 打开设备详细信息表格
      showDiscription.value = true

      descriptionDeviceInfo.value.deviceName = e.target._originOpts.markerName
    //   infoWindow.value.open(map.value, [e.target._originOpts.position.lng, e.target._originOpts.position.lat])
    }
    /**
     * 写入descriptionDeviceInfo方法
     * @param {*} key
     */

    const wirteDescriptionDeviceInfo = (d) => {
      descriptionDeviceInfo.value.wei = deleteNull(dataPosition.value[d].wei)
      descriptionDeviceInfo.value.jing = deleteNull(dataPosition.value[d].jing)
      descriptionDeviceInfo.value.gao = dataPosition.value[d].altitude
      descriptionDeviceInfo.value.ptxPower = dataPosition.value[d].ptxPower
      descriptionDeviceInfo.value.stxPower = deleteNull(dataPosition.value[d].stxPower)
      //   descriptionDeviceInfo.value.stxPower = dataPosition.value[d].stxPower
      descriptionDeviceInfo.value.scenterFreq = dataPosition.value[d].scenterFreq
      descriptionDeviceInfo.value.pcenterFreq = dataPosition.value[d].pcenterFreq
      descriptionDeviceInfo.value.remainingBattery = dataPosition.value[d].remainingBattery
      if (dataPosition.value[d].inNetwork === 1) {
        descriptionDeviceInfo.value.inNetwork = '在线'
      } else if (dataPosition.value[d].inNetwork === 0) {
        descriptionDeviceInfo.value.inNetwork = '在线不在网'
      } else { descriptionDeviceInfo.value.inNetwork = '离线' }

      descriptionDeviceInfo.value.hosttemperature = dataPosition.value[d].hosttemperature
    }

    const deleteNull = (befor) => {
      if (befor === null || befor === '' || befor === undefined) {
        return '--'
      } else {
        return befor
      }
    }

    // const setFlag = () => {
    //   for (let i = 0; i < dataPosition.value.length; i++) {
    //     addFlag[i] = 1
    //   }
    // }
    // const addMarkFlag = false

    // temp
    // const testTemp = ref()
    const follow = key => {
      positionMode = 1
      map.value.clearMap()

      // 关闭选项卡片
      showCard.value = false
      // 打开设备详细信息表格
      showDiscription.value = true

      // 找到追踪设备的Mark
      for (let i = 0; i < dataPosition.value.length; i++) {
        if (dataGps.value[key].sn === dataPosition.value[i].sn) {
          followMarker = dataPosition.value[i].mark
          followText = dataPosition.value[i].text
          followNum = i
          map.value.add(followMarker)
          map.value.add(followText)
          map.value.setZoomAndCenter(18, [dataPosition.value[followNum].jing, dataPosition.value[followNum].wei])
          set()
          model.sn = dataPosition.value[i].sn
          // 对设备详细描述的列表名称赋值（追踪哪个设备显示那个设备）
          descriptionDeviceInfo.value.deviceName = dataPosition.value[i].deviceName
          getNow()
        }
      }
    }

    const allPosition = () => {
      positionMode = 0
      map.value.clearMap()
      set()
    }

    const stopFollow = () => {
      positionMode = 2
      set()
    }

    const set = () => {
    // 调用 moveTo 方法
      switch (positionMode) {
        case 0 :
          // map.value.clearMap()
          for (let i = 0; i < dataPosition.value.length; i++) {
            map.value.add(dataPosition.value[i].mark)
            map.value.add(dataPosition.value[i].text)
            dataPosition.value[i].mark.moveTo([dataPosition.value[i].jing, dataPosition.value[i].wei], {
              duration: 2500
            })
            dataPosition.value[i].text.moveTo([dataPosition.value[i].jing, dataPosition.value[i].wei], {
              duration: 2500
            })
          }
          break
        case 1:
          map.value.setCenter([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], false, 3100)
          followMarker.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
          followText.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
          break
        case 2:
          followMarker.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
          followText.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
      }
    }
    const test = (data) => {
      lineArr.value.length = 0
      for (let i = 0; i < data.data.length; i++) {
        lineArr.value.push([data.data[i].Jing, data.data[i].Wei])
      }
      console.log(lineArr.value)
      //   lineArr.value = [[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549], [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528], [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184], [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718], [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]]
      // 停止获取实时定位，节约资源
      clearInterval(getBegin.value)
      // JSAPI2.0 使用覆盖物动画必须先加载动画插件
      AMap.plugin('AMap.MoveAnimation', function () {
        mapTrip.value = new AMap.Map('container', {
          resizeEnable: true,
          center: [116.397428, 39.90923],
          zoom: 17,
          mapStyle: 'amap://styles/grey'
        })

        marker.value = new AMap.Marker({
          map: mapTrip.value,
          position: [data.data[0].Jing, data.data[0].Wei],
          icon: ico_blue,
          offset: new AMap.Pixel(-25, -25)
        })

        // 绘制轨迹
        // eslint-disable-next-line no-unused-vars
        const polyline = new AMap.Polyline({
          map: mapTrip.value,
          path: lineArr.value,
          showDir: true,
          strokeColor: '#AF5', // 线颜色
          strokeOpacity: 1, // 线透明度
          strokeWeight: 10 // 线宽
          // strokeStyle: "solid"  //线样式
        })

        const passedPolyline = new AMap.Polyline({
          map: mapTrip.value,
          strokeColor: '#AF5', // 线颜色
          strokeWeight: 6 // 线宽
        })

        marker.value.on('moving', function (e) {
          passedPolyline.setPath(e.passedPath)
          mapTrip.value.setCenter(e.target.getPosition(), true)
        })

        mapTrip.value.setFitView()

        window.pauseAnimation = function () {
          marker.value.pauseMove()
        }

        window.resumeAnimation = function () {
          marker.value.resumeMove()
        }

        window.stopAnimation = function () {
          marker.value.stopMove()
        }
      })
    }

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListFinally = () => {

    }

    const getListSuccess = (data) => {
      Device.value.length = 0
      for (let i = 0; i < data.data.content.length; i++) {
        if (data.data.content[i].in_network !== 2 && data.data.content[i].in_network !== null) {
          Device.value.push({
            name: data.data.content[i].device_name,
            value: data.data.content[i].id
          })
        }
      }
      // 将默认选择的节点设置为列表第一项
      if (Device.value.length > 0) {
        model.id = Device.value[0].value
      }

      getDeviceByList(data)
      console.log('=====================')
      console.log(deviceByList)
    }

    getBegin.value = setInterval(function () {
      if ((Router.currentRoute.value.fullPath).slice(0, 8) === '/') {
        getTopo()
        set()
      } else {
        clearInterval(getBegin.value)
      }
    }, 3000)
    onMounted(() => {
    //   window.addEventListener('offline', function () {
    //     console.log('断网了')
    //   })

      //   window.addEventListener('online', function () {
      //     console.log('在网了')
      //   })
      //   alert(navigator.onLine)
      //   if (navigator.onLine === false) {
      //     Router.push({
      //       path: 'mapOffLine'
      //     })
      //   }
      getList()
      //   getTopo()
      getDeviceinfoById()
      initMap()
      mapOnline.value = '在线地图'
    //   setFlag()
    })

    onUnmounted(() => {
      console.log('组件被销毁')
      if (DebugModeOption.value === 0) {
      // 销毁地图，并清空地图容器
        map.value.destroy()
        // 地图对象赋值为null
        map.value = null
        // 清除地图容器的 DOM 元素
        document.getElementById('container').remove() // "container" 为指定 DOM 元素的id
      }
    })

    // 开始动画
    const startAnimation = () => {
      marker.value.moveAlong(lineArr.value, {
        // 每一段的时长
        duration: 3000 / markerSpead.value, // 可根据实际采集时间间隔设置
        // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
        autoRotation: true
      })
    }
    // 停止动画
    const stopAnimation = () => {
      marker.value.stopMove()
    }

    return {
      activeKey: ref('1'),
      EnvironmentOutlined,
      model,
      baseRequestData,
      ico_blue,
      dataSource,
      baseParam,
      LabelCaption,
      DebugModeOption,
      DeviceinfoColumns,
      pageDirectAction,
      jing,
      wei,
      map,
      showCard,
      showTripCtrl,
      columns,
      dataGps,
      pagination,
      startValue,
      endValue,
      endOpen,
      allDeviceName,
      showDiscription,
      dayjs,
      rangpickdate,
      deviceByList,
      columnsTrip,
      showDis,
      dataTrip,
      drawTrip,
      markerSpead,
      mapOnline,
      timeseq,
      stopAnimation,
      //   selectDeviceChange,
      disabledStartDate,
      disabledEndDate,
      handleStartOpenChange,
      handleEndOpenChange,
      stopFollow,
      allPosition,
      follow,
      startAnimation,
      changeTable,
      ShowCard,
      descriptionDeviceInfo,
      getTrip,
      mapModeSelect,
      rule,
      unrule,
      startRule
    }
  }
})

</script>
  <style >
#container {
position: absolute;
height: 100%;
width:100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.editable-row-operations a {
  margin-right: 8px;
}
.amap-ranging-label {
  background-color: black !important; /* 设置背景色为黑色，或者替换为其他颜色 */
  color: white !important; /* 确保字体颜色和背景对比明显 */
  padding: 5px;
  border-radius: 3px;
  opacity: 0.8; /* 设置透明度，使其看起来更柔和 */
}
</style>
