import baseRequest from '@/request/request'
import axios from 'axios'
class UserRequestAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo/config'
    this.urllogin = '/api/login'
    this.urladdUser = `${this.BASE_API_URL}/adduser`
    this.urlmodAuth = `${this.BASE_API_URL}/modauth`
    this.urlgetAuthList = `${this.BASE_API_URL}/getauthlist`
    this.urladdRole = `${this.BASE_API_URL}/addrole`
    this.urlgetrolelist = `${this.BASE_API_URL}/getrolelist`
    this.urlgetuserlist = `${this.BASE_API_URL}/getuserlist`
    this.urlmodpass = `${this.BASE_API_URL}/modpass`
    // 删除用户
    this.urldeluser = `${this.BASE_API_URL}/deluser`
    // 删除角色
    this.urldelrole = `${this.BASE_API_URL}/delrole`
    // 删除权限
    this.urldelauth = `${this.BASE_API_URL}/delauth`
    // 增加权限
    this.urladdauthui = `${this.BASE_API_URL}/addauthui`
    // 获取用户角色
    this.urlgetrolename = `${this.BASE_API_URL}/getrolename`
  }

   /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   login = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     axios.post(this.urllogin, baseRequestData)
       .then((response) => {
         if (typeof successCallback === 'function') {
           successCallback(response.data, response.headers)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   addUser = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urladdUser, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

  modAuth = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlmodAuth, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  modPass = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlmodpass, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getAuthList = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlgetAuthList, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getUserList = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlgetuserlist, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  addRole = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urladdRole, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  // 删除用户
  delUser = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urldeluser, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  // 删除角色
  delRole = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urldelrole, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  // 根据用户名获取用户角色
  getRoleName = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlgetrolename, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }

  getrolelist = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlgetrolelist, baseRequestData)
      .then(response => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch(error => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  }
}

export { UserRequestAction }
