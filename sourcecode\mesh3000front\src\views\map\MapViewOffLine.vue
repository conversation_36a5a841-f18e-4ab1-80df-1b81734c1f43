<template >
  <div style="position: fixed; width: 100%; height: 100%;">
    <div id="container1"></div>

    <a-button type="primary"  size="large" v-if="!showCard" @click="ShowCard()" style="margin-left: 1%; margin-top: 1%;z-index: 999999 !important;">
        <MenuUnfoldOutlined/> 选项
      </a-button>
    <a-card v-if="showCard" hoverable style="width: 600px;background-color: #162130ce;font-size: large;z-index: 999999 !important;" title="模式切换">
      <template #extra><a-button type="primary" @click="ShowCard()">收起</a-button></template>
      <a-tabs  @change="changeTable(activeKey)" v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <EnvironmentOutlined />
              定位模式
            </span>
          </template>
          <a-row span="24">
                <a-col span="8" align="center">
                  <a-button type='primary'>刷新列表</a-button>
                </a-col>
                <a-col span="8" align="center">
                  <a-button type='primary' @click="allPosition()">定位全部</a-button>
                </a-col>
                <a-col span="8" align="center">
                  <a-button type='primary' @click="stopFollow()" >停止追踪</a-button>

                </a-col>
              </a-row>
          <a-table
          style="margin-top:2%;background-color: #ffffff00;"
      :pagination="pagination"
      class="ant-table-striped"
      size="middle"
      :columns="columns"
      :data-source="dataGps"
      bordered>
      <template #operation="{ record}">
                  <div class="editable-row-operations">
                    <span>
                      <a-button  @click="follow((record.key))" type="primary" >追踪</a-button>
                    </span>
                  </div>
      </template>
      </a-table>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <span>
              <NodeIndexOutlined />
              轨迹模式
            </span>
          </template>
          <a-row span="24">
                  <a-col span="4" align="center">
                      选择设备：
                  </a-col>
                  <a-col span="16" align="start">
                      <a-select
                      v-model:value="model.sn"
                      placeholder="请选择节点"
                       style="width:60%">
                          <a-select-option
                              v-for="option in deviceByList"
                              v-bind:key="option.sn" :value="sn">
                              {{ option.name }}
                          </a-select-option>
                      </a-select>
                  </a-col>
          </a-row>
          <a-row span="24" style="margin-top: 4%;">
                 <a-col span="4" align="center">
                      时间选择：
                  </a-col>
                  <a-col span="15" align="center">
                      <!--  <a-date-picker
                          v-model:value="baseParam.start_time"
                          :disabled-date="disabledStartDate"
                          show-time
                          format="YYYY-MM-DD HH:mm:ss"
                          placeholder="开始时间"
                          @openChange="handleStartOpenChange"
                           />
                  </a-col>
                  <a-col span="2" align="center">
                      --
                  </a-col>
                  <a-col span="8" align="center">
                      <a-date-picker
                      v-model:value="baseParam.end_time"
                          :disabled-date="disabledEndDate"
                          show-time
                          format="YYYY-MM-DD HH:mm:ss"
                          placeholder="结束时间"
                          :open="endOpen"
                          @openChange="handleEndOpenChange"
                      /> -->
                      <a-range-picker  v-model:value="rangpickdate" style="margin-left:10px" :disabled-date="disabledDate" :disabled-time="disabledRangeTime" :show-time="{
                                 hideDisabledOptions: true,
                                 defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                               }" format="YYYY-MM-DD HH:mm:ss" />
                  </a-col>
                  <a-col span="5" align="center">
                      <a-button  @click="timeseq()">查询</a-button>
                  </a-col>
          </a-row>
          <a-table
          style="margin-top:2%;background-color: #ffffff00;"
      :pagination="pagination"
      class="ant-table-striped"
      size="middle"
      :columns="columnsTrip"
      :data-source="dataTrip"
      bordered>
      <template #operation="{ record}">
                  <div class="editable-row-operations">
                    <span>
                      <a-button  @click="drawTrip((record.key))" type="primary" >绘制</a-button>
                    </span>
                  </div>
                </template>
      </a-table>
          <!-- 轨迹模式 -->

        </a-tab-pane>
        <!-- <a-tab-pane key="3">
          <template #tab>
              <ForkOutlined />
            <span>
              拓扑模式
            </span>
          </template>
          拓扑模式
        </a-tab-pane> -->
        <a-tab-pane key="4">
          <template #tab>
              <SettingOutlined />
            <span>
              图层选项
            </span>
          </template>
          <a-row>
                  <a-col align="start" span="7">
                      <span>
                       轨迹动画速度：
                      </span>
                  </a-col>
                  <a-col align="start" span="8">
                      <a-select
                      v-model:value="markerSpead"
                      placeholder="请选择"
                       style="width:60%">
                          <a-select-option key="1" value= 1>
                              X1
                          </a-select-option>
                          <a-select-option key="2" value= 10>
                              X10
                          </a-select-option>
                          <a-select-option key="3" value= 50>
                              X50
                          </a-select-option>
                          <a-select-option key="4" value= 100>
                              X100
                          </a-select-option>
                      </a-select>
                  </a-col>
              </a-row>
              <a-row style="margin-top: 2%;">
                  <a-col align="start" span="7">
                      <span>
                       加载模式：
                      </span>
                  </a-col>
                  <a-col align="start" span="8">
                    <a-select
                      v-model:value="mapOnline"
                      placeholder="请选择"
                      style="width:60%"
                      @change="mapModeSelect"
                    >
                        <a-select-option value= 1>
                            在线地图
                        </a-select-option>
                        <a-select-option value= 0>
                            离线地图
                        </a-select-option>
                    </a-select>
                </a-col>
              </a-row>

              <!-- <a-row style="margin-top: 2%;">
                  <a-col align="start" span="7">
                      <span>
                       地图模式：
                      </span>
                  </a-col>
                  <a-col align="start" span="8">
                      <a-select
                      v-model:value="mapMode"
                      placeholder="请选择"
                       style="width:60%">
                          <a-select-option value= 1>
                              2D
                          </a-select-option>
                          <a-select-option value= 0>
                              3D
                          </a-select-option>
                      </a-select>
                  </a-col>
              </a-row> -->
        </a-tab-pane>
      </a-tabs>

    </a-card>
    <a-card v-if="showDiscription" style=" position:fixed; width: 60%; right:2%; bottom: 2%;z-index: 999999 !important;">

      <a-descriptions :title="descriptionDeviceInfo.deviceName" span="24">
          <template #extra>
          <a-button @click="showDis" type="primary">
              关闭面板
          </a-button>
      </template>
      <a-descriptions-item label="主路发射功率">{{descriptionDeviceInfo.ptxPower}}dbm</a-descriptions-item>
      <a-descriptions-item label="辅路发射功率">{{descriptionDeviceInfo.stxPower}}dbm</a-descriptions-item>
      <a-descriptions-item label="主路中心频率">{{descriptionDeviceInfo.pcenterFreq}}</a-descriptions-item>
      <a-descriptions-item label="辅路中心频率">{{descriptionDeviceInfo.scenterFreq}}</a-descriptions-item>
      <a-descriptions-item label="电池电量">{{descriptionDeviceInfo.remainingBattery}}%</a-descriptions-item>
      <a-descriptions-item label="主机温度">{{descriptionDeviceInfo.hosttemperature}}℃</a-descriptions-item>
      <a-descriptions-item label="在网状态">{{descriptionDeviceInfo.inNetwork}}</a-descriptions-item>
      <a-descriptions-item label="经度" >{{descriptionDeviceInfo.jing}}</a-descriptions-item>
      <a-descriptions-item label="纬度">{{descriptionDeviceInfo.wei}}</a-descriptions-item>
      <a-descriptions-item label="海拔">{{descriptionDeviceInfo.gao}}m</a-descriptions-item>
    </a-descriptions>
    </a-card>
      <a-card v-if="showTripCtrl" style=" position:fixed; width: 300px; right:2%; bottom: 2%;z-index: 999999 !important;">
      <a-row justify="space-around">
          <a-col>
              <a-button size="normal"  @click="startAnimation()">开始动画</a-button>
          </a-col>
          <a-col>
              <a-button size="normal" @click="pauseAnimation()">停止动画</a-button>
          </a-col>
          <a-col>
              <a-button size="normal" @click="leaveAnimation()">退出播放</a-button>
          </a-col>
      </a-row>
    </a-card>
  </div>

  </template>
<script>
import { onMounted, defineComponent, ref, reactive, watch, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { EnvironmentOutlined, NodeIndexOutlined, SettingOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { LocationTrip } from '@/views/deviceinfo/action/locationTrip'
import { TopoRequestAction } from '@/views/deviceinfo/action/topoRequestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
// import AMapLoader from '@amap/amap-jsapi-loader'

import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import { shallowRef } from '@vue/reactivity'
import Router from '@/router/index'
// import iconBule from '@/assets/img/blue3.png'
import dayjs from 'dayjs'
import 'leaflet.animatedmarker/src/AnimatedMarker'

import {
  DebugModeOption
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  components: {
    EnvironmentOutlined,
    NodeIndexOutlined,
    SettingOutlined,
    MenuUnfoldOutlined
    // ForkOutlined
  },
  setup () {
    let trip_select = ''
    let config_data = {}
    let select_configData = {}
    const Device = ref([])

    // 开始时间
    const startValue = ref()
    // 结束时间
    const endValue = ref()

    const endOpen = ref(false)

    // 全部设备的名称及sn
    const allDeviceName = ref([])

    // 定位模式表格配置项
    const pagination = {
      pageSize: 5 // 每页最大五行
    }
    // 定位模式的columns
    const columns = [
      {
        title: '设备名称',
        dataIndex: 'deviceName',
        width: '40%',
        align: 'center',
        slots: {
          customRender: 'deviceName'
        }
      },
      {
        title: '定位模式',
        dataIndex: 'mode',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'mode'
        }
      },
      {
        title: '星数',
        dataIndex: 'star',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'star'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    // 轨迹模式的columns
    const columnsTrip = [
      {
        title: '序号',
        dataIndex: 'index',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'index'
        }
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        width: '35%',
        align: 'center',
        slots: {
          customRender: 'startTime'
        }
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        width: '35%',
        align: 'center',
        slots: {
          customRender: 'endTime'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    window._AMapSecurityConfig = {
      securityJsCode: '82f23caa24603b4945140897b8b5be64'
    }
    // 对应后台数据表
    let model = reactive(new Deviceinfo())
    model.configData = 'json'
    // model.id = '270'
    const rangpickdate = ref()

    // 定位表格数据
    const dataGps = ref()
    dataGps.value = []

    // 轨迹表格数据
    const dataTrip = ref()
    dataTrip.value = []

    // 轨迹数据
    // const lineArr = ref([])

    // 轨迹中设备下拉框 设备列表中全部设备的名称及sn
    const deviceByList = ref([])

    // 显示卡片
    const showCard = ref(false)

    // 定位追踪模式
    let positionMode = 0

    // 追踪Marker
    let followMarker = null
    // 追踪经纬度下标
    let followNum = null
    // 追踪文字标识
    let followText = null

    // 显示轨迹动画控制板
    const showTripCtrl = ref(false)

    // 经纬度
    const jing = ref(0)
    const wei = ref(0)

    // 是否已经添加点标志
    const addFlag = ref([])

    // new AMap.Map 对象
    let lmap = null
    const map = shallowRef(null)
    let tileLayer

    // 轨迹marker
    // const marker = ref()
    let marker_follow
    // 起点marker
    let startMarker = null
    // 终点marker
    // let endMarker = null
    let if_setStartMarker = false
    let animatedMarker
    let traceLine = null
    // const latlngs = [[34.215589, 108.955952], [34.215589, 108.965952], [34.215589, 108.975952], [34.215589, 108.985952], [34.215589, 108.995952]]
    let latlngs_trip = []
    const latlngs_trace = []
    const latlng1 = []
    const latlng2 = []
    // const latlng_dot = []

    const lineOption = {
      color: 'blue',
      fillColor: '#CCCCFF',
      // dashArray: [8, 12],
      fill: false,
      opacity: 0.9,
      weight: 6
    }
    let layerGroup
    let marklayers = []
    let tracelayers = []
    const markerSpead = ref(50)
    // 地图在线离线模式，默认在线
    const mapOnline = ref('在线地图')
    // 轨迹map
    // const mapTrip = ref()

    let table_key = '1'

    // AMap
    // const aMap = ref()

    // linearr
    // const lineArr = ref([[116.478935, 39.997761], [116.478939, 39.997825], [116.478912, 39.998549], [116.478912, 39.998549], [116.478998, 39.998555], [116.478998, 39.998555], [116.479282, 39.99856], [116.479658, 39.998528], [116.480151, 39.998453], [116.480784, 39.998302], [116.480784, 39.998302], [116.481149, 39.998184], [116.481573, 39.997997], [116.481863, 39.997846], [116.482072, 39.997718], [116.482362, 39.997718], [116.483633, 39.998935], [116.48367, 39.998968], [116.484648, 39.999861]])

    // 图标对象
    // const icon = ref()

    // // 开始定时器标志
    // const flag = ref(0)

    // 定时器
    const getBegin = ref()
    const getFollowTrip = ref()

    // 蓝点icon
    const ico_blue = require('@/assets/img/blue3.png')
    const ico_b = require('@/assets/img/ico-b.png')
    const ico_v = require('@/assets/img/ico-v.png')
    const ico_h = require('@/assets/img/ico-h.png')
    const ico_p = require('@/assets/img/ico-p.png')
    const ico_start = require('@/assets/img/blue2.png')
    let devHandIcon = null

    const devIcon = L.icon({
      iconUrl: ico_start,
      iconSize: [52, 52], //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
      iconAnchor: [12, 20],
      popupAnchor: [0, 0]
    })
    // 设备定位数据
    const dataPosition = ref([])
    // const infoWindow = ref()

    // 设备信息详情显示控制
    const showDiscription = ref()

    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const topoRequestAction = new TopoRequestAction()
    const locationTripAction = new LocationTrip()
    const pageDirectAction = new PageDirectAction()

    const showDis = () => {
      showDiscription.value = false
    }

    const disabledStartDate = startValue => {
      if (!startValue || !endValue.value) {
        return false
      }
      return startValue.valueOf() > endValue.value.valueOf()
    }

    const disabledEndDate = endValue => {
      if (!endValue || !startValue.value) {
        return false
      }

      return startValue.value.valueOf() >= endValue.valueOf()
    }

    const handleStartOpenChange = open => {
      if (!open) {
        endOpen.value = true
      }
    }

    const handleEndOpenChange = open => {
      endOpen.value = open
    }

    watch(startValue, () => {
      console.log('startValue', startValue.value)
    })
    watch(endValue, () => {
      console.log('endValue', endValue.value)
    })

    // 切换标签页触发
    const changeTable = (activeKey) => {
      addFlag.value = []
      showTripCtrl.value = false
      clearInterval(getBegin.value)
      table_key = activeKey
      const path = (Router.currentRoute.value.fullPath).slice(0, 11)
      console.log('path:' + path)
      switch (activeKey) {
        case '1':
          // initMap()
          getDeviceinfoById()
          getBegin.value = setInterval(function () {
            if ((Router.currentRoute.value.fullPath).slice(0, 11) === '/mapOffLine') {
              getTopo()
              set()
            } else {
              clearInterval(getBegin.value)
            }
          }, 3000)
          break
        case '2':
          showTripCtrl.value = true
          //   getTrip()
          getTopo()
          // 获取全部设备的名称
          getAllDeviceName()
          break
        case '3':
          break
        case '4':
          // alert(4)
          break
      }
    }

    // 获取全部设备的名称
    const getAllDeviceName = () => {
      for (let i = 0; i < dataGps.value.length; i++) {
        allDeviceName.value[i] = {
          deviceName: dataGps.value[i].deviceName,
          sn: dataGps.value[i].sn
        }
      }
    //   console.log(allDeviceName)
    }

    // 显示卡片
    const ShowCard = () => {
      showCard.value = !showCard.value
      // 打开选项则关闭设备信息详情
      showDiscription.value = false
    }

    const mapModeSelect = value => {
      console.log(`selected ${value}`)
      console.log(mapOnline.value)
      if (mapOnline.value === '1') {
        Router.push({
          path: '/'
        })
      } else if (mapOnline.value === '0') {
        Router.push({
          path: 'mapOffLine'
        })
      }
    }

    // 回调函数错误处理
    const callbackError = () => {
      if (table_key === '1') {
        message.warning('当前无设备在线')
      }
    }
    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data
      }
    }

    const getNowSuccess = (data) => {
      // console.log(data)
    }

    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }

    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }

    const getTopo = () => {
      // model.id = 279
      topoRequestAction.query(baseRequestData, getTopoSuccess, callbackError, null)
    }

    const getNow = () => {
      // alert('getNow')
      locationTripAction.query2(baseRequestData, getNowSuccess, callbackError, null)
    }

    const timeseq = () => {
      dataTrip.value.length = 0
      //   model.sn = 'CBFCT0807094'
      // model.id = 277
      // if (rangpickdate.value !== undefined) {
      //   baseParam.start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
      //   baseParam.end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
      // }
      locationTripAction.query3(baseRequestData, timeseqSuccess, callbackError, null)
    }
    // 获取轨迹数据
    const getLocationTrip = () => {
      let i = ''
      trip_select = baseRequestData.entity.sn
      console.log('trip_select in getLocationTrip ' + trip_select)
      locationTripAction.query(baseRequestData, getLocationTripSuccess, callbackError, null)
      for (i in config_data) {
        if (trip_select === config_data[i].sn) {
          select_configData = config_data[i]
          break
        }
      }
    }

    // const selectDeviceChange = key => {
    //   alert(key)
    // }

    // 获取轨迹成功回调
    const getLocationTripSuccess = (data) => {
      clearMarker()
      console.log(data.data[0].Time)
      console.log('获取轨迹成功')
      latlngs_trip = []
      data.data.forEach((item) => {
        // console.log(item)
        // latlng_dot[0] = item.Wei
        // latlng_dot[1] = item.Jing
        latlngs_trip.push([item.Wei, item.Jing])
      })

      clearInterval(getBegin.value)
      // clearMarker()
      if (startMarker != null) {
        lmap.removeLayer(startMarker)
      }
      if (marker_follow != null) {
        // lmap.removeLayer(marker_follow)
      }
      if (layerGroup !== null) {
        // layerGroup.clearLayers()
      }

      // 关闭选项卡片
      showCard.value = false
      // 打开设备详细信息表格
      // showDiscription.value = true
      showTripCtrl.value = true
      if_setStartMarker = false

      // 找到对应轨迹的设备
      console.log('config_data in trip ' + JSON.stringify(config_data))
      if (select_configData.device_type === 1) {
        devHandIcon = L.icon({
          iconUrl: ico_h,
          iconSize: [32, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
        })
      } else if (select_configData.device_type === 2) {
        devHandIcon = L.icon({
          iconUrl: ico_b,
          iconSize: [52, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
        })
      } else if (select_configData.device_type === 3) {
        devHandIcon = L.icon({
          iconUrl: ico_v,
          iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
        })
      } else if (select_configData.device_type === 4) {
        devHandIcon = L.icon({
          iconUrl: ico_p,
          iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
        })
      }
      /*
      if (if_setStartMarker === false) {
        startMarker = L.marker(latlngs_trip[0], { icon: devIcon }).addTo(lmap).on('click', function (e) {
          handleMapMarerClick(select_configData)
        })
        if_setStartMarker = true
      }

          marker_follow = L.marker([dataPosition.value[followNum].wei, dataPosition.value[followNum].jing], { icon: devHandIcon }).addTo(map).on('click', function (e) {
            handleMapMarerClick(dataPosition.value[followNum])
          })
          */
      lmap.setView(latlngs_trip[0])
      traceLine = L.polyline(latlngs_trip, lineOption).bindPopup('设备:' + select_configData.sn + '轨迹 <br>').addTo(lmap)

      animatedMarker = L.animatedMarker(latlngs_trip, {
        icon: devHandIcon,
        interval: 1000 / markerSpead.value,
        autoStart: false,
        onEnd: function () {
          lmap.removeLayer(animatedMarker)
          lmap.removeLayer(startMarker)
          tracelayers.push(traceLine)
          const length = latlngs_trip.length
          const lastPos = latlngs_trip[length - 1]
          select_configData.wei = lastPos[0]
          select_configData.jing = lastPos[1]
          marker_follow = L.marker(lastPos, { icon: devHandIcon }).addTo(lmap).on('click', function (e) {
            handleMapMarerClick(select_configData)
          })
        }
      }).addTo(lmap)
    }

    // 将时间戳转为YYYY-MM-DD HH:mm:ss 格式
    const formatData = (timestamp) => {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const sec = date.getSeconds()

      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + sec
    }

    // 获取分段轨迹成功回调
    const timeseqSuccess = (data) => {
      let k = 0
      for (let i = 0; i < data.data.length; i += 2) {
        dataTrip.value[k] = {
          key: i / 2,
          index: (i / 2) + 1,
          startTime: formatData(data.data[i].Time),
          endTime: formatData(data.data[i + 1].Time)
        }
        k++
      }
    }

    // 点击分段轨迹后面的绘制按钮，绘制相应轨迹
    const drawTrip = (key) => {
      baseParam.start_time = dataTrip.value[key].startTime
      baseParam.end_time = dataTrip.value[key].endTime

      getLocationTrip()
    }

    // 获取拓扑成功回调

    const getTopoSuccess = (data) => {
      config_data = data.data.configData
      writeDataGps(data)
      writeDataPosition(data)
      /**
       * 使用高德经纬度转换API
       */
      //   AMap.convertFrom([jing.value, wei.value], 'gps', function (status, result) {
      // if (result.info === 'ok') {
      //   jing.value = result.locations[0].lng
      //   wei.value = result.locations[0].lat
      //   const lnglats = result.locations // Array.<LngLat>
      // }
      //   })
    }

    /**
     * gps定位数据方法，包含：设备名称、模式、星数
     * @param {*} data 请求返回的数据源
     */
    const writeDataGps = (data) => {
      let i = ''
      let k = 0
      for (i in data.data.configData) {
        let modeNameTemp = '/'
        if (data.data.configData[i].location_mode !== 0 && data.data.configData[i].location_mode !== null) {
          // 做模式参数与模式名称进行映射
          switch (data.data.configData[i].location_mode) {
            case 1:
              modeNameTemp = 'GPS模式'
              break
            case 2:
              modeNameTemp = '北斗模式'
              break
            case 3:
              modeNameTemp = 'GPS+北斗'
              break
          }
          dataGps.value[k] = {
            key: k.toString(),
            deviceName: data.data.configData[i].device_name,
            mode: modeNameTemp,
            star: data.data.configData[i].satellite_number,
            sn: data.data.configData[i].sn
          }
          k++
        }
      }
    }

    /**
     * 获取设备管理中设备维护的所有设备
     */
    const getDeviceByList = (dataDevice) => {
      deviceByList.value.length = 0
      for (let i = 0; i < dataDevice.data.content.length; i++) {
        deviceByList.value.push({
          name: dataDevice.data.content[i].device_name,
          sn: dataDevice.data.content[i].sn
        })
      }
    }

    const handleMapMarerClick = (item) => {
      // console.info(item)
      const content =
      '<p >设备信息</p>' +
        '<ul class="info">' +
          `<li><span>设备SN：</span>${item.sn}</li>` +
          `<li><span>设备IP：</span>${item.ip_addr}</li>` +
          `<li><span>设备名称：</span>${item.deviceName}</li>` +
        '</ul>' +
      '<div style="width:100%;height:80px" id="pptnMapChart"></div>' +
      ' </div>'
      L.popup({ minWidth: 150, closeButton: false })
        .setLatLng([item.wei, item.jing])
        .setContent(content)
        .openOn(lmap)
    }

    /**
     * GPS定位经纬度信息方法，包含 设备名称、经纬度、高程
     * description： 此方法在每次获取定位信息的时候调用一次，data为响应数据源。
     */
    const writeDataPosition = (data) => {
      // configData遍历变量
      let i = ''
      //   // 循环变量，用于遍历定位dataPosition数组下标
      //   let k = 0
      // marker的临时变量，暂存new Marker使用
      let markerTemp
      let text
      // 是否可以创建标志变量
      let canCreatMarkerFlag = true
      /*
*/
      for (i in data.data.configData) {
        // 首先遍历dataPostiton，判断对应的设备是否存在，如果不存在则将对应设备的Mark进行创建
        for (let j = 0; j < dataPosition.value.length; j++) {
          if (data.data.configData[i].device_name === dataPosition.value[j].deviceName) {
            // 将对应设备dataPosition的经纬度、高程、GPS模式状态信息进行设置
            dataPosition.value[j].jing = data.data.configData[i].longitude
            dataPosition.value[j].wei = data.data.configData[i].latitude
            dataPosition.value[j].altitude = data.data.configData[i].altitude
            dataPosition.value[j].locationMode = data.data.configData[i].location_mode
            dataPosition.value[j].sn = data.data.configData[i].sn
            dataPosition.value[j].ptxPower = data.data.configData[i].ptx_power
            dataPosition.value[j].stxPower = data.data.configData[i].stx_power
            dataPosition.value[j].scenterFreq = data.data.configData[i].scenter_freq
            dataPosition.value[j].pcenterFreq = data.data.configData[i].pcenter_freq
            dataPosition.value[j].remainingBattery = data.data.configData[i].remaining_battery
            dataPosition.value[j].inNetwork = data.data.configData[i].in_network
            dataPosition.value[j].ip_addr = data.data.configData[i].ip_addr
            dataPosition.value[j].hosttemperature = data.data.configData[i].hosttemperature

            // 如果找到了则将创建Marker的标志变量设为false——不用new Marker (dataPositon中存在)
            canCreatMarkerFlag = false
            // 停止继续循环遍历
            break
          }
        }
        // 如果遍历完dataPosition数组还没有找到相同的设备，那么将Marker添加进来
        // 并且GPS模式不能是关闭状态
        console.info('0=======================================================')
        if (canCreatMarkerFlag === true && data.data.configData[i].location_mode !== 0 && data.data.configData[i].location_mode !== null) {
          console.info('1=======================================================')
          if (data.data.configData[i].device_type === 1) {
            devHandIcon = L.icon({
              iconUrl: ico_h,
              iconSize: [32, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          if (data.data.configData[i].device_type === 2) {
            devHandIcon = L.icon({
              iconUrl: ico_b,
              iconSize: [52, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×                                     52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          if (data.data.configData[i].device_type === 3) {
            devHandIcon = L.icon({
              iconUrl: ico_v,
              iconSize: [52, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          if (data.data.configData[i].device_type === 4) {
            devHandIcon = L.icon({
              iconUrl: ico_p,
              iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          markerTemp = L.marker([data.data.configData[i].longitude, data.data.configData[i].latitude], { icon: devHandIcon })
          // .addTo(lmap)
          // .on('click', (e) => {
          //   handleMapMarerClick(data.data.configData[i])
          //   clickPoint()
          // })
          console.info('2=======================================================')
          dataPosition.value.push({
            deviceName: data.data.configData[i].device_name,
            jing: data.data.configData[i].longitude,
            wei: data.data.configData[i].latitude,
            altitude: data.data.configData[i].altitude,
            mark: markerTemp,
            text: text,
            locationMode: data.data.configData[i].location_mode,
            sn: data.data.configData[i].sn,
            ptxPower: data.data.configData[i].ptx_power,
            stxPower: data.data.configData[i].stx_power,
            scenterFreq: data.data.configData[i].scenter_freq,
            pcenterFreq: data.data.configData[i].pcenter_freq,
            remainingBattery: data.data.configData[i].remaining_battery,
            inNetwork: data.data.configData[i].in_network,
            device_type: data.data.configData[i].device_type,
            ip_addr: data.data.configData[i].ip_addr,
            hosttemperature: data.data.configData[i].hosttemperature
          })
        }
        // 刷新数据集
        if (showDiscription.value) {
          // 在dataPosition中找到对应的设备信息，并且将其中对应的信息创建信息体，填入展示表格
          for (let d = 0; d < dataPosition.value.length; d++) {
            // 在dataPostion中找到点击的数据集
            if (dataPosition.value[d].deviceName === descriptionDeviceInfo.value.deviceName) {
              wirteDescriptionDeviceInfo(d)
            }
          }
        }
      }
    }

    const getTrip = () => {
    //   console.log(rangpickdate)
      if (rangpickdate.value !== undefined) {
        baseParam.start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
        baseParam.end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
      }
      //   alert(baseParam.start_time)
      //   alert(baseParam.end_time)
      getLocationTrip()
    }

    const clearMarker = () => {
      if (layerGroup !== null) {
        layerGroup.clearLayers()
      }
      if (marker_follow != null) {
        lmap.removeLayer(marker_follow)
      }
      if (startMarker != null) {
        lmap.removeLayer(startMarker)
      }
      if (animatedMarker != null) {
        if (positionMode === 1) {
          animatedMarker.stop()
        }
        lmap.removeLayer(animatedMarker)
      }
      clearInterval(getFollowTrip.value)

      marklayers.forEach(function (item) {
        // console.log(item)
        lmap.removeLayer(item)
      })
      marklayers = []

      if (traceLine !== null) {
        lmap.removeLayer(traceLine)
      }

      console.info('In tracelayers.forEach')
      tracelayers.forEach(function (item) {
        // console.log(item)
        lmap.removeLayer(item)
      })
      tracelayers = []

      if_setStartMarker = false
    }

    const initMap = () => {
      const config_str = sessionStorage.getItem('mesh_Config')
      let json_obj = {}
      if (config_str != null) {
        json_obj = JSON.parse(config_str)
        if (json_obj.map_server == null) {
          console.log('离线地图地址无效！')
          return
        }
      }

      const center = [34.216602, 108.953696]

      const tileLayer = L.tileLayer(json_obj.map_server, {
        minZoom: 1,
        maxZoom: 15,
        tms: true
      })

      lmap = L.map('container1', {
        zoom: 18,
        layers: [tileLayer],
        zoomControl: true,
        attributionControl: false,
        center: center
      })

      lmap.setView(center)

      lmap.locate({
        setView: true,
        maxZoom: 18
      })

      initMapValue()
    }

    /**
 * 初始化对象，在加载地图的时候完成对象的创建
 */
    const initMapValue = () => {
      lmap.on('locationfound', function (e) {
        const radius = e.accuracy / 30
        // L.marker(e.latlng).addTo(lmap).bindPopup(' ')
        // L.marker([34.21, 108.95], { icon: devHandIcon }).addTo(map).bindPopup('你就在这个圈内')
        L.circle(e.latlng, radius).addTo(lmap)
        // L.circle([34.215589, 108.955952], radius).addTo(map)
        console.log('定位成功=====>', e)
      })
      lmap.on('locationerror', function (e) {
        console.log('定位出错=====>', e)
      })
    }

    const descriptionDeviceInfo = ref({
      deviceName: '',
      stxPower: '',
      ptxPower: '',
      scenterFreq: '',
      pcenterFreq: '',
      rssi: '--',
      snr: '--',
      remainingBattery: '',
      template: '--',
      inNetwork: '',
      jing: '',
      wei: '',
      gao: ''

    })

    /**
     * 描述：覆盖物的（定位图标）点击事件
     * 对象：定位点

    const clickPoint = (e) => {
    // 关闭选项卡片
      showCard.value = false
      // 打开设备详细信息表格
      showDiscription.value = true

      descriptionDeviceInfo.value.deviceName = e.target._originOpts.markerName
    //   infoWindow.value.open(map.value, [e.target._originOpts.position.lng, e.target._originOpts.position.lat])
    }
    */
    /**
     * 写入descriptionDeviceInfo方法
     * @param {*} key
     */

    const wirteDescriptionDeviceInfo = (d) => {
      descriptionDeviceInfo.value.wei = deleteNull(dataPosition.value[d].wei)
      descriptionDeviceInfo.value.jing = deleteNull(dataPosition.value[d].jing)
      descriptionDeviceInfo.value.gao = dataPosition.value[d].altitude
      descriptionDeviceInfo.value.ptxPower = dataPosition.value[d].ptxPower
      descriptionDeviceInfo.value.stxPower = deleteNull(dataPosition.value[d].stxPower)
      //   descriptionDeviceInfo.value.stxPower = dataPosition.value[d].stxPower
      descriptionDeviceInfo.value.scenterFreq = dataPosition.value[d].scenterFreq
      descriptionDeviceInfo.value.pcenterFreq = dataPosition.value[d].pcenterFreq
      descriptionDeviceInfo.value.remainingBattery = dataPosition.value[d].remainingBattery
      if (dataPosition.value[d].inNetwork === 1) {
        descriptionDeviceInfo.value.inNetwork = '在线'
      } else if (dataPosition.value[d].inNetwork === 0) {
        descriptionDeviceInfo.value.inNetwork = '在线不在网'
      } else { descriptionDeviceInfo.value.inNetwork = '离线' }
      descriptionDeviceInfo.value.ip_addr = dataPosition.value[d].ip_addr
      descriptionDeviceInfo.value.hosttemperature = dataPosition.value[d].hosttemperature
    }

    const deleteNull = (befor) => {
      if (befor === null || befor === '' || befor === undefined) {
        return '--'
      } else {
        return befor
      }
    }

    const follow = key => {
      positionMode = 0
      // clearInterval(getBegin.value)
      clearMarker()
      // 关闭选项卡片
      showCard.value = false
      // 打开设备详细信息表格
      showDiscription.value = true

      // 找到追踪设备的Mark
      for (let i = 0; i < dataPosition.value.length; i++) {
        if (dataGps.value[key].sn === dataPosition.value[i].sn) {
          followMarker = dataPosition.value[i].mark
          followText = dataPosition.value[i].text
          followNum = i
          set()
          model.sn = dataPosition.value[i].sn
          // 对设备详细描述的列表名称赋值（追踪哪个设备显示那个设备）
          descriptionDeviceInfo.value.deviceName = dataPosition.value[i].deviceName
          getNow()

        /*
          // 模拟数据测试轨迹
          getFollowTrip.value = setInterval(function () {
            if (latlng1[0] === undefined) {
              latlng1[0] = dataPosition.value[i].wei
              latlng1[1] = dataPosition.value[i].jing
            }
            if (latlng2[0] === undefined) {
              latlng2[0] = dataPosition.value[i].wei
              latlng2[1] = dataPosition.value[i].jing
            }
            latlngs_trace = []
            latlng1[0] = latlng2[0]
            latlng1[1] = latlng2[1]
            latlng2[0] += 0.001
            latlng2[1] += 0.001
            latlngs_trace.push(latlng1)
            latlngs_trace.push(latlng2)
            set()
          }, 3000)
        */
        }
      }
    }

    const allPosition = () => {
      clearInterval(getFollowTrip.value)
      positionMode = 0
      clearMarker()
      set()
    }

    const stopFollow = () => {
      clearInterval(getFollowTrip.value)
      positionMode = 2
      // set()
      clearMarker()
      allPosition()
    }

    const set = () => {
      let marker = null
      // 调用 moveTo 方法
      switch (positionMode) {
        case 0 :
          // map.value.clearMap()
          for (let i = 0; i < dataPosition.value.length; i++) {
            // map.value.add(dataPosition.value[i].mark)
            // map.value.add(dataPosition.value[i].text)
            if (dataPosition.value[i].device_type === 1) {
              devHandIcon = L.icon({
                iconUrl: ico_h,
                iconSize: [32, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
              })
            }
            if (dataPosition.value[i].device_type === 2) {
              devHandIcon = L.icon({
                iconUrl: ico_b,
                iconSize: [52, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
              })
            }
            if (dataPosition.value[i].device_type === 3) {
              devHandIcon = L.icon({
                iconUrl: ico_v,
                iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
              })
            }
            if (dataPosition.value[i].device_type === 4) {
              devHandIcon = L.icon({
                iconUrl: ico_p,
                iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
              })
            }
            marker = L.marker([dataPosition.value[i].wei, dataPosition.value[i].jing], { icon: devHandIcon }).addTo(lmap).on('click', (e) => {
              handleMapMarerClick(dataPosition.value[i])
            })
            L.DomEvent.on(marker, 'click', function (ev) {
              L.DomEvent.stopPropagation(ev)
            })
            marklayers.push(marker)

            /* dataPosition.value[i].mark.moveTo([dataPosition.value[i].jing, dataPosition.value[i].wei], {
              duration: 2500
            })
            dataPosition.value[i].text.moveTo([dataPosition.value[i].jing, dataPosition.value[i].wei], {
              duration: 2500
            }) */
          }
          layerGroup = L.layerGroup(marklayers)
          lmap.addLayer(layerGroup)
          if (dataPosition.value.length !== 0) { lmap.setView([dataPosition.value[0].wei, dataPosition.value[0].jing]) }
          break
        case 1:
          if (dataPosition.value[followNum].device_type === 1) {
            devHandIcon = L.icon({
              iconUrl: ico_h,
              iconSize: [32, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          if (dataPosition.value[followNum].device_type === 2) {
            devHandIcon = L.icon({
              iconUrl: ico_b,
              iconSize: [52, 52] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }
          if (dataPosition.value[followNum].device_type === 3) {
            devHandIcon = L.icon({
              iconUrl: ico_v,
              iconSize: [52, 32] //  图标的大小    【值1，值2】 为具体你自定义图标的尺寸，比如我图标尺寸是32×52，表示该图标：宽度32像素，高度：52像素，那么值1:就是32，值2：就是52
            })
          }

          if (if_setStartMarker === false) {
            startMarker = L.marker([dataPosition.value[followNum].wei, dataPosition.value[followNum].jing], { icon: devIcon }).addTo(lmap).on('click', function (e) {
              handleMapMarerClick(dataPosition.value[followNum])
            })
            marklayers.push(startMarker)
            layerGroup = L.layerGroup(marklayers)
            if_setStartMarker = true
            latlng1[0] = dataPosition.value[followNum].wei
            latlng1[1] = dataPosition.value[followNum].jing
          }
          /*
          marker_follow = L.marker([dataPosition.value[followNum].wei, dataPosition.value[followNum].jing], { icon: devHandIcon }).addTo(map).on('click', function (e) {
            handleMapMarerClick(dataPosition.value[followNum])
          })
          */
          /*
          // 模拟数据测试轨迹
          getFollowTrip.value = setInterval(function () {
            if (latlng1[0] === undefined) {
              latlng1[0] = dataPosition.value[i].wei
              latlng1[1] = dataPosition.value[i].jing
            }
            if (latlng2[0] === undefined) {
              latlng2[0] = dataPosition.value[i].wei
              latlng2[1] = dataPosition.value[i].jing
            }
            latlngs_trace = []
            latlng1[0] = latlng2[0]
            latlng1[1] = latlng2[1]
            latlng2[0] += 0.001
            latlng2[1] += 0.001
            latlngs_trace.push(latlng1)
            latlngs_trace.push(latlng2)
            set()
          }, 3000)
        */
          latlng2[0] = dataPosition.value[followNum].wei
          latlng2[1] = dataPosition.value[followNum].jing
          latlngs_trace.push(latlng1)
          latlngs_trace.push(latlng2)

          latlng1[0] = latlng2[0]
          latlng1[1] = latlng2[1]
          animatedMarker = L.animatedMarker(latlngs_trace, {
            icon: devHandIcon,
            interval: 1000,
            onEnd: function () {
              lmap.removeLayer(animatedMarker)
              traceLine = L.polyline(latlngs_trace, lineOption).bindPopup('设备:' + dataPosition.value[followNum].sn + '轨迹 <br>').addTo(lmap)
              tracelayers.push(traceLine)
              dataPosition.value[followNum].wei = latlng2[0]
              dataPosition.value[followNum].jing = latlng2[1]
              marker_follow = L.marker(latlng2, { icon: devHandIcon }).addTo(lmap).on('click', function (e) {
                handleMapMarerClick(dataPosition.value[followNum])
              })
              marklayers.push(marker_follow)
              layerGroup = L.layerGroup(marklayers)
              setTimeout(function () {
                lmap.removeLayer(marker_follow)
              }, 1000)
            }
          }).addTo(lmap)

          // console.info(animatedMarker)
          // console.info(devIcon)
          // console.info(latlngs)
          // console.info(lineOption)

          break
        case 2:
          followMarker.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
          followText.moveTo([dataPosition.value[followNum].jing, dataPosition.value[followNum].wei], {
            duration: 2500
          })
          break
        default:
          break
      }
    }

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListFinally = () => {
      console.log('getListFinally')
    }

    const getListSuccess = (data) => {
      Device.value.length = 0
      for (let i = 0; i < data.data.content.length; i++) {
        if (data.data.content[i].in_network !== 2 && data.data.content[i].in_network !== null) {
          Device.value.push({
            name: data.data.content[i].device_name,
            value: data.data.content[i].id
          })
        }
      }
      // 将默认选择的节点设置为列表第一项
      if (Device.value.length > 0) {
        model.id = Device.value[0].value
      }

      getDeviceByList(data)
      console.log('=====================')
      console.log(deviceByList)
      model.id = data.data.content[0].id
      console.info('model.id:' + model.id)
      getDeviceinfoById()
    }

    getBegin.value = setInterval(function () {
      if ((Router.currentRoute.value.fullPath).slice(0, 11) === '/mapOffLine') {
        getTopo()
        set()
      } else {
        clearInterval(getBegin.value)
      }
    }, 3000)
    onMounted(() => {
      // model.id = pageDirectAction.getCurrentRouteValue()
      // model.id = 279
      getList()
      getDeviceinfoById()
      initMap()
      mapOnline.value = '离线地图'
    //   setFlag()
    })

    onUnmounted(() => {
      // 销毁地图，并清空地图容器
      if (DebugModeOption.value === 0) {
        stopAnimation()
        lmap.removeLayer(tileLayer)
        lmap.off()
        // lmap.remove()
        // 地图对象赋值为null
        lmap = null
        // 清除地图容器的 DOM 元素
        document.getElementById('container1').remove() // "container1" 为指定 DOM 元素的id
      }
    })
    const startAnimation = () => {
      animatedMarker.start()
    }
    const leaveAnimation = () => {
      stopAnimation()
      allPosition()
    }
    const stopAnimation = () => {
      if (startMarker != null) { lmap.removeLayer(startMarker) }
      if (traceLine != null) { lmap.removeLayer(traceLine) }
      if (tracelayers != null) { lmap.removeLayer(tracelayers) }
      if (followMarker != null) { lmap.removeLayer(followMarker) }
      if (animatedMarker != null) { lmap.removeLayer(animatedMarker) }
      if (marker_follow != null) { lmap.removeLayer(marker_follow) }

      layerGroup.clearLayers()
    }
    const pauseAnimation = () => {
      animatedMarker.stop()
    }
    const resumeAnimation = () => {
      animatedMarker.start()
    }

    return {
      activeKey: ref('1'),
      EnvironmentOutlined,
      model,
      baseRequestData,
      ico_blue,
      dataSource,
      baseParam,
      LabelCaption,
      DebugModeOption,
      DeviceinfoColumns,
      pageDirectAction,
      jing,
      wei,
      map,
      lmap,
      showCard,
      showTripCtrl,
      columns,
      dataGps,
      pagination,
      startValue,
      endValue,
      endOpen,
      allDeviceName,
      showDiscription,
      dayjs,
      rangpickdate,
      deviceByList,
      columnsTrip,
      showDis,
      dataTrip,
      drawTrip,
      markerSpead,
      timeseq,
      //   selectDeviceChange,
      disabledStartDate,
      disabledEndDate,
      handleStartOpenChange,
      handleEndOpenChange,
      stopFollow,
      allPosition,
      follow,
      startAnimation,
      stopAnimation,
      pauseAnimation,
      resumeAnimation,
      changeTable,
      ShowCard,
      descriptionDeviceInfo,
      getTrip,
      layerGroup,
      clearMarker,
      mapModeSelect,
      mapOnline,
      tileLayer,
      leaveAnimation
    }
  }
})

</script>
  <style  scoped>
#container1 {
position: absolute;
height: 100%;
width:100%;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.editable-row-operations a {
  margin-right: 8px;
}
</style>
