import baseRequest from '@/request/request'
class VoiceModeRequestAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo/config/voice'
    this.urlSet = `${this.BASE_API_URL}/set`
    this.urlquery = `${this.BASE_API_URL}/query`
  }

  /**
   * 设置
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  set = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
    baseRequest
      .post(this.urlSet, baseRequestData)
      .then((response) => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch((error) => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  };

  /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
  query = (
    baseRequestData,
    successCallback,
    errorCallback,
    finalllyCallback
  ) => {
    baseRequest
      .post(this.urlquery, baseRequestData)
      .then((response) => {
        if (typeof successCallback === 'function') {
          successCallback(response.data)
        }
      })
      .catch((error) => {
        if (typeof errorCallback === 'function') {
          errorCallback(error)
        }
      })
      .finally(() => {
        if (typeof finalllyCallback === 'function') {
          finalllyCallback()
        }
      })
  };
}

export { VoiceModeRequestAction }
