<template>
    <div style="height: 100%;width: 100%;background-color: azure;">
        <div class="page2">
        <Row class='content'>
            <Col span="8">
                <div class="list">
                    <div class="left">
                        <span class='title'><span class="title-8">设备剩余电量</span></span>
                        <span class="angle1"></span>
                        <span class="angle2"></span>
                        <span class="angle3"></span>
                        <span class="angle4"></span>
                        <div class="chart-68">
                            <bar-chart ref="chart3" id="left_3" :config="configData2"></bar-chart>
                        </div>
                    </div>
                </div>
                <div class="list">
                    <div class="left">
                        <span class='title'><span class="title-8">设备温度分析</span></span>
                        <span class="angle1"></span>
                        <span class="angle2"></span>
                        <span class="angle3"></span>
                        <span class="angle4"></span>
                        <div class="chart-68">
                            <bar-chart ref="chart3" id="left_3" :config="configData2"></bar-chart>
                        </div>
                    </div>
                </div>
            </Col>
            <Col span="8">
                <div class="circlePie" id="circlePie">
                </div>
            </Col>
            <Col span="8">
                <div class="list">
                    <div class="right">
                        <span class='title'><span class="title-4">信噪比分析</span></span>

                        <div class="chart-68">
                            <double-bar-chart ref="chart6"></double-bar-chart>
                        </div>

                    </div>
                </div>
                <div class="list">
                    <div class="right">
                        <span class='title'><span class="title-4">信号质量分析</span></span>

                        <div class="chart-68">
                            <double-bar-chart2 ref="chart6"></double-bar-chart2>
                        </div>

                    </div>
                </div>

            </Col>
        </Row>
        <Row class="bottom-list">
        </Row>
    </div>
    </div>
</template>

<script>
const barChart = () => import('./components/barChart')
const doubleBarChart = () => import('./components/doubleBarChart')
const doubleBarChart2 = () => import('./components/doubleBarChart2')

export default {
  name: 'page2',
  props: ['selectRangeDate'],
  components: {
    barChart,
    doubleBarChart,
    doubleBarChart2
  },
  data () {
    return {
    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.page2 {
    height: 100%;
    width: 100%;
    padding: 14px 20px 20px;
    background: #03044A;
    overflow: hidden;

    .content {
        height: 65%;

        .ivu-col {
            height: 100%;
        }

        .circlePie {
            height: 100%;
            padding: 0 0 40px;
            text-align: center;
            position: relative;

            canvas {
                position: absolute;
                left: 50%;
                top: 0;
                transform: translate(-50%, 0);
            }

            #dot {
                background: rgba(0, 0, 0, 0);
            }
        }

        .list {
            height: 48%;

            .left, .right {
                background: #0D1341;
            }

            .left, .right, .center {
                width: 100%;
                height: 90%;
                border: 1px solid #0D2451;
                position: relative;

                #left1, #left2, #left3, #right1, #right2, #right3, #center2 {
                    height: 100%;
                }

                .chart-68 {
                    width: 100%;
                    height: 100%;
                    float: left;
                }

                .chart-32 {
                    width: 32%;
                    height: 100%;
                    float: left;
                }
            }
        }
    }

    .bottom-list {
        height: 35%;

        .ivu-col {
            height: 100%;

            .list {
                height: 100%;
                width: 33.3333%;
                padding-right: 1.5%;
                float: left;

                #bottom_4 {
                    padding: 0 20px;
                }

                .bottom {
                    width: 100%;
                    height: 100%;
                    border: 1px solid #0D2451;
                    position: relative;
                }
            }

            .right-bottom {
                width: 100%;
                padding-right: 0;

                .bottom1 {
                    width: 100%;
                }
            }
        }
    }

}
</style>
