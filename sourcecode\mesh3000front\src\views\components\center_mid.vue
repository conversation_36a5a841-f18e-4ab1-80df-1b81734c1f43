
<template >
    <Map v-if="isMap"></Map>
    <Topo v-if="isTopo"></Topo>

    </template>
<script>
import { defineComponent, onMounted, ref, watch } from 'vue'
import Map from '@/views/components/map_compon.vue'
import Topo from '@/views/components/topo_compon.vue'

export default defineComponent({
  props: ['showMap', 'showTopo'],
  components: {
    // ForkOutlined
    Map,
    Topo
  },
  setup (props) {
    const isMap = ref(true)
    const isTopo = ref(false)
    onMounted(() => {
      // 监听 prop 的变化，当 props发生变化时，更新图表
      watch(() => props.showMap, (newValue, oldValue) => {
        isMap.value = !newValue
      })

      // 监听 prop 的变化，当 props发生变化时，更新图表
      watch(() => props.showTopo, (newValue, oldValue) => {
        isTopo.value = !newValue
      })
    })
    return {
      isMap,
      isTopo
    }
  }
})
</script>
