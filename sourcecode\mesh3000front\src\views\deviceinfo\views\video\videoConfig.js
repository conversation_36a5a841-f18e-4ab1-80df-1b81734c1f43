
// 流媒体服务器地址
export const ServerAddr = {
  // 默认使用相对路径，这样会自动使用当前访问的主机和端口
  serverAddr: window.location.protocol + '//' + window.location.hostname + ':' + window.location.port,
  // 以下是备用配置
  // serverAddr: 'http://**************:8000' // 叶师电脑
  // serverAddr: 'https://203.jiabao.site:8443/', // 本机电脑
  // serverAddr: 'https://www.mesh3000.com:8443/', // 本机电脑
  // serverAddr: 'http://*************:8000' // 21楼服务器
  get videoAddr () {
    return this.serverAddr
  },
  set videoAddr (Addr) {
    this.serverAddr = Addr
  }
}

// 设备 rtsp 视频流端口
export const DeviceRtspPort = {
  deviceRtspPort: ':2000'
}

// 获取地址的方法
export const getServerAddr = () => {
  return ServerAddr.serverAddr
}

// 设置地址方法
export const setServerAddr = (Addr) => {
  ServerAddr.serverAddr = Addr
  return 'success'
}
