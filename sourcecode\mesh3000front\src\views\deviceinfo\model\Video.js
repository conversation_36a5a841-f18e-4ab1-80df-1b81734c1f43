class Video {
  constructor () {
    this.Mode = null
    this.Appmode = null
    this.Framerate = null
    this.Coderate = null
    this.Encoding = null
    this.Resolution = null
    this.HdmiIn = null
  }

  init () {
    this.Mode = null
    this.Appmode = null
    this.Framerate = null
    this.Coderate = null
    this.Encoding = null
    this.Resolution = null
    this.HdmiIn = null
  }
}

export { Video }
