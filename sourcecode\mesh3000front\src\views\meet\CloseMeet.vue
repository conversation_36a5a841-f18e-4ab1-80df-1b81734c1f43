<template>
    <v-card class="ma-2">
      <v-container>
        <v-layout>
          正在关闭......
          <div id="meet" style="position:absolute; width:86%; height:89%"></div>
          <!-- missing -->
        </v-layout>
      </v-container>
    </v-card>
</template>

<script>

import { onMounted, defineComponent } from 'vue'
import Router from '@/router/index'

export default defineComponent({
  components: {
  },
  setup () {
    onMounted(() => {
      Router.push({
        path: 'meetindex'
      })
    })
  }

})
</script>
