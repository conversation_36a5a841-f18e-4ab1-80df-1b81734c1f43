<template>
  <div>
    <a-row>
      <a-col :span="24">
        <a-card title="设备信息详情">
          <a-row :span="24" style="margin-top: 2%;">
            <a-col :span="2" >
              {{LabelCaption.sn.label}}:
            </a-col>

            <a-col :span="4">
              {{deviceInfoTran.sn}}
            </a-col>

            <a-col :span="2" >
              {{LabelCaption.wfmode.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.wfModel}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.device_no.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.device_no}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.device_name.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.device_name}}
            </a-col>
            </a-row>
            <a-row :span="24" style="margin-top: 2%;">
            <a-col :span="2" >
              {{LabelCaption.device_type.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.device_type}}
            </a-col>
            <a-col :span="2" >
              {{LabelCaption.net_id.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.net_id}}
            </a-col>

            <!-- <a-col :span="2" align="center">
              {{LabelCaption.apversion.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.apversion}}
            </a-col> -->

            <a-col :span="2" >
              {{LabelCaption.mac_addr.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.mac_addr}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.ip_addr.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.ip_addr}}
            </a-col>
            </a-row>

            <a-row :span="24" style="margin-top: 2%;">
            <a-col :span="2">
              {{LabelCaption.gate_way.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.gate_way}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.pcenter_freq.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.pcenter_freq}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.pband_width.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.pband_width}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.ptx_power.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.ptx_power}}
            </a-col>
            </a-row>

            <a-row :span="24" style="margin-top: 2%;">
            <a-col :span="2">
              {{LabelCaption.scenter_freq.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.scenter_freq}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.sband_width.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.sband_width}}
            </a-col>
            <a-col :span="2">
              {{LabelCaption.stx_power.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.stx_power}}
            </a-col>
            <a-col :span="2">
              {{LabelCaption.work_mode.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.work_mode}}
            </a-col>
            </a-row>

            <a-row :span="24" style="margin-top: 2%;">

            <a-col :span="2">
              {{LabelCaption.in_network.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.inNetWork}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.distance.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.distance}}
            </a-col>

            <a-col :span="2">
              {{LabelCaption.hdmi_in.label}}:
            </a-col>

            <a-col :span="4" >
              {{deviceInfoTran.hdmi_in}}
            </a-col>

          </a-row>

          <template #actions>
            <a-row type="flex" justify="end" align="top">
              <a-col :span="4">
               <a-space>
                <a-button type="primary" @click="pageDirectAction.goToIndex()">返回</a-button>
               </a-space>
              </a-col>
            </a-row>
          </template>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { onMounted, defineComponent, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { MeshDeviceInfoDetailAction } from '@/views/deviceinfo/action/MeshDeviceInfoDetailAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'

import {
  BandWidthOptions,
  WorkModeOptions,
  TrunkOptions,
  RoutingHealthIndexOptions,
  InNetworkOptions,
  DistanceOptions,
  HDMIinOptions,
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  name: 'Deviceinfo-Detail',
  setup () {
    // 对应后台数据表
    let model = reactive(new Deviceinfo())

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const pageDirectAction = new PageDirectAction()
    const requestAction = new MeshDeviceInfoDetailAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    // ****************** 设备当前DHCP参数 *******************
    const getData = () => {
      requestAction.query(baseRequestData, getDataSuccess, callbackError, null)
    }

    const getDataSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data.configData
        model.configData = model[data.data.sn]
        console.info(model.configData)
        console.info(data)
      }
      // 获取设备数据的时候进行参数显示转换
      tranDeviceInfo()
      // model.configData = data.data.configData
    }
    // ****************** 设备当前DHCP参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getData()
      // getDeviceinfoById()
    })

    const deviceInfoTran = ref({
      // 接入模式（wfModel）
      wfModel: '',
      // 设备类型
      device_type: '',
      // 带宽
      band_width: '',
      // 监听模式
      work_mode: '',
      // 在网状态
      inNetWork: '',
      // 距离
      distance: '',
      // hdmi接入
      hdmi_in: '',
      // 主路带宽
      pband_width: '',
      // 辅路带宽
      sband_width: '',
      // 节点主路固定发射功率
      ptx_power: '',
      // 主路中心频率
      pcenter_freq: '',
      // 节点辅路固定发射功率
      stx_power: '',
      // 辅路中心频率
      scenter_freq: '',
      // 网关地址
      gate_way: '',
      // ip地址
      ip_addr: '',
      // MAC地址
      mac_addr: '',
      // AP版本号
      apversion: '',
      // 网络ID
      net_id: '',
      // 设备名称
      device_name: '',
      // mesh编号
      device_no: '',
      // 序列号
      sn: ''
    })
    // 设备参数与显示转换
    const tranDeviceInfo = () => {
      // 循环变量，用于遍历options
      let i
      // 设备类型
      for (i = 0; i < DeviceTypeOptions.length; i++) {
        if (model.configData.device_type === DeviceTypeOptions[i].value) {
          deviceInfoTran.value.device_type = DeviceTypeOptions[i].name
          break
        }
      }
      // 工作模式
      for (i = 0; i < WfModeOptions.length; i++) {
        if (model.configData.wfmode === WfModeOptions[i].value) {
          deviceInfoTran.value.wfModel = WfModeOptions[i].name
          break
        }
      }
      // 带宽
      // 遍历option
      for (i = 0; i < BandWidthOptions.length; i++) {
      // 设备类型
        if (model.configData.pband_width === BandWidthOptions[i].value) {
          deviceInfoTran.value.pband_width = BandWidthOptions[i].name
          // break
        }
        if (model.configData.sband_width === BandWidthOptions[i].value) {
          deviceInfoTran.value.sband_width = BandWidthOptions[i].name
          // break
        }
      }
      // 监听模式
      for (i = 0; i < WorkModeOptions.length; i++) {
        if (model.configData.work_mode === WorkModeOptions[i].value) {
          deviceInfoTran.value.work_mode = WorkModeOptions[i].name
          break
        }
      }
      // 在网状态
      for (i = 0; i < InNetworkOptions.length; i++) {
        if (model.configData.in_network === InNetworkOptions[i].value) {
          deviceInfoTran.value.inNetWork = InNetworkOptions[i].name
          break
        }
      }
      // 距离
      for (i = 0; i < DistanceOptions.length; i++) {
        if (model.configData.distance === DistanceOptions[i].value) {
          deviceInfoTran.value.distance = DistanceOptions[i].name
          break
        }
      }
      // hdmi接入
      for (i = 0; i < HDMIinOptions.length; i++) {
        if (model.configData.hdmi_in === HDMIinOptions[i].value) {
          deviceInfoTran.value.hdmi_in = HDMIinOptions[i].name
          break
        }
      }
      deviceInfoTran.value.ptx_power = model.configData.ptx_power + ' dbm'
      deviceInfoTran.value.stx_power = model.configData.stx_power + ' dbm'
      //   deviceInfoTran.value.pband_width = model.configData.pband_width
      //   deviceInfoTran.value.sband_width = model.configData.sband_width
      deviceInfoTran.value.gate_way = model.configData.gate_way
      deviceInfoTran.value.ip_addr = model.configData.ip_addr
      deviceInfoTran.value.mac_addr = model.configData.mac_addr
      deviceInfoTran.value.apversion = model.configData.apversion
      deviceInfoTran.value.pcenter_freq = model.configData.pcenter_freq
      deviceInfoTran.value.scenter_freq = model.configData.scenter_freq
      deviceInfoTran.value.net_id = model.configData.net_id
      deviceInfoTran.value.device_name = model.configData.device_name
      deviceInfoTran.value.device_no = model.configData.device_no
      deviceInfoTran.value.sn = model.configData.sn
    }
    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      BandWidthOptions,
      WorkModeOptions,
      TrunkOptions,
      RoutingHealthIndexOptions,
      InNetworkOptions,
      deviceInfoTran,
      DistanceOptions,
      HDMIinOptions,
      DeviceTypeOptions,
      WfModeOptions,
      DeviceinfoColumns,
      pageDirectAction
    }
  }
})
</script>

<style scoped>
</style>
