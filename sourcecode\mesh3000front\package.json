{"name": "mesh3000front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^6.1.0", "@jiaminghi/data-view": "^2.10.0", "animate.css": "^4.1.1", "ant-design-vue": "^3.2.0", "antd-theme-webpack-plugin": "^1.3.9", "axios": "^0.26.1", "babel-polyfill": "^6.26.0", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "doc-vue": "^2.0.1", "echarts": "^5.4.2", "hls.js": "^1.1.5", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "leaflet": "^1.9.4", "leaflet-measure": "^3.1.0", "leaflet-rotatedmarker": "^0.2.0", "leaflet.animatedmarker": "^1.0.0", "lib-jitsi-meet": "^1.0.6", "memfs": "^4.15.1", "mitt": "^3.0.0", "mqtt": "^3.0.0", "n": "^10.1.0", "nvm": "^0.0.4", "paho-mqtt": "^1.1.0", "pinia": "^2.0.13", "proj4leaflet": "^1.0.2", "run": "^1.4.0", "screenfull": "^4.2.0", "serve": "^14.0.1", "url": "^0.11.3", "uuid": "^9.0.1", "video.js": "^7.18.1", "videojs-contrib-hls": "^5.15.0", "vue": "^3.2.13", "vue-router": "^4.0.3", "vue2-leaflet-movingmarker": "^1.1.0", "vue3-video-play": "^1.3.1-beta.6"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.3", "less-loader": "^6.0.0"}}