import { reactive } from 'vue'
export const AuthUi = reactive({
  网络拓扑: {
    name: '网络拓扑',
    id: '001',
    vis: false,
    key: '0-0',
    // 读属性
    statusR: false
  },
  设备维护: {
    name: '设备维护',
    id: '002',
    vis: true
  },
  设备日志: {
    name: '设备日志',
    id: '003',
    vis: false,
    key: '0-2',
    // 读属性
    statusR: false
  },
  设备管理: {
    name: '设备管理',
    id: '004',
    vis: true
  },
  login_item2: {
    name: 'login_item2',
    id: '005',
    vis: true
  },
  login_item3: {
    name: 'login_item3',
    id: '006',
    vis: true
  },
  login_item4: {
    name: 'login_item4',
    id: '007',
    vis: true
  },
  login_item5: {
    name: 'login_item5',
    id: '008',
    vis: true
  },
  login_item6: {
    name: 'login_item6',
    id: '009',
    vis: true
  },
  left_item_topo: {
    name: 'left_item_topo',
    id: '010',
    vis: true
  },
  视频播放: {
    name: '视频播放',
    id: '011',
    vis: false,
    key: '1',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  海康视频播放: {
    name: '海康视频播放',
    id: '012',
    vis: false,
    key: '2',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  系统设置: {
    name: '系统设置',
    id: '013',
    vis: false,
    key: '3',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  Uart0: {
    name: 'Uart0',
    id: '014',
    vis: false,
    key: '0-1-3',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  防火墙: {
    name: '防火墙',
    id: '015',
    vis: false,
    key: '0-1-2',
    status: 2,
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  WiFi: {
    name: 'WiFi',
    id: '016',
    vis: false,
    key: '0-1-0',
    status: 0,
    statusR: 0,
    statusW: 0
  },
  DHCP: {
    name: 'DHCP',
    id: '017',
    vis: false,
    key: '0-1-1',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  Comart: {
    name: 'Comart',
    id: '018',
    vis: false,
    key: '0-1-4',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  接入模式: {
    name: '接入模式',
    id: '019',
    vis: false,
    key: '0-1-5',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  语音: {
    name: '语音',
    id: '020',
    vis: false,
    key: '0-1-6',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  温度设置: {
    name: '温度设置',
    id: '021',
    vis: false,
    key: '0-1-7',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  设备版本信息: {
    name: '设备版本信息',
    id: '022',
    vis: false,
    key: '0-1-8',
    // 读属性
    statusR: false
  },
  Mesh设备信息: {
    name: 'Mesh设备信息',
    id: '023',
    vis: false,
    key: '0-1-9',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  Mesh设置: {
    name: 'Mesh设置',
    id: '024',
    vis: false,
    key: '0-1-A',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  视频参数设置: {
    name: '视频参数设置',
    id: '025',
    vis: false,
    key: '0-1-B',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  HDMI视频播放: {
    name: 'HDMI视频播放',
    id: '026',
    vis: false,
    key: '0-1-C',
    // 读属性
    statusR: false
  },
  用户管理: {
    name: '用户管理',
    id: '027',
    vis: true
  },
  角色管理: {
    name: '角色管理',
    id: '028',
    vis: true
  },
  AT调试: {
    name: 'AT调试',
    id: '029',
    vis: false,
    key: '0-1-D',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  蓝牙设置: {
    name: '蓝牙设置',
    id: '030',
    vis: false,
    key: '0-1-E',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  扫频: {
    name: '扫频',
    id: '031',
    vis: false,
    key: '0-3',
    // 读属性
    statusR: false
  },
  GPS设置: {
    name: 'GPS设置',
    id: '032',
    vis: false,
    key: '0-1-F',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  视频会议: {
    name: '视频会议',
    id: '033',
    vis: false,
    key: '4',
    // 读属性
    statusR: false,
    // 写属性
    statusW: false
  },
  操作日志: {
    name: '操作日志',
    id: '034',
    vis: false,
    key: '0-4',
    // 读属性
    statusR: false
  }
})
