import axios from 'axios'
import ROUTER from '@/router/index'
import { Modal } from 'ant-design-vue'

// 请求超时时长
const REQUEST_TIMEOUT = 30000

// 请求根路径
const SERVER_BASE_URL = '/api'

// create an axios instance
const baseRequest = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  baseURL: SERVER_BASE_URL,
  // request timeout
  timeout: REQUEST_TIMEOUT,
  headers: { 'Content-Type': 'application/json' }
})

console.info(ROUTER)

// 判断是否登录   或者登录国过期让重新登录
// ROUTER.beforeEach((to, from, next) => {
/* const nextRoute = ['home', 'good-list', 'good-detail', 'cart', 'profile']
  // let isLogin = global.isLogin // 是否登录
  // 未登录状态；当路由到nextRoute指定页时，跳转至login
  if (nextRoute.indexOf(to.name) >= 0) {
    if (!isLogin) {
      console.log('what fuck')
      ROUTER.push({ name: 'login' })
    }
  }
  // 已登录状态；当路由到login时，跳转至home
  if (to.name === 'login') {
    if (isLogin) {
      ROUTER.push({ name: 'home' })
    }
  } */
// next()
// })

// 请求拦截器
baseRequest.interceptors.request.use(
  config => {
    // 统一将Object转成String
    config.data = JSON.stringify(config.data)
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// 相应请求器
baseRequest.interceptors.response.use(
  /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

  /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
  response => {
    if (response.data.code === 600) {
      // 返回登录页面弹窗
      let secondsToGo = 5
      const modal = Modal.success({
        width: '800px',
        okText: '好',
        title: '登录过期',
        content: `登录过期，系统将在 ${secondsToGo} 秒后自动进入登录页面.`,
        onOk: goToIndex()
      })
      const interval = setInterval(() => {
        secondsToGo -= 1
        modal.update({
          content: `登录过期，系统将在 ${secondsToGo} 秒后自动进入登录页面.`
        })
      }, 1000)
      setTimeout(() => {
        clearInterval(interval)
        modal.destroy()
        ROUTER.push('/login')
      }, secondsToGo * 1000)
    }

    // 多主机登录
    if (response.data.code === 1019) {
      // 返回登录页面弹窗
      let secondsToGo = 5
      const modal = Modal.success({
        width: '800px',
        okText: '好',
        title: '用户已登录',
        content: `您已在另一台主机登录，被迫下线，系统将在 ${secondsToGo} 秒后退出.`,
        onOk: goToIndex()
      })
      const interval = setInterval(() => {
        secondsToGo -= 1
        modal.update({
          content: `您已在另一台主机登录，被迫下线，系统将在 ${secondsToGo} 秒后退出.`
        })
      }, 1000)
      setTimeout(() => {
        clearInterval(interval)
        modal.destroy()
        ROUTER.push('/login')
      }, secondsToGo * 1000)
    }
    return response
  }
//   error => {
//     console.log('err' + error) // for debug
//     return Promise.reject(error)
//   }
)

const goToIndex = () => {
  ROUTER.push('/login')
}

export default baseRequest
