import baseRequest from '@/request/request'
class MeshDeviceInfoDetailAction {
  constructor () {
    this.BASE_API_URL = '/deviceinfo/devdetail'
    this.urlquery = `${this.BASE_API_URL}/query`
  }

   /**
   * 查询
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlquery, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }
}

export { MeshDeviceInfoDetailAction }
