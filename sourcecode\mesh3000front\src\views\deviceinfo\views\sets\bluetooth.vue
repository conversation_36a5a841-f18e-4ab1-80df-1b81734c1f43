
<template>

<a-card title="蓝牙设置">
    <a-row style="margin-top: 1%;">
        <a-col span="2"></a-col>
        <a-col span="2">
            蓝牙开关：
        </a-col>
        <a-col span="2">
            <a-switch v-model:checked= "checked"  @change="switchChange"  />
        </a-col>
        <a-col span="2">
            <a-button type="primary" :loading="iconLoading" @click="enterIconLoading">搜索设备</a-button>
            <!-- <a-button type="primary" @click="searchDevice()">搜索设备</a-button> -->
        </a-col>
    </a-row>
   <a-form
                :model="model"
                name="nest-messages"
                layout="horizontal"
                @finish="confirm"
                :validate-messages="validate">
                  <a-row justify="center" >
                    <a-table
                        :columns="columns"
                        :data-source="tableData"
                        :pagination="false"
                        :scroll="{ y: 380 }"
                    >
                    <template #operation="{ record }">
                <div class="editable-row-operations">
                  <span>
                    <a-button type="primary" v-if="tableData[record.key].status === '未连接'" @click="connect(record.key)">连接蓝牙</a-button>
                    <a-button type="primary" v-if="tableData[record.key].status === '已连接'" @click="disconnect(record.key)">断开蓝牙</a-button>
                    <a-button type="primary" v-if="tableData[record.key].status === '已连接'" @click="forget(record.key)" style="margin-left: 2%;">忽略此设备</a-button>
                  </span>
                </div>
              </template>
                    </a-table>
                  </a-row>
    <a-row style="margin-top: 1%;" justify="space-around">
    </a-row>
   </a-form>
</a-card>

</template>

<script>
// import { QuestionOutlined } from '@ant-design/icons-vue'
import { onMounted, defineComponent, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { Bluebooth } from '@/views/deviceinfo/action/bluetooth'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BluetoothModel } from '@/views/deviceinfo/model/bluetoothModel'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import {
  VoiceOption
} from '@/views/deviceinfo/constant/options'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
const columns = [
  {
    title: '蓝牙名称',
    dataIndex: 'devname',
    align: 'center',
    width: 300
  },
  {
    title: 'Mac地址',
    dataIndex: 'macaddress',
    align: 'center',
    width: 350
  },
  {
    title: '连接状态',
    dataIndex: 'status',
    align: 'center',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 250,
    slots: {
      customRender: 'operation'
    }
  }
]
const tableData = ref([])
// const data = [...Array(50)].map((_, i) => ({
//   key: i,
//   name: `模拟设备 ${i}`,
//   age: 'a3:33:22:4f',
//   address: '未连接',
//   operation: 'operation'
// }))

export default defineComponent({

  setup () {
    // 开关控件显示
    const checked = ref(false)
    /**
     * 搜索设备的函数，搜索设备的逻辑在这里实现
     */
    const searchDevice = () => {
      model.configData.state = 4
      set()
      getBuletoothModel()
    }

    /**
     * 断开连接
     * @param {*} key
     */
    const disconnect = (key) => {
      model.configData.state = 3
      model.configData.mac = tableData.value[key].macaddress
      set()
    }

    /**
     * 忽略设备
     */
    const forget = (key) => {
      model.configData.state = 2
      model.configData.mac = tableData.value[key].macaddress
      set()
    }

    const iconLoading = ref(false)

    const enterIconLoading = () => {
      searchDevice()
      tableData.value.length = 0
      const getList = setInterval(function () {
        getBuletoothModel()
      }, 3000)
      iconLoading.value = {
      }
      setTimeout(() => {
        iconLoading.value = false
        clearInterval(getList)
      }, 45000)
    }
    /**
     * 连接设备的函数，连接对应设备的逻辑在这里实现
     * @param {key 所选行的key} key
     */
    const connect = (key) => {
      model.configData.state = 1
      model.configData.mac = tableData.value[key].macaddress
      set()
    }
    // 单选按钮风格
    const radioStyle = reactive({
      display: 'flex',
      height: '50px',
      lineHeight: '30px',
      size: 'large'

    })

    const onFinish = values => {
      console.log('Received values of form:', values)
    }

    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const bluetooth = new Bluebooth()
    const bluetoothModel = new BluetoothModel()
    const pageDirectAction = new PageDirectAction()
    model.configData = bluetoothModel

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }
    // const confirm = (record) => {
    //   Modal.confirm({
    //     title: '修改设备信息',
    //     icon: createVNode(QuestionOutlined),
    //     content: '确认修改设备信息吗？',
    //     okText: '保存',
    //     cancelText: '取消',
    //     onOk () {
    //       return new Promise((resolve, reject) => {
    //         resolve()
    //         set(record)
    //       }).catch(() => console.log('Oops errors!'))
    //     }
    //   })
    // }

    const setSuccess = (data) => {
    }

    const querySetSuccess = (data) => {
      if (data.data.configData.btswitch === '1') {
        checked.value = true
      } else {
        checked.value = false
      }
    }

    // 定时器 每3S获取一次设备列表
    // const getList = setInterval(function () {
    //   getBuletoothModel()
    // }, 3000)

    // const setSwitch = () => {
    //   if (checked.value === false) {
    //     clearInterval(getList)
    //   } else {
    //     clearInterval(getList)
    //     getList()
    //   }
    // }

    const switchChange = () => {
      if (checked.value === false) {
        model.configData.state = 0
        set()
        console.log(tableData.value)
        tableData.value.length = 0

        // setSwitch()
      } else {
        model.configData.state = 1
        set()
        // setSwitch()
        getBuletoothModel()
      }
    }

    const set = () => {
    //  model.configData.Enabled === true ? 1 : 0
      bluetooth.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null

      )
    }

    const querySet = () => {
    //  model.configData.Enabled === true ? 1 : 0
      bluetooth.set(
        baseRequestData,
        querySetSuccess,
        callbackError,
        null

      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前防火墙参数 *******************
    const getBuletoothModel = () => {
      bluetooth.query(baseRequestData, getBuletoothModelSuccess, callbackError, null)
    }

    const getBuletoothModelSuccess = (data) => {
      if (data.length === 0) {
        // tableData.value.length = 0
      } else {
      // 将获取到的蓝牙设备数据转为要显示的列表数据
        for (let i = 0; i < data.length; i++) {
          let status
          if (data[i].status === '1') {
            status = '已连接'
          } else {
            status = '未连接'
          }

          tableData.value[i] = {
            key: i,
            devname: data[i].devname,
            macaddress: data[i].macaddress,
            status: status
          }
        }
      }
    }
    // ****************** 设备蓝牙参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      //   // 进入页面的时候查询开关状态
      model.configData.state = 5
      querySet()
    //   // 检测设备返回的蓝牙状态
    //   if (model.configData.btswitch === '1') {
    //     checked.value = true
    //   } else {
    //     checked.value = false
    //   }
    // //   getBuletoothModel()
    })

    return {
      loading: ref(false),
      iconLoading,
      enterIconLoading,
      tableData,
      columns,
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      VoiceOption,
      AuthUi,
      checked,
      disconnect,
      forget,
      switchChange,
      //   searchDevice,
      connect,
      confirm,
      Pattern,
      onFinish,
      // 单选按钮参数
      radioStyle
    }
  }
})
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}
</style>
