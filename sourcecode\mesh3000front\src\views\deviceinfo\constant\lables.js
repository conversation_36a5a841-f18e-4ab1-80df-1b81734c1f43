export const LabelCaption = {
  id: {
    name: 'id',
    label: '电台编号'
  },
  sn: {
    name: 'sn',
    label: '序列号'
  },
  operator: {
    name: 'operator',
    label: '操作用户'
  },
  wfmode: {
    name: 'wfmode',
    label: '接入模式'
  },
  device_no: {
    name: 'device_no',
    label: 'Mesh编号'
  },
  device_name: {
    name: 'device_name',
    label: '设备名称'
  },
  device_type: {
    name: 'device_type',
    label: '设备类型'
  },
  net_id: {
    name: 'net_id',
    label: '网络ID'
  },
  paversion: {
    name: 'paversion',
    label: 'PA版本号'
  },
  apversion: {
    name: 'apversion',
    label: 'AP版本号'
  },
  cpversion_pl: {
    name: 'cpversion_pl',
    label: 'CP_pl版本号'
  },
  cpversion_hls: {
    name: 'cpversion_hls',
    label: 'CP_hls版本号'
  },
  recover: {
    name: 'recover',
    label: '恢复出厂设置'
  },
  mac_addr: {
    name: 'mac_addr',
    label: 'MAC'
  },
  ip_addr: {
    name: 'ip_addr',
    label: 'IP'
  },
  mask: {
    name: 'mask',
    label: 'Mesh设备子网掩码'
  },
  gate_way: {
    name: 'gate_way',
    label: '网关地址'
  },
  dns: {
    name: 'dns',
    label: 'DNS'
  },
  remaining_battery: {
    name: 'remaining_battery',
    label: '电池剩余电量'
  },
  voltage: {
    name: 'voltage',
    label: '电池当前电压'
  },
  interval: {
    name: 'interval',
    label: '设备查询间隔'
  },
  threshold: {
    name: 'threshold',
    label: '主机高温阈值'
  },
  boardtemperature: {
    name: 'boardtemperature',
    label: '主机当前温度'
  },
  voltagethreshold: {
    name: 'voltagethreshold',
    label: '电池低压阈值'
  },
  batterytemperature: {
    name: 'batterytemperature',
    label: '电池当前温度'
  },
  pcenter_freq: {
    name: 'pcenter_freq',
    label: '主路中心频率'
  },
  pband_width: {
    name: 'pband_width',
    label: '主路带宽'
  },
  ptx_power: {
    name: 'ptx_power',
    label: '主路发射功率'
  },
  scenter_freq: {
    name: 'scenter_freq',
    label: '辅路中心频率'
  },
  sband_width: {
    name: 'sband_width',
    label: '辅路带宽'
  },
  stx_power: {
    name: 'stx_power',
    label: '辅路发射功率'
  },
  work_mode: {
    name: 'work_mode',
    label: '工作模式'
  },
  trunk: {
    name: 'trunk',
    label: 'Trunk'
  },
  register_period: {
    name: 'register_period',
    label: 'RegisterPeriod'
  },
  routing_health_index: {
    name: 'routing_health_index',
    label: '总输出速率/输入速率等级'
  },
  in_network: {
    name: 'in_network',
    label: '在网标志'
  },
  distance: {
    name: 'distance',
    label: '传输距离等级'
  },
  hdmi_in: {
    name: 'hdmi_in',
    label: 'HDMI接入标志'
  },
  longitude: {
    name: 'longitude',
    label: '经度'
  },
  latitude: {
    name: 'latitude',
    label: '纬度'
  },
  create_time: {
    name: 'create_time',
    label: '创建时间'
  },
  modify_time: {
    name: 'modify_time',
    label: '修改时间'
  },
  del_marker: {
    name: 'del_marker',
    label: 'DelMarker'
  },
  dhcp_sw: {
    name: 'dhcp_sw',
    label: 'DHCP开关'
  },
  dhcp_firstAddr: {
    name: 'dhcp_firstAddr',
    label: '起始地址'
  },
  dhcp_lastAddr: {
    name: 'dhcp_lastAddr',
    label: '结束地址'
  },
  dhcp_subMask: {
    name: 'dhcp_subMask',
    label: '子网掩码'
  },
  dhcp_gateAddr: {
    name: 'dhcp_gateAddr',
    label: '网关地址'
  },
  dhcp_dnsAddr: {
    name: 'dhcp_dnsAddr',
    label: 'DNS地址'
  },
  wifi_set: {
    name: 'wifi_set',
    label: '模式'
  },
  wifi_ssid: {
    name: 'wifi_ssid',
    label: 'SSID'
  },
  wifi_keys: {
    name: 'wifi_keys',
    label: 'KEYS'
  },
  wifi_channel: {
    name: 'wifi_channel',
    label: 'CHANNEL'
  },
  // 防火墙
  fire_macAddr: {
    name: 'fire_macAddr',
    label: 'MacAddr'
  },
  fire_NetFilterType: {
    name: 'fire_NetFilterType',
    label: '设置防火墙'
  },
  fire_ipAddr: {
    name: 'fire_ipAddr',
    label: 'ip地址与端口'
  },
  fire_port: {
    name: 'fire_port',
    label: '端口'
  },
  fire_IP: {
    name: 'fire_IP',
    label: 'IP地址 '
  },
  fire_inputInfo: {
    name: 'inputInfo',
    label: '格式(地址:端口）'
  },
  // 语音
  voice_VoiceMode: {
    name: 'voiceMode',
    label: '语音服务模式'
  },
  voice_Speakergain: {
    name: 'speakergain',
    label: '扬声器音量'
  },
  voice_Micgain: {
    name: 'micgain',
    label: '麦克风音量'
  },
  voice_Backlevel: {
    name: 'backLevel',
    label: '背景音量'
  },
  voice_Voicegroup: {
    name: 'voicegroup',
    label: '当前语音组'
  },
  voice_Voicegroup1: {
    name: 'voicegroup1',
    label: '语音组1'
  },
  voice_Voicegroup2: {
    name: 'voicegroup2',
    label: '语音组2'
  },
  voice_Voicegroup3: {
    name: 'voicegroup3',
    label: '语音组3'
  },

  // mesh配置

  mesh_FreqHopping: {
    name: 'freqHopping',
    label: '跳频开关'
  },
  mesh_CenterFreq: {
    name: 'centerFreq',
    label: '主路中心频率'
  },
  mesh_Bandwidth: {
    name: 'bandwidth',
    label: '主路带宽'
  },
  mesh_Outputrate: {
    name: 'outputrate',
    label: '主路输出功率'
  },
  mesh_CA: {
    name: 'cA',
    label: '工作模式'
  },
  mesh_CAFreq: {
    name: 'cAFreq',
    label: '辅路中心频率'
  },
  mesh_CABandwidth: {
    name: 'cABandwidth',
    label: '辅路带宽'
  },
  mesh_CAOutputrate: {
    name: 'cAOutputrate',
    label: '辅路输出功率'
  },
  mesh_Distance: {
    name: 'distance',
    label: '距离'
  },
  mesh_Key: {
    name: 'key',
    label: '空口密钥'
  },
  mesh_WorkMode: {
    name: 'workMode',
    label: '监听模式'
  },
  mesh_encryptMode: {
    name: 'encryptMode',
    label: '加密模式'
  },
  mesh_RegisterPeriod: {
    name: 'registerPeriod',
    label: '注册间隔'
  },
  mesh_WhiteListEnabled: {
    name: 'whiteListEnabled',
    label: '白名单开关'
  },
  mesh_FreqPoint: {
    name: 'freqPoint',
    label: '频点规划'
  },
  mesh_BeginFreq: {
    name: 'beginFreq',
    label: '开始频率'
  },
  mesh_EndFreq: {
    name: 'endFreq',
    label: '结束频率'
  },
  mesh_SpaceFreq: {
    name: 'spaceFreq',
    label: '频率间隔'
  },
  // 串口
  uart_Switch: {
    name: 'switch',
    label: '开关'
  },
  uart_DataMode: {
    name: 'dateMode',
    label: '数据模式'
  },
  uart_BaudRate: {
    name: 'baudRate',
    label: '波特率'
  },
  uart_Party: {
    name: 'party',
    label: '奇偶校验'
  },
  uart_Stop: {
    name: 'stop',
    label: '停止位'
  },
  uart_LocalIP: {
    name: 'localIP',
    label: '接收IP地址'
  },
  uart_LocalPort: {
    name: 'localPort',
    label: '接收端口'
  },
  uart_RemotePort: {
    name: 'remotePort',
    label: '目的端口'
  },
  uart_RemoteIP: {
    name: 'remoteIP',
    label: '目的IP地址'
  },

  // 日期
  start_time: {
    name: 'start_time',
    lable: '起始日期:'
  },
  end_time: {
    name: 'end_time',
    lable: '结束日期:'
  },
  rang_time: {
    name: 'rang_time',
    lable: '时间段:'
  },

  // 用户设置
  user_name: {
    name: 'user_name',
    lable: '用户名称:'
  },
  password: {
    name: 'password',
    lable: '登录密码:'
  },
  passwordRepeat: {
    name: 'passwordRepeat',
    lable: '确认密码:'
  },
  rolename: {
    name: 'rolename',
    lable: '用户类型:'
  },
  // 添加摄像头
  camera_user: {
    name: 'user_name',
    lable: '用户名:'
  },
  camera_password: {
    name: 'password',
    lable: '密码:'
  },
  camera_name: {
    name: 'camera_name',
    lable: '摄像头名称:'
  },
  camera_channel: {
    name: 'channel',
    lable: '通道:'
  },
  camera_description: {
    name: 'description',
    lable: '设备描述:'
  },
  camera_firm: {
    name: 'firm',
    lable: '厂商:'
  },
  camera_ip: {
    name: 'ip',
    lable: '摄像头ip地址:'
  },
  rtsp_port: {
    name: 'port',
    lable: 'rtsp端口:'
  },
  camera_stream: {
    name: 'stream',
    lable: '码流选择:'
  },
  // 系统信息
  sysinfo_name: {
    name: 'sysname',
    lable: '系统名称:'
  },
  sysinfo_comp: {
    name: 'comp',
    lable: '系统名称:'
  },
  sysinfo_version: {
    name: 'version',
    lable: '系统版本:'
  }
}
