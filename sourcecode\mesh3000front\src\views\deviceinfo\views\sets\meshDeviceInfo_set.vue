<template>
          <a-row>
            <a-col :span="24">
              <a-card title="Mesh设备标识">
                <a-form
                :model="model"
                name="nest-messages"
                layout="horizontal"
                @finish="confirm"
                :validate-messages="validate">
                <!-- 第一行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <!-- ip地址 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.fire_IP.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                           <a-form-item :name="['configData', 'ipAddr']" :rules="[{ required: true, message: '请输入IP地址' },Pattern('IP')]">
                                 <a-input  v-model:value="model.configData.ipAddr"    style="width:200px;margin-left:10px"/>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <!-- 子网掩码 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.dhcp_subMask.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                           <a-form-item :name="['configData', 'subAddr']" :rules="[{ required: true, message: '请输入子网掩码' },Pattern('IP')]">

                                <a-input v-model:value="model.configData.subAddr"      style="width:200px;margin-left:10px"/>

                             </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                     <!-- 网关地址 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.gate_way.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                            <a-form-item :name="['configData', 'gateway']" :rules="[Pattern('IP')]">

                                <a-input  v-model:value="model.configData.gateway"    style="width:200px;margin-left:10px"/>

                            </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>

                 <!-- 第二行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <!-- DNS -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.dns.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                           <a-form-item :name="['configData', 'dns']" :rules="[Pattern('IP')]">
                               <a-input  v-model:value="model.configData.dns"    style="width:200px;margin-left:10px"/>
                           </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <!-- Mesh编号 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.device_no.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                           <a-form-item :name="['configData', 'deviceno']" :rules="[{ required: true, message: '请输入Mesh编号' },Pattern('MeshNo')]">
                                <a-input  v-model:value="model.configData.deviceno"   style="width:200px;margin-left:10px"/>
                           </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                     <!-- 设备名称 -->
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.device_name.label+":"}}</a-col>
                        <a-col :span="18" align="left" >
                        <a-form-item :name="['configData', 'name']" :rules="[{ required: true, message: '请输入设备名称' }]" >
                            <a-input enabled  v-model:value="model.configData.name"    style="width:200px;margin-left:10px"/>
                        </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>

                <!-- 第三行 -->
                <a-row justify="space-around"  style="margin-top:2%" :span="24" >
                    <a-col :span="6" >
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" >{{LabelCaption.net_id.label+":"}} </a-col>
                        <a-col :span="18" align="left" >
                    <a-form-item
                      :name="['configData', 'netid']"
                    >
                      <a-select
                        ref="select"
                        v-model:value="model.configData.netid"
                          style="width:200px;margin-left:10px"
                      >
                        <a-select-option
                          v-for="option in MeshNetID"
                          v-bind:key="option.value"
                          :value="value"
                          >{{ option.name }}</a-select-option
                        >
                      </a-select>
                    </a-form-item>
                        </a-col>
                    </a-row>
                    </a-col>
                    <a-col :span="6" >
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" ></a-col>
                        <a-col :span="18" align="left" >
                        </a-col>
                    </a-row>
                    </a-col>

                    <a-col :span="6" >
                    <a-row :span="24" vertical-align="top">
                        <a-col :span="6" align="right" ></a-col>
                        <a-col :span="18" align="left" >
                        </a-col>
                    </a-row>
                    </a-col>
                </a-row>

                    <a-divider />

                  <a-row justify="center"  >
                         <a-col :span="2">
                      <a-form-item >
                      <a-button type="primary" html-type="submit" v-if="AuthUi.Mesh设备信息.statusW">保存</a-button>
                      </a-form-item>
                    </a-col>
                    <a-col :span="2">
                      <a-form-item >
                      <a-button type="primary" @click="getMeshDeviceInfo">刷新</a-button>
                      </a-form-item>
                    </a-col>
                    </a-row>
                  </a-form>

                 <!-- <template #actions> -->
                 <!--  <a-row type="flex" justify="end" align="top"> -->
                 <!--    <a-col :span="4"> -->
                 <!--    <a-space> -->
                 <!--      <a-button type="primary" @click="confirm">保存</a-button> -->
                 <!--    </a-space> -->
                 <!--    <a-space> -->
                 <!--      <a-button type="primary" @click="getDhcp">查询</a-button> -->
                 <!--    </a-space> -->
                 <!--    </a-col> -->
                 <!--  </a-row> -->
                <!-- /template> -->

              </a-card>
            </a-col>
          </a-row>
        </template>
<script>
import { onMounted, defineComponent, createVNode, reactive } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { MeshDeviceInfoRequestAction } from '@/views/deviceinfo/action/meshDeviceInfoActive'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { MeshDeviceInfo } from '@/views/deviceinfo/model/MeshDeviceInfo'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { MeshNetID } from '@/views/deviceinfo/constant/options'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
export default defineComponent({
  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const meshdeviceinfo = new MeshDeviceInfo()
    model.configData = meshdeviceinfo

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const meshDeviceinfoRequestAction = new MeshDeviceInfoRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    // ****************** 根据主键查询数据 *******************
    /*  const getDeviceinfoByIdSuccess = (data) => { */
    /*    if (data.error_code === ErrorInfo.Success) { */
    /*      model = data.data */
    /*      getDhcp() */
    /*    } */
    /*  } */

    /*  const getDeviceinfoByIdFinally = () => { */
    /*    console.info('OK') */
    /*  } */

    /*  const getDeviceinfoById = () => { */
    /*    requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally) */
    /*  } */

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('Mesh信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置Mesh信息失败!${data.error_code}`)
      }
    }

    const set = () => {
    //  model.configData.Enabled === true ? 1 : 0
      meshDeviceinfoRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前meshdeviceinfof参数 *******************
    const getMeshDeviceInfo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      meshDeviceinfoRequestAction.query(baseRequestData, getMeshDeviceInfoSuccess, callbackError, null)
    }

    const getMeshDeviceInfoSuccess = (data) => {
      model.configData = data.data.configData
      // this.model = data.data
      console.info(meshdeviceinfo.value)
    }
    // ****************** 设备当前DHCP参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getMeshDeviceInfo()
      // getDeviceinfoById()
    })

    const onFinish = (values) => {
      console.log('Success:', values)
    }

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      MeshNetID,
      AuthUi,
      confirm,
      Pattern,
      onFinish,
      getMeshDeviceInfo
    }
  }
})
</script>
