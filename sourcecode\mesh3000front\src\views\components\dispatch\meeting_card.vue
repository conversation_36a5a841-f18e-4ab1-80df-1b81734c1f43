<template>
    <div style="padding-left: 30px;padding-right: 30px;">
                <div style="background-img: url('/src/assets/img/phone.png')" @click="clickCard()" @mouseenter="mouseenterCard()" @mouseleave="mouseleaveCard()">
                    <a-card style="text-align: center;padding: 0%;"  :bordered="true" :title="card.name"  id="meetCardId">
                        <a-row style="justify-content: center;">
                            <a-col >
                                <img class="shake-animation" src="../../../../src/assets/img/ring.png" alt="Smiley face" width="92" height="30" style="justify-content: center;">
                            </a-col>
                        </a-row>
                        <a-row>
                            <div v-if="card.voiceMeeting" style="background-color: #00ffdaad ;width: 100%;text-align: center">
                                语音会议中...
                            </div>
                            <div v-if="card.videoMeeting" style="background-color: #00ffdaad ;width: 100%;text-align: center">
                                视频会议中...
                            </div>
                        </a-row>
                    </a-card>
                </div>
    </div>
  </template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { defineComponent, ref, onMounted, watch } from 'vue'

export default defineComponent({
  props: ['currMeet', 'meetCardList', 'meetStatusChange'],
  setup (props) {
    // 测试占位数据
    const card = ref([])

    const meetCardList = ref([])
    const meetMeet = ref([])
    // 已经选择的用户数据
    const selectMeetCard = ref([])

    /**
     * @name 点击卡片的事件
     * @param i 点击卡片的ID，通过这个I就可以知道点击了那个卡片
     */
    function clickCard () {
      document.getElementById('meetCardId').style.backgroundColor = '#00ffdd5d'
    }

    /**
     * @name 鼠标移动到卡片上的事件
     * @param i 鼠标放到卡片上的ID,通过这个ID就可以知道鼠标放到了那个卡片上
     */
    function mouseenterCard () {
      document.getElementById('meetCardId').style.borderColor = 'aqua'
      document.getElementById('meetCardId').style.borderWidth = '3px'
    }

    /**
     * @name 鼠标移出卡片的事件
     * @param i 鼠标移出卡片的ID，通过这个ID就可以知道鼠标从那个卡片上移出了。
     */
    function mouseleaveCard () {
      document.getElementById('meetCardId').style.borderColor = ''
      document.getElementById('meetCardId').style.borderWidth = ''
    }

    /**
     * @description 获取用户所有卡片数据，并将所有用户数据通过卡片形式进行展示
     * @param data 用户数据
     */
    // const getUsserList = (data) => {
    //   list.value = data
    // }

    onMounted(() => {
      // 监听 props.meetCardList 的变化，当 props.meetCardList 发生变化时，更新所有卡片状态。
      watch(() => props.meetCardList, (newValue, oldValue) => {
        meetCardList.value.length = 0
        meetCardList.value = newValue
        console.info('meetCardList:' + newValue)
      })

      // 监听 props.userStatusChange 的变化，当 props.userStatusChange 发生变化时，更新对应卡片状态。
      watch(() => props.meetStatusChange, (newValue, oldValue) => {
        console.log('meetStatusChange:' + newValue)
      })
    })
    return {
      card,
      selectMeetCard,
      clickCard,
      mouseleaveCard,
      mouseenterCard
    }
  }

})
</script>
<style scoped>
.shake-animation {
    display: inline-block;
    animation: shake 0.82s;
    animation-iteration-count: infinite;
  }

  @keyframes shake {
    0% { transform: translateY(0); }
    20% { transform: translateY(-5px); }
    40% { transform: translateY(5px); }
    60% { transform: translateY(-5px); }
    80% { transform: translateY(5px); }
    100% { transform: translateY(0); }
  }
</style>
