<template>
  <a-row>
    <a-col :span="24">
      <div
        class="components-page-header-demo-responsive"
        style="border: 1px solid rgb(235, 237, 240)"
      >
        <a-page-header title="设备参数设置" sub-title="">
          <template #extra>
            <a-button type="primary" @click="pageDirectAction.goToIndex()"
              >返回</a-button
            >
          </template>
          <template  #tags>
            <div v-if="deviceInfo.inNetWork === 0">
                <a-tag color="#FF6347">在线不在网</a-tag>
            </div>
            <div v-else-if="deviceInfo.inNetWork === 1">
                <a-tag color="#00C957">在线在网</a-tag>
            </div>
            <div v-else>
                <a-tag color="#DCDCDC">离线</a-tag>
            </div>
          </template>
          <template #footer>
            <a-menu inlineCollapsed="false" mode="horizontal">
                <!-- 外侧循环遍历按钮数列 -->
              <div
                v-for="(item, index) in deviceinfoSetMenu"
                :key="item.title"
              >
              <!-- 内层判断是否显示菜单选项 -->
                <a-menu-item  @click="pageDirectAction.goTo(item.path, model.id)" :key="index">
                        {{ item.title }}
                </a-menu-item>
            </div>
            </a-menu>
          </template>
           <a-spin :spinning="deviceInfo.device_name===''" tip="加载中..." size="large">
          <div class="content">
            <div class="main">
              <a-descriptions size="small" :column="4">
                <a-descriptions-item :label="LabelCaption.sn.label">{{
                   deviceInfo.sn
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.device_name.label">{{
                  deviceInfo.device_name
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.device_type.label">
                {{deviceInfoTran.deviceType}}
               </a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.wfmode.label">{{
                  deviceInfoTran.workMode
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.ip_addr.label">{{
                  deviceInfo.ip_addr
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.mask.label">{{
                  deviceInfo.mask
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.gate_way.label">{{
                  deviceInfo.gate_way
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.dns.label">{{
                 deviceInfo.dns
                }}</a-descriptions-item>
                <a-descriptions-item :label="LabelCaption.mac_addr.label">{{
                  deviceInfo.mac_addr
                }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
            </a-spin>
        </a-page-header>
      </div>
    </a-col>
  </a-row>

  <a-row>
    <a-col :span="24">
      <router-view></router-view>
    </a-col>
  </a-row>
</template>

<script>
import { onMounted, defineComponent, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { AuthUi } from '@/views/deviceinfo/constant/authui'
import { MeshDeviceInfoDetailAction } from '@/views/deviceinfo/action/MeshDeviceInfoDetailAction'
// import { MeshDeviceInfoRequestAction } from '@/views/deviceinfo/action/meshDeviceInfoActive'
// import { MeshDeviceInfo } from '@/views/deviceinfo/model/MeshDeviceInfo'
import { useRouter } from 'vue-router'
import {
  DeviceTypeOptions,
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({
  setup () {
    // 对应后台数据表
    let model = reactive(new Deviceinfo())
    // const model_Net = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    model.configData = 'json'
    // const model = reactive(new Deviceinfo())
    // const meshdeviceinfo = new MeshDeviceInfo()
    // model_Net.configData = meshdeviceinfo

    // 转换数据（设备信息）
    const deviceInfoTran = reactive({
      deviceType: '',
      workMode: ''
    })

    // 保存查询的数据库数据

    const dataSource = ref({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const meshDeviceInfoDetailAction = new MeshDeviceInfoDetailAction()
    // const meshDeviceinfoRequestAction = new MeshDeviceInfoRequestAction()
    const pageDirectAction = new PageDirectAction()
    const deviceInfo = ref({
      sn: '',
      inNetWork: '',
      device_name: '',
      device_type: '',
      wfmode: '',
      ip_addr: '',
      mask: '',
      gate_way: '',
      dns: '',
      mac_addr: '',
      create_time: '',
      modify_time: ''
    })
    const setOption = ref([])
    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 5)
    }

    // 转换数据，将设备的参数转换为设备状态的文字描述，循环遍历option接口
    const tranDeviceInfo = () => {
      // option 循环变量
      let i
      // 设备类型
      for (i = 0; i < DeviceTypeOptions.length; i++) {
        if (model.configData.device_type === DeviceTypeOptions[i].value) {
          deviceInfoTran.deviceType = DeviceTypeOptions[i].name
          break
        }
      }
      // 接入模式
      for (i = 0; i < WfModeOptions.length; i++) {
        if (deviceInfo.value.wfmode === WfModeOptions[i].value) {
          deviceInfoTran.workMode = WfModeOptions[i].name
          break
        }
      }
    }

    // 查询数据，拿到数据库信息
    const getMeshDeviceInfoDetail = () => {
      meshDeviceInfoDetailAction.query(baseRequestData, getMeshDeviceInfoDetailSuccess, callbackError, null)
    }
    // 成功回调函数
    const getMeshDeviceInfoDetailSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data.configData
        model.configData = model[data.data.sn]
        console.info(model.configData)
        console.info(data)
      }
      // this.model = data.data
      //   console.info(meshdeviceinfo.value)
      // 设置设备状态
      setDeviceInfo()
      // 设备状态参数与状态描述进行转换
      tranDeviceInfo()
    }

    // 设置设备信息
    const setDeviceInfo = () => {
    // 在网状态
      if (model.configData.in_network === 1) {
        deviceInfo.value.inNetWork = 1
        console.info('在网在线')
      } else if (model.configData.in_network === 0) {
        console.info('在线不在网！')
        deviceInfo.value.inNetWork = 0
      } else {
        console.info('离线')
        deviceInfo.value.inNetWork = 2
      }
      // sn/device_name/......
      deviceInfo.value.sn = model.configData.sn
      deviceInfo.value.device_name = model.configData.device_name
      deviceInfo.value.device_type = model.configData.device_type
      deviceInfo.value.wfmode = model.configData.wfmode
      deviceInfo.value.ip_addr = model.configData.ip_addr
      deviceInfo.value.mask = model.configData.mask
      deviceInfo.value.gate_way = model.configData.gate_way
      deviceInfo.value.dns = model.configData.dns
      deviceInfo.value.mac_addr = model.configData.mac_addr
    }
    // ****************** 根据主键查询数据 *******************

    const deviceinfoSetMenu = ref([])

    const goTo = (path) => {
      console.info(path)
    }

    onMounted(() => {
      const router = useRouter()
      router.options.routes.forEach((item) => {
        if (item.path === '/') {
          const children_route = item.children
          children_route.forEach((v) => {
            if (v.path === '/deviceinfo_set') {
              const deviceinfo_set_children = v.children
              deviceinfo_set_children.forEach((dsc) => {
                console.info(dsc)
                // 遍历权限数组，拿到对应权限并写入数组
                // 创建变量，保存对应的选项权限
                let authOpt
                for (const auth in AuthUi) {
                  // 找到对应权限
                  if (AuthUi[auth].name === dsc.title) {
                    // 将对应权限进行保存
                    authOpt = AuthUi[auth].vis
                    if (AuthUi[auth].vis === true) {
                      deviceinfoSetMenu.value.push({
                        path: dsc.path,
                        title: dsc.title,
                        auth: authOpt
                      })
                    }
                  }
                }
              })
            }
          })
        }
        if (item.path === '/video_hk') {
          deviceinfoSetMenu.value.push({
            path: item.path,
            title: item.title
          })
        }
      })
      console.log('================options================')
      console.log(deviceinfoSetMenu)
      model.id = pageDirectAction.getCurrentRouteValue()
      getMeshDeviceInfoDetail()
    })

    const ifshow = (id) => {
      // const uiControl = {}
      // return uiControl.id
      console.info(`id=== ${id}`)
      return false
    }

    return {
      model,
      baseRequestData,
      DeviceinfoColumns,
      pageDirectAction,
      deviceinfoSetMenu,
      LabelCaption,
      dataSource,
      deviceInfo,
      deviceInfoTran,
      AuthUi,
      setOption,
      getMeshDeviceInfoDetail,
      goTo,
      ifshow
    }
  }
})
</script>

<style scoped>
.components-page-header-demo-responsive {
  padding-bottom: 20px;
}
.components-page-header-demo-responsive tr:last-child td {
  padding-bottom: 0;
}
#components-page-header-demo-responsive .content {
  display: flex;
}
#components-page-header-demo-responsive .ant-statistic-content {
  font-size: 20px;
  line-height: 28px;
}
@media (max-width: 576px) {
  #components-page-header-demo-responsive .content {
    display: block;
  }

  #components-page-header-demo-responsive .main {
    width: 100%;
    margin-bottom: 12px;
  }

  #components-page-header-demo-responsive .extra {
    width: 100%;
    margin-left: 0;
    text-align: left;
  }
}
</style>
