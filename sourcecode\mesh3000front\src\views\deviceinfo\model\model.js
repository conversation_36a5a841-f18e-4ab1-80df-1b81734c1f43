class Deviceinfo {
  constructor () {
    this.id = null
    this.sn = null
    this.wfmode = null
    this.device_no = null
    this.device_name = null
    this.device_type = null
    this.net_id = null
    this.paversion = null
    this.apversion = null
    this.cpversion_hls = null
    this.mac_addr = null
    this.ip_addr = null
    this.mask = null
    this.gate_way = null
    this.dns = null
    this.remaining_battery = null
    this.voltage = null
    this.interval = null
    this.threshold = null
    this.pcenter_freq = null
    this.pband_width = null
    this.ptx_power = null
    this.scenter_freq = null
    this.sband_width = null
    this.stx_power = null
    this.work_mode = null
    this.trunk = null
    this.register_period = null
    this.routing_health_index = null
    this.in_network = null
    this.distance = null
    this.hdmi_in = null
    this.longitude = null
    this.latitude = null
    this.create_time = null
    this.modify_time = null
    this.delmarker = null
    this.NetFilterType = null
    this.IpAddr = null
  }

  init () {
    this.id = null
    this.sn = null
    this.meshsn = null
    this.wfmode = null
    this.device_no = null
    this.device_name = null
    this.device_type = null
    this.net_id = null
    this.paversion = null
    this.apversion = null
    this.cpversion_hls = null
    this.mac_addr = null
    this.ip_addr = null
    this.mask = null
    this.gate_way = null
    this.dns = null
    this.remaining_battery = null
    this.voltage = null
    this.interval = null
    this.threshold = null
    this.pcenter_freq = null
    this.pband_width = null
    this.ptx_power = null
    this.scenter_freq = null
    this.sband_width = null
    this.stx_power = null
    this.work_mode = null
    this.trunk = null
    this.register_period = null
    this.routing_health_index = null
    this.in_network = null
    this.distance = null
    this.hdmi_in = null
    this.longitude = null
    this.latitude = null
    this.create_time = null
    this.modify_time = null
    this.delmarker = null
    this.NetFilterType = null
    this.IpAddr = null
  }
}

export { Deviceinfo }
