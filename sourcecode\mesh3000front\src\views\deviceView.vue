<template>
    <dv-full-screen-container class="background">
        <div class="d-flex jc-center title_wrap">
    <div class="zuojuxing"></div>
    <div class="youjuxing"></div>
    <div class="guang"></div>
    <div class="d-flex jc-center">
      <div class="title">
        <span class="title-text">设备管理可视化平台</span>
      </div>
      <a-button @click="exit" type="link" style="position: absolute; top:40%;right:1%; color:rgb(122, 242, 170)">
                        <template #icon><LogoutOutlined style="color:rgb(122, 242, 170)"/></template>
                        退出
      </a-button>
    </div>
  </div>
        <!-- <div style="height: 60px; width: 100%; background-color: bisque;"></div> -->
        <div class="index-box">
            <!-- 第一列 -->
            <div class="contetn_left">
            <div class="item_title" style="height: 30%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;告警总览 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart">
                        <!-- <button @click="click">点击我触发事件</button> -->
                        <LeftTop @click="warningClick" :warningTotal ="warningTotal"></LeftTop>
                    </div>
                </dv-border-box-13>
            </div>
            <div class="item_title" style="height: 30%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;设备总览 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  @click="deviceClick" class="chart" >
                        <leftMid  :deviceTotal ="deviceTotal"></leftMid>
                    </div>
                </dv-border-box-13>
            </div>
            <div class="item_title" style="height: 40%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;实时告警 &nbsp;&nbsp; </span>
                    <a-button @click="warningInfoClick" size="small" type="link" style="position: absolute; top:23%;right:4%; color:rgb(122, 242, 170)">
                        <template #icon><EditOutlined style="color:rgb(122, 242, 170)"/></template>
                        处理</a-button>
                        <a-modal v-model:visible="visible" title="告警处理" style="width:80%">
                            <template #footer>
                            </template>
                            <a-table
                                :pagination="pagination"
                                class="ant-table-striped"
                                size="middle"
                                :columns="columns"
                                :data-source="filteredData"
                                bordered
                            >
                                <template #operation="{ record}">
                                  <div class="editable-row-operations">
                                    <span>
                                      <a-button  @click="deleteWarn((record.key))" type="primary" >清除告警</a-button>
                                    </span>
                                  </div>
                                </template>
                            </a-table>
                        </a-modal>
                    </div>
                    <div  class="chart" @click="warningInfoClick">
                        <leftBot :warningInfo ="warningInfo"></leftBot>
                    </div>
                </dv-border-box-13>
            </div>
        </div>
<!-- 第二列 -->
        <div class="contetn_center">
            <div class="item_title_top" style="height: 70%;">
                <dv-border-box-13>
                    <div class="item_title_top">
                        <a-button v-if="showTopo" @click="showTopoClick" size="small" type="link" style="position: absolute; top:23%;right:4%; color:rgb(122, 242, 170)">
                        <template #icon><DeploymentUnitOutlined style="color:rgb(122, 242, 170)"/></template>
                        切换拓扑
                        </a-button>
                        <a-button v-if="showMap" @click="showMapClick" size="small" type="link" style="position: absolute; top:23%;right:4%; color:rgb(122, 242, 170)">
                        <template #icon><EnvironmentOutlined style="color:rgb(122, 242, 170)"/></template>
                        切换地图</a-button>
                        <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;设备分布 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart_top" @dblclick="MapTopoClick">
                        <centerMid :showMap="showMap" :showTopo="showTopo"></centerMid>
                    </div>
                </dv-border-box-13>
            </div>
            <div class="item_title" style="height: 30%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;设备电量 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart" @click="powerClick">
                        <centerBot :powerInfo = "powerInfo"></centerBot>
                    </div>
                </dv-border-box-13>
            </div>
        </div>
        <!-- 第三列 -->

        <div class="contetn_left">
            <div class="item_title" style="height: 35%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;信号质量(RSSI) &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart" @click="rssiClick">
                        <RightTop :RSSI="RSSI"></RightTop>
                    </div>
                </dv-border-box-13>
            </div>
            <div class="item_title" style="height: 35%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;信噪比(SNR) &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart" @click="snrClick">
                        <RightMid :SNR="SNR"></RightMid>
                    </div>
                </dv-border-box-13>
            </div>
            <div class="item_title" style="height: 30%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;设备温度 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                    <div  class="chart" @click="temperatureClick">
                        <RightBot :temperature="temperature"></RightBot>
                    </div>
                </dv-border-box-13>
            </div>
        </div>
        </div>
    </dv-full-screen-container>

</template>
<script>
import { defineComponent, onMounted, ref } from 'vue'
import Router from '../router/index'
// import screenfull from 'screenfull'
import LeftTop from '@/views/components/left_top.vue'
import RightTop from '@/views/components/right_top.vue'
import RightMid from '@/views/components/right_mid.vue'
import RightBot from '@/views/components/right_bot.vue'
import centerBot from '@/views/components/center_bot.vue'
import leftBot from '@/views/components/left_bot.vue'
import leftMid from '@/views/components/left_mid.vue'
import centerMid from '@/views/components/center_mid.vue'

import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { DeviceViewAction } from '@/views/deviceinfo/action/deviceView'
import { EditOutlined, DeploymentUnitOutlined, EnvironmentOutlined, LogoutOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  // 当路由发生变化的时候清除定时器
  watch: {
    $route (to, from) {
      clearInterval(this.deviceInterval)
    }
  },
  components: {
    LeftTop,
    leftBot,
    leftMid,
    centerBot,
    RightBot,
    RightMid,
    RightTop,
    centerMid,
    EditOutlined,
    DeploymentUnitOutlined,
    LogoutOutlined,
    EnvironmentOutlined
  },
  setup () {
    const columns = [
      {
        title: '设备名称',
        dataIndex: 'deviceName',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'deviceName'
        }
      },
      {
        title: '告警级别',
        dataIndex: 'level',
        width: '10%',
        align: 'center',
        filters: [
          {
            text: '一般',
            value: '一般'
          },
          {
            text: '严重',
            value: '严重'
          }
          // ...更多筛选项
        ],
        onFilter: (value, record) => record.level === value,
        filterMultiple: true
      },
      {
        title: '描述',
        dataIndex: 'description',
        width: '60%',
        align: 'center',
        slots: {
          customRender: 'description'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]
    const dataWarnInfo = ref()
    dataWarnInfo.value = []

    const filteredData = ref(dataWarnInfo)

    const pagination = {
      pageSize: 10 // 每页最大五行
    }

    const visible = ref(false)

    // 告警总数
    const warningTotal = ref({
      type1: 2,
      type2: 3,
      type3: 4
    })

    // 设备总数
    const deviceTotal = ref({
      type0: '',
      type1: '',
      type2: '',
      type3: ''
    })

    // 告警信息
    const warningInfo = ref([
    ])

    // SNR
    const SNR = ref({
      x: [],
      y: []
    })

    // 温度
    const temperature = ref({
      x: [],
      y: []
    })

    // 电量
    const powerInfo = ref({
      x: [
      ],
      y: [
      ]
    })

    // RSSI
    const RSSI = ref({
      x: [],
      y: []
    })

    // 是否展示地图
    const showMap = ref(false)

    // 是否展示拓扑
    const showTopo = ref(true)

    const warningId = ref() // 记录告警最后一条的id,如果未发生变化则不进行更新

    // 对应后台数据表
    const model = ref(new Deviceinfo())
    // 保存查询的数据库数据
    const baseParam = ref(new BaseParam())
    const baseRequestData = ref(new BaseRequestData(baseParam, model))
    const deviceViewAction = new DeviceViewAction()

    const warningClick = () => {
    //   alert(234)
      warningTotal.value = {
        type1: 20,
        type2: 10,
        type3: 10
      }
    }

    const deviceClick = () => {
    //   alert(234)
      deviceTotal.value = {
        type0: 9,
        type1: 2,
        type2: 3,
        type3: 4
      }
    }

    const warningInfoClick = () => {
      visible.value = true
    }

    const powerClick = () => {
      powerInfo.value = {
        x: ['绿色手持', '黑色背负', '车载03', '车载02', '背负01', '背负05'],
        y: [
          {
            value: 2
          }, {
            value: 1
          }, {
            value: 9
          }, {
            value: 16
          }, {
            value: 17
          }, {
            value: 79
          }
        ]
      }
    }

    const rssiClick = () => {
      RSSI.value = {
        x: [
          -10,
          -11,
          -21,
          -12,
          -14,
          -15,
          -26,
          -25,
          -56,
          -10
        ],
        y: ['绿色手持1', '黑色背负', '车载03', '车载02', '背负01', '背负05']
      }
    }

    const temperatureClick = () => {
      temperature.value = {
        y: [21, 85, 72, 75, 35, 45, 41, 40],
        x: ['手持02', '手持03', '手持04', '手持05', '手持01', '手持022']
      }
    }

    const snrClick = () => {
      SNR.value = {
        x: ['背负01->手持02', '背负01->手持03', '背负01->手持01', '背负01->手持05'],
        y: [10, 20, 30, 20, 30, 90]
      }
    }

    const showMapClick = () => {
      showMap.value = false
      showTopo.value = true
    }

    const showTopoClick = () => {
      showMap.value = true
      showTopo.value = false
    }

    const MapTopoClick = () => {
      if (showTopo.value) {
        Router.push({ path: '/' })
      } else {
        Router.push({ path: '/topo_draw?id=334' })
      }
    }

    const exit = () => {
      Router.push({ path: '/' })
    }

    const deleteWarn = (index) => {
      console.log('deleteWarn index:', index)
      // 将对应的清除警告请求发送
      model.value.id = dataWarnInfo.value[index].id
      deviceViewAction.seen(baseRequestData.value, null, null, null)

      dataWarnInfo.value.splice(index, 1)

      // 删除后对数组中data的key进行重新排列 下次删除该位置的元素时候就不会发生错误
      for (let i = 0; i < dataWarnInfo.value.length; i++) {
        dataWarnInfo.value[i].key = i
      }
    }

    // 全屏
    const allScreen = () => {
      const domElement = document.documentElement
      if (domElement.requestFullscreen) {
        domElement.requestFullscreen()
      } else if (domElement.mozRequestFullScreen) {
        domElement.mozRequestFullScreen()
      } else if (domElement.webkitRequestFullScreen) {
        domElement.webkitRequestFullScreen()
      }
    }

    const deviceView = () => {
      deviceViewAction.query(baseRequestData.value, deviceViewSuccess, null, null)
    }

    const warTotalChange = ref(false)

    const deviceViewSuccess = (data) => {
      // 告警总览更新 当数据有变化的时候进行更新，没有变化则不进行更新
      if (!((warningTotal.value.type1 === data.data.warTotal) && (warningTotal.value.type2 === data.data.warLevel1) && (warningTotal.value.type3 === data.data.warLevel2 + data.data.warLevel3))) {
        warningTotal.value = {
          type1: data.data.warTotal,
          type2: data.data.warLevel1,
          type3: data.data.warLevel2 + data.data.warLevel3
        }
        warTotalChange.value = true
      } else {
        warTotalChange.value = false
      }
      // 设备总览更新
      deviceTotal.value = {
        type0: data.data.devTotal, // 设备总数
        type1: data.data.devInnet, // 在线设备
        type2: data.data.devOnline, // 在线不在网设备
        type3: data.data.devOffline // 离线设备
      }

      // 临时变量接收电池电量
      const temp = ({
        x: [], // x为设备名称
        y: [] // y为设备电量
      })

      // 临时变量用于接收设备温度
      const temp2 = ({
        x: [], // 设备名称
        y: [] // 设备对应的温度
      })
      // 遍历后端返回的数据，将电池电量拿出来
      for (let i = 0; i < data.data.meshDevBrief.length; i++) {
        temp.x.push(data.data.meshDevBrief[i].device_name)
        temp.y.push({
          value: data.data.meshDevBrief[i].remaining_battery
        })
      }
      powerInfo.value = {
        x: temp.x,
        y: temp.y
      }

      // 遍历后端返回的数据，将温度拿出来
      for (let i = 0; i < data.data.meshDevBrief.length; i++) {
        temp2.x.push(data.data.meshDevBrief[i].device_name)
        temp2.y.push({
          value: data.data.meshDevBrief[i].hosttemperature
        })
      }

      temperature.value = {
        x: temp2.x,
        y: temp2.y
      }

      // 实时告警的更新
      const warningTemp = [] // 实时告警的临时变量，用于接收实时告警信息
      if (data.data.meshDevWarnings.length === 0) {
        warningInfo.value = []
      }
      if ((warningId.value !== data.data.meshDevWarnings[0].id) || warTotalChange.value) {
        for (let i = 0; i < data.data.meshDevWarnings.length; i++) {
        // 判断告警级别，根据告警级别设置对应颜色
          if (data.data.meshDevWarnings[i].level === 'WARN') {
            warningTemp.push([
              data.data.meshDevWarnings[i].mesh_name,
              '<font color="yellow">一般</font>',
              data.data.meshDevWarnings[i].event_description
            ])
            dataWarnInfo.value[i] = {
              key: i,
              deviceName: data.data.meshDevWarnings[i].mesh_name,
              level: '一般',
              description: data.data.meshDevWarnings[i].event_description,
              id: data.data.meshDevWarnings[i].id
            }
          } else {
            warningTemp.push([
              data.data.meshDevWarnings[i].mesh_name,
              '<font color="red">严重</font>',
              data.data.meshDevWarnings[i].event_description
            ])
            dataWarnInfo.value[i] = {
              key: i,
              deviceName: data.data.meshDevWarnings[i].mesh_name,
              level: '严重',
              description: data.data.meshDevWarnings[i].event_description,
              id: data.data.meshDevWarnings[i].id
            }
          }
        }
        warningId.value = data.data.meshDevWarnings[0].id
        // 将temp获取到的告警信息给warningInfo
        warningInfo.value = warningTemp
      }
      const temp3 = {
        x: [],
        y: []
      }
      // rssi实时更新
      for (let i = 0; i < data.data.meshDevBrief.length; i++) {
        for (let j = 0; j < data.data.meshDevBrief[i].linkCharacteristics.length; j++) {
          if (data.data.meshDevBrief[i].linkCharacteristics[j].AdjacentNodeSn !== null) {
            temp3.y.push(data.data.meshDevBrief[i].device_name + '-->' + data.data.meshDevBrief[i].linkCharacteristics[j].adjacentNodeDevname)
            temp3.x.push(data.data.meshDevBrief[i].linkCharacteristics[j].ANT1RSSI)
          }
        }
      }

      RSSI.value = temp3

      const temp4 = {
        x: [],
        y: []
      }
      // rssi实时更新
      for (let i = 0; i < data.data.meshDevBrief.length; i++) {
        for (let j = 0; j < data.data.meshDevBrief[i].linkCharacteristics.length; j++) {
          if (data.data.meshDevBrief[i].linkCharacteristics[j].AdjacentNodeSn !== null) {
            temp4.x.push(data.data.meshDevBrief[i].device_name + '-->' + data.data.meshDevBrief[i].linkCharacteristics[j].adjacentNodeDevname)
            temp4.y.push(data.data.meshDevBrief[i].linkCharacteristics[j].ANT1SNR)
          }
        }
      }

      SNR.value = temp4
    }

    const deviceInterval = setInterval(function () {
      deviceView()
    }, 1000)

    onMounted(() => {
      allScreen()
    })
    return {
      warningTotal,
      deviceTotal,
      warningInfo,
      powerInfo,
      RSSI,
      SNR,
      temperature,
      showMap,
      showTopo,
      visible,
      dataWarnInfo,
      columns,
      pagination,
      deviceInterval,
      filteredData,
      deleteWarn,
      MapTopoClick,
      showMapClick,
      showTopoClick,
      temperatureClick,
      snrClick,
      rssiClick,
      warningClick,
      deviceClick,
      warningInfoClick,
      powerClick,
      exit
    }
  }
})
</script>
<style scoped>
.background{
    background-color: #011329;
}

body{
    margin: 0;
}
.title_wrap {
  height: 60px;
  background-image: url("../assets/img/top.png");
  background-size: cover;
  background-position: center center;
  position: relative;
  margin-bottom: 6px;
}
.zuojuxing {
    left: 11%;
  }

  .youjuxing {
    right: 11%;
    transform: rotate(180deg);
  }

  .guang {
    position: absolute;
    bottom: -26px;
    background-image: url("../assets/img/guang.png");
    background-position: 80px center;
    width: 100%;
    height: 56px;
  }
  .title {
  position: relative;
  text-align: center;
  background-size: cover;
  color: transparent;
  height: 60px;
  line-height: 46px;

}

.title-text {
    font-size: 38px;
    font-weight: 900;
    letter-spacing: 6px;
    width: 100%;
    background: linear-gradient(
      92deg,
      #0072ff 0%,
      #00eaff 48.8525390625%,
      #01aaff 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
.chart{
     width: 95%;
     height: 78%;
     position: relative;
     margin-left:2%;
     margin-top: -1%;
}

.chart_top{
     width: 95%;
     height: 87%;
     position: relative;
     margin-left:2%;
}
.index-box {
  width: 100%;
  display: flex;
  min-height: calc(100% - 64px);
  justify-content: space-between;
}

.contetn_left,
.contetn_right {
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
  position: relative;
  width: 30%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.contetn_center {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  width: 40%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.item_title {
  height:20%;
  line-height: calc(100% - 38px);
  width: 100%;
  color: #23aceb;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item_title_top {
    height: 10%;
  line-height: calc(100% - 38px);
  width: 100%;
  color: #23aceb;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
  .zuo,
  .you {
    width: 58px;
    height: 14px;
    background-image: url("@/assets/img/titles/zuo.png");
  }

  .you {
    transform: rotate(180deg);
  }
  .title-inner {
    font-weight: 900;
    letter-spacing: 6px;
    background: linear-gradient(
      92deg,
      #0072ff 0%,
      #00eaff 48.8525390625%,
      #468cb4 100%
    );
    -webkit-background-clip: text;
  }
  :deep(.dv-border-box-content)  {
    box-sizing: border-box;
    padding: 6px 16px 0px;
  }

  .item_title_content {
  height: 8px;
}

.item_title_content_def {
  width: 100%;
  height: 100%;
}

.contetn_lr-item {
  height: 300px;
}
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.editable-row-operations a {
  margin-right: 8px;
}

</style>
