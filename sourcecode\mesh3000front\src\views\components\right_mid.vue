<template>
    <dv-loading v-if="showLoading"></dv-loading>
    <div :v-if = "! showLoading" id="main2" style="width: 99%; height: 99%; background-color: rgba(255, 255, 255, 0); position: relative;"></div>
  </template>

<script>
import { defineComponent, onMounted, watch, ref } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['SNR'],
  setup (props) {
    // 渲染数据的时候计算一下 百分比
    const data = ref([])
    const className = ref([])
    const defaultData = [100, 100, 100, 100, 100, 100, 100, 100]
    const dataZoomMove = {
      start: 0,
      end: 4
    }
    const option = {
      dataZoom: [
        {
          show: true, // 为true 滚动条出现
          realtime: true,
          type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
          startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
          endValue: dataZoomMove.end,
          width: 6,
          right: '20',
          top: '20%', // 位置和grid配置注意下
          height: '56%',
          yAxisIndex: [0, 1], // 关联多个y轴
          moveHandleStyle: {
            color: 'rgba(89, 202, 241,.5)'
          },
          moveHandleSize: '6',
          emphasis: {
            moveHandleStyle: {
              color: 'rgba(89, 202, 241,.5)'
            }
          },
          textStyle: {
            color: 'rgba(255,255,255,0)'
          },
          backgroundColor: 'rgba(255,255,255,.1)',
          borderColor: 'rgba(255,255,255,0)',
          fillerColor: 'rgba(0,0,0,0)',
          handleSize: '6',
          handleStyle: {
            color: 'rgba(255,255,255,0)'
          },
          brushStyle: {
            color: 'rgba(129, 243, 253)'
          }
        },
        { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
          type: 'inside',
          yAxisIndex: 0,
          zoomOnMouseWheel: false, // 滚轮是否触发缩放
          moveOnMouseMove: true, // 鼠标滚轮触发滚动
          moveOnMouseWheel: true
        }
      ],
      grid: {
        left: '5%',
        right: '5%',
        bottom: '5%',
        top: '10%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none'
        },
        formatter: function (params) {
          return params[0].name + '<br/>' +
                "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
                // params[0].seriesName + ' : ' + Number((params[0].value.toFixed(4) / 10000).toFixed(2)).toLocaleString() + ' <br/>'
                params[0].seriesName + ' : ' + params[0].value
        }
      },
      backgroundColor: '',
      xAxis: {
        show: false,
        type: 'value'
      },
      yAxis: [{
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            color: '#fff'
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        data: className.value
      }, {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: '12'
          },
          formatter: function (value) {
            return value
          }
        },
        data: data.value
      }],
      series: [{
        name: 'SNR',
        type: 'pictorialBar',
        zlevel: 1,
        itemStyle: {
          normal: {
            barBorderRadius: 0,
            color: '#5CD6EE'

          }
        },
        symbol: 'rich', // 图形类型，带圆角的矩形
        symbolMargin: '3', // 图形垂直间隔
        symbolRepeat: true, // 图形是否重复
        symbolSize: [5, 20], // 图形元素的尺寸
        barWidth: 20,
        data: data.value
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 20,
        barGap: '-100%',
        data: defaultData,
        itemStyle: {
          normal: {
            color: 'rgba(24,31,68,1)',
            barBorderRadius: 0
          }
        }
      }
      ]
    }

    const showLoading = ref(false)
    onMounted(() => {
      const chartDom2 = document.getElementById('main2')
      const myChart2 = echarts.init(chartDom2, 'dark')

      const drawChart = () => {
        option.value = {
          dataZoom: [
            {
              show: true, // 为true 滚动条出现
              realtime: true,
              type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
              startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
              endValue: dataZoomMove.end,
              width: 6,
              right: '20',
              top: '20%', // 位置和grid配置注意下
              height: '56%',
              yAxisIndex: [0, 1], // 关联多个y轴
              moveHandleStyle: {
                color: 'rgba(89, 202, 241,.5)'
              },
              moveHandleSize: '6',
              emphasis: {
                moveHandleStyle: {
                  color: 'rgba(89, 202, 241,.5)'
                }
              },
              textStyle: {
                color: 'rgba(255,255,255,0)'
              },
              backgroundColor: 'rgba(255,255,255,.1)',
              borderColor: 'rgba(255,255,255,0)',
              fillerColor: 'rgba(0,0,0,0)',
              handleSize: '6',
              handleStyle: {
                color: 'rgba(255,255,255,0)'
              },
              brushStyle: {
                color: 'rgba(129, 243, 253)'
              }
            },
            { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
              type: 'inside',
              yAxisIndex: 0,
              zoomOnMouseWheel: false, // 滚轮是否触发缩放
              moveOnMouseMove: true, // 鼠标滚轮触发滚动
              moveOnMouseWheel: true
            }
          ],
          grid: {
            left: '5%',
            right: '5%',
            bottom: '5%',
            top: '10%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'none'
            },
            formatter: function (params) {
              return params[0].name + '<br/>' +
                "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
                // params[0].seriesName + ' : ' + Number((params[0].value.toFixed(4) / 10000).toFixed(2)).toLocaleString() + ' <br/>'
                params[0].seriesName + ' : ' + params[0].value
            }
          },
          backgroundColor: 'rgb(20,28,52)',
          xAxis: {
            show: false,
            type: 'value'
          },
          yAxis: [{
            type: 'category',
            inverse: true,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            data: className.value
          }, {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#ffffff',
                fontSize: '12'
              },
              formatter: function (value) {
                return value
              }
            },
            data: data.value
          }],
          series: [{
            name: 'SNR',
            type: 'pictorialBar',
            zlevel: 1,
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: '#5CD6EE'

              }
            },
            symbol: 'rich', // 图形类型，带圆角的矩形
            symbolMargin: '3', // 图形垂直间隔
            symbolRepeat: true, // 图形是否重复
            symbolSize: [5, 20], // 图形元素的尺寸
            barWidth: 20,
            data: data.value
          },
          {
            name: '背景',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            data: defaultData,
            itemStyle: {
              normal: {
                color: 'rgba(24,31,68,1)',
                barBorderRadius: 0
              }
            }
          }
          ]
        }
      }

      // 初始化图表
      myChart2.setOption(option)

      // 监听 props.snr 的变化，当 props.snr 发生变化时，更新柱状图数据
      watch(() => props.SNR, (newValue, oldValue) => {
        showLoading.value = (newValue.x.length === 0) || (newValue.y.length === 0)
        // 在这里根据新的 props.snr 值更新柱状图数据
        data.value = newValue.y
        className.value = newValue.x

        drawChart()

        // 更新图表配置
        myChart2.setOption(option.value)
      })

      // 自动轮播和鼠标移入移出的停止和开启
      let dataZoomMoveTimer = null
      const startMoveDataZoom = (myChart, dataZoomMove) => {
        dataZoomMoveTimer = setInterval(() => {
          dataZoomMove.start += 1
          dataZoomMove.end += 1
          if (dataZoomMove.end > className.value.length - 1) {
            dataZoomMove.start = 0
            dataZoomMove.end = 4
          }
          myChart.setOption({
            dataZoom: [
              {
                type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
                startValue: dataZoomMove.start,
                endValue: dataZoomMove.end
              }
            ]
          })
        }, 1000)
      }
      startMoveDataZoom(myChart2, dataZoomMove)
      const chartDom = myChart2.getDom()
      chartDom.addEventListener('mouseout', () => {
        if (dataZoomMoveTimer) return
        const dataZoomMove_get = myChart2.getOption().dataZoom[0]
        dataZoomMove.start = dataZoomMove_get.startValue
        dataZoomMove.end = dataZoomMove_get.endValue
        startMoveDataZoom(myChart2, dataZoomMove)
      })
      // 移入
      // myChart.on
      chartDom.addEventListener('mouseover', () => {
        clearInterval(dataZoomMoveTimer)
        dataZoomMoveTimer = undefined
      })
    })
    return {
      showLoading
    }
  }
})
</script>
