<template>
  <div>
    <div id="hikcamera"/>
    <button type="primary" @click="login">Login</button>
    <button type="default" @click="logout">Logout</button>
    <button type="primary" @click="playVideo">Play</button>
    <button type="danger" @click="stop">Stop</button>
  </div>
</template>

<!-- <script src="../static/hikvision/jquery-1.7.1.min.js"></script> -->
<!-- <script src="../static/hikvision/webVideoCtrl.js"></script> -->
<script>
const oPlugin = {
  iWidth: 530, // plugin width
  iHeight: 270 // plugin height
}

const oLiveView = {
  iProtocol: 1, // protocol 1：http, 2:https
  szIP: '*************', // protocol ip
  szPort: 80, // protocol port
  szUsername: 'admin', // device username
  szPassword: 'Dtt123456', // device password
  iStreamType: 1, // stream 1：main stream  2：sub-stream  3：third stream  4：transcode stream
  iChannelID: 1, // channel no
  bZeroChannel: false, // zero channel
  iRtspPort: 554
}
// const WebVideoCtrl = require('./static/hikvision/webVideoCtrl.js')
// @ is an alias to /src
// import HelloWorld from '@/components/HelloWorld.vue'
export default {
  name: 'home',
  mounted () {
    const jquerySrc = '/jquery-1.7.1.min.js'
    const hikSrc = '/webVideoCtrl.js'

    const scriptForJquery = document.createElement('script')
    scriptForJquery.type = 'text/javascript'
    scriptForJquery.src = jquerySrc
    document.body.appendChild(scriptForJquery)
    scriptForJquery.onerror = function (oError) {
      console.info(oError)
    }
    scriptForJquery.onload = function () {
      console.info(`jquery loaded successfully! == ${jquerySrc}`)

      const scriptForHik = document.createElement('script')
      scriptForHik.type = 'text/javascript'
      scriptForHik.src = hikSrc
      document.body.appendChild(scriptForHik)
      scriptForHik.onerror = function (oError) {
        console.info(oError)
      }
      scriptForHik.onload = function () {
        console.info(`webVideoCtrl loaded successfully! == ${hikSrc}`)

        // eslint-disable-next-line no-undef
        WebVideoCtrl.I_InitPlugin(oPlugin.iWidth, oPlugin.iHeight, {
          bWndFull: true, // 是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
          iWndowType: 1,
          cbInitPluginComplete: function () {
            console.info('1')
            // eslint-disable-next-line no-undef
            WebVideoCtrl.I_InsertOBJECTPlugin('hikcamera')
            console.info('2')
            // 检查插件是否最新
            // eslint-disable-next-line no-undef
            if (WebVideoCtrl.I_CheckPluginVersion() === -1) {
              console.info(
                '检测到新的插件版本，双击开发包目录里的WebComponentsKit.exe升级！'
              )
            }
          }
        })
      }
    }
  },
  methods: {
    login () {
      // eslint-disable-next-line no-undef
      const iRet = WebVideoCtrl.I_Login(
        oLiveView.szIP,
        oLiveView.iProtocol,
        oLiveView.szPort,
        oLiveView.szUsername,
        oLiveView.szPassword,
        {
          success: function (xmlDoc) {
            this.$message.success('登录成功！')
          },
          error: function (status, xmlDoc) {
            this.$message.error(`登录成功！ ${status}`)
          }
        })

      if (iRet === -1) {
        this.$message.success('已登录过！')
      }
    },
    logout () {
      const ip = `${oLiveView.szIP}_${oLiveView.szPort}`

      // eslint-disable-next-line no-undef
      const iRet = WebVideoCtrl.I_Logout(ip)
      if (iRet === 0) {
        this.$message.success('退出成功！')
      } else {
        this.$message.error('退出失败！')
      }
    },
    playVideo () {
      const szDeviceIdentify = oLiveView.szIP + '_' + oLiveView.szPort
      // eslint-disable-next-line no-undef
      WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
        iStreamType: oLiveView.iStreamType,
        iChannelID: oLiveView.iChannelID,
        bZeroChannel: oLiveView.bZeroChannel,
        success: function () {
          this.$message.success('开始预览成功！')
        },
        error: function (status, xmlDoc) {
          if (status === 403) {
            this.$message.error('设备不支持Websocket取流！')
          } else {
            this.$message.error('开始预览失败！')
          }
        }
      })
    },
    playAgain () {
      // eslint-disable-next-line no-undef
      WebVideoCtrl.I_InitPlugin(oPlugin.iWidth, oPlugin.iHeight, {
        bWndFull: true, // 是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
        iWndowType: 1,
        cbInitPluginComplete: function () {
          console.info('1')
          // eslint-disable-next-line no-undef
          WebVideoCtrl.I_InsertOBJECTPlugin('hikcamera')
          console.info('2')
          // 检查插件是否最新
          // eslint-disable-next-line no-undef
          if (WebVideoCtrl.I_CheckPluginVersion() === -1) {
            console.info(
              '检测到新的插件版本，双击开发包目录里的WebComponentsKit.exe升级！'
            )
            return
          }

          // 登录设备
          // eslint-disable-next-line no-undef
          WebVideoCtrl.I_Login(
            oLiveView.szIP,
            oLiveView.iProtocol,
            oLiveView.szPort,
            oLiveView.szUsername,
            oLiveView.szPassword,
            {
              success: function (xmlDoc) {
                console.info('3')
                // 开始预览
                const szDeviceIdentify = oLiveView.szIP + '_' + oLiveView.szPort
                // eslint-disable-next-line no-undef
                WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
                  iStreamType: oLiveView.iStreamType,
                  iChannelID: oLiveView.iChannelID,
                  bZeroChannel: oLiveView.bZeroChannel
                })
              }
            }
          )
        }
      })
    },
    stop () {
      // eslint-disable-next-line no-undef
      WebVideoCtrl.I_Stop({
        success: function () {
          this.$message.success('停止预览成功！')
        },
        error: function () {
          this.$message.error('停止预览失败！')
        }
      })
    }
  }
}
</script>
