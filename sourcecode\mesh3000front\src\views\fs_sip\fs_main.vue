<template>
    <dv-full-screen-container class="background">
        <div class="d-flex jc-center title_wrap">
        <div class="zuojuxing"></div>
          <div class="youjuxing"></div>
          <div class="guang"></div>
          <div class="d-flex jc-center">
            <div class="title">
              <span class="title-text">软交换调度管理平台</span>
            </div>
            <div @click="exit" style="position: absolute; top:10%;right:3%;">
                <dv-decoration-11  style="width:160px;height:60px;">
                    <a-button class="exit-button" type="link">
                        退出
                    </a-button>
                </dv-decoration-11>
            </div>
            <!-- 导航菜单 -->
            <div class="navigation-container" style="position: absolute; top:20%;left:2%;">
                <div class="nav-menu">
                    <div
                        class="nav-item"
                        :class="{ 'nav-item-active': currentActiveTab === 'dispatch' }"
                        @click="go_dispatch"
                    >
                        <dv-border-box-6 style="width:110px;height:40px;">
                            <dv-border-box-8 style="width:110px;height:40px;">
                                <span class="nav-text">语音调度</span>
                            </dv-border-box-8>
                        </dv-border-box-6>
                    </div>
                    <div
                        class="nav-item"
                        :class="{ 'nav-item-active': currentActiveTab === 'meeting' }"
                        @click="go_meeting"
                    >
                        <dv-border-box-6 style="width:110px;height:40px;">
                            <dv-border-box-8 style="width:110px;height:40px;">
                                <span class="nav-text">会议</span>
                            </dv-border-box-8>
                        </dv-border-box-6>
                    </div>
                    <div
                        class="nav-item"
                        :class="{ 'nav-item-active': currentActiveTab === 'broadcast' }"
                        @click="go_broadcast"
                    >
                        <dv-border-box-6 style="width:110px;height:40px;">
                            <dv-border-box-8 style="width:110px;height:40px;">
                                <span class="nav-text">广播</span>
                            </dv-border-box-8>
                        </dv-border-box-6>
                    </div>
                </div>
            </div>

          </div>
          <!-- <div class="content_left">
                <dv-border-box-10>dv-border-box-10</dv-border-box-10>
            </div> -->
        </div>
        <!-- <div style="height: 60px; width: 100%; background-color: bisque;"></div> -->
        <div v-if = showDispatch  class="index-box">
            <!-- 第一列 -->
            <div class="content_left">
              <div class="item_title" style="height: 60%;">
                  <dv-border-box-13>
                      <div class="item_title">
                      <div class="zuo">
                      </div>
                      <span class="title-inner"> 组织架构 &nbsp;&nbsp; </span>
                      <!-- <a-button id="button_col1_person" @click="showLeftTreeUser" size="small" type="link" style="position: absolute; top:23%;left:2%; color:rgb(149, 135, 145)">
                          <template #icon><EditOutlined style="color:rgb(74, 27, 216)"/></template>
                          人员
                      </a-button>
                      <a-button id="button_col1_meeting" @click="showLeftTreeMeet" size="small" type="link" style="position: absolute; top:23%;left:15%; color:rgb(149, 135, 145)">
                          <template #icon><EditOutlined style="color:rgb(122, 242, 170)"/></template>
                          会议
                      </a-button> -->
                      <a-button @click="getLeftTree" size="small" type="link" style="position: absolute; top:23%;right:15%; color:rgb(122, 242, 170)">
                          刷新
                      </a-button>
                      <a-button @click="userManage" size="small" type="link" style="position: absolute; top:23%;right:2%; color:rgb(122, 242, 170)">
                          管理
                      </a-button>
                      </div>
                      <div style="position: absolute;top:15%;height:60%;left:3%;width:94%">
                        <a-tree v-if="!ifShow.leftTree"
                          style="height: 60%; overflow: auto; width: 100%"
                          v-model:expandedKeys="expandedKeys"
                          v-model:selectedKeys="selectedKeys"
                          v-model:checkedKeys="checkedKeys"
                          show-line
                          checkable
                          :tree-data="tmpTreeData"
                        >
                        <template #title="item">
                          <span v-if="item.type=='department'" >{{item.title}}</span>
                          <span v-else-if="item.type=='member'&&item.call_status=='离线'" style="color:rgb(149, 135, 145)">{{item.title}}</span>
                          <span v-else style="color:#8ac385">{{item.title}}</span>
                        </template>
                        </a-tree>
                        <a-tree v-else
                          style="height: 400px; overflow: auto; width: 100%"
                          v-model:expandedKeys="expandedMeetKeys"
                          v-model:selectedKeys="selectedMeetKeys"
                          v-model:checkedKeys="checkedMeetKeys"
                          show-line
                          :tree-data="tmpMeetTreeData"
                        >
                        </a-tree>
                      </div>
                  </dv-border-box-13>
              </div>
              <div class="item_title" style="height: 38%;">
                <dv-border-box-13>
                    <div class="item_title">
                    <div class="zuo">
                    </div>
                    <span class="title-inner"> &nbsp;&nbsp;坐席信息 &nbsp;&nbsp; </span>
                    <div class="you"></div>
                    </div>
                        <hostInfoCard  :host = "hostUserForComponents"></hostInfoCard>
                </dv-border-box-13>
          </div>
            </div>
        <!-- 第二列 -->
        <div  class="content_center">
            <!-- 历史通话卡片 -->
            <!-- <a-card v-if="ifShow.hisCard" hoverable style="width: 600px;background-color: #162130ce;font-size: large;">
              <div >
                <a-select
                  v-model:value="selectedHisUser"
                  show-search
                  placeholder="选择用户"
                  style="width: 200px"
                  :options="hisUserOptions"
                  :filter-option="hisFilterOption"
                  @focus="handleHisFocus"
                  @blur="handleHisBlur"
                  @change="handleHisChange"
                ></a-select>
              </div>
              <a-range-picker  v-model:value="rangpickdate" style="margin-left:10px" :disabled-date="disabledDate" :disabled-time="disabledRangeTime" :show-time="{
                                 hideDisabledOptions: true,
                                 defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
                               }" format="YYYY-MM-DD HH:mm:ss" />
              <a-divider />
              <a-button type="primary" @click="queryHis(selectedHisUser)">确定</a-button>
              <a-button type="primary" @click="cancel_queryHis()">取消</a-button>
            </a-card> -->

        <!-- 部门管理的card -->
        <a-card title="部门管理" style="height: 60%" :bordered="false" v-if="ifShow.userManage" >
          <template #extra><a-button class="get-btn" type="primary" @click="quit_userManage" v-if="mytreeData.length">退出</a-button></template>
          <a-button type="primary" @click="addComp" v-if="!mytreeData.length">添加根节点</a-button>
            <a-tree v-if = '!showLeftTree'
              style="height: 500px; overflow: auto"
              :autoExpandParent="true"
              :showIcon="false"
              :tree-data="mytreeData"
              defaultExpandAll=true
              showLine blockNode>
              <template v-slot:title="nodeData">
                <span @click="showNode(nodeData)"> {{nodeData.name}} </span>
                <a-button-group v-if="nodeData.name!='无组织人员'" style="float:right">
                  <!--            <a-button size="small" @click="slotAddSame(nodeData)" icon="plus-circle" title="添加同级"></a-button>-->
                  <a-button size="small" @click="addComp(nodeData)" title="添加下级">
                    <PlusSquareOutlined />
                  </a-button>
                  <a-button size="small" @click="slotModify(nodeData)" title="修改">
                    <FormOutlined />
                  </a-button>
                  <a-popconfirm title="确定删除该节点吗？" ok-text="确定" cancel-text="取消" @confirm="confirmDel">
                    <a-button size="small" @click="slotDelete(nodeData)" title="删除">
                      <DeleteOutlined />
                    </a-button>
                  </a-popconfirm>
                </a-button-group>
              </template>
            </a-tree>
            <a-modal
              v-model:visible="ifShow.deptManage"
              :title="inputTitle.title"
              @ok="handleOk"
              ok-text="确定"
              cancel-text="取消"
            >
              <a-checkbox
                v-model:checked="checked_deptOrUser"
                v-if="ifShow.checkbox"
                @change="onCheckChange"
              >{{inputTitle.checkTitle}}
              </a-checkbox>
              <a-divider />
              {{inputTitle.type}}<a-input v-model:value="compName" placeholder="请输入名称" />
              {{inputTitle.number}}<a-input v-if="ifShow.inputNumber" v-model:value="compNum" placeholder="请输入号码" />
            </a-modal>
        </a-card>

          <!-- 人员列表card展示形式 -->
           <loding2 v-if="false"></loding2>
           <div class="content_center_personnel_list">
                <personnelList v-if="!ifShow.userList&&!ifShow.leftTree&&!ifShow.userManage&&!ifShow.meetingPanel" :userList = "currList" :userStatusChange="userStatusChange" @selectUser="userListUpdate" @bt-change-mode-click="changeShowUserListMode" ></personnelList>
           </div>

          <dv-border-box-13 v-if = "ifShow.userList&&!ifShow.leftTree&&!ifShow.meetingPanel" style="padding: 3%">
            <a-row justify="end" style="margin-top: 1%;">
                <div @click="switch_show">
                 <button2 button_name="卡片模式"></button2>
                </div>
            </a-row>
          <a-table v-if = "ifShow.userList&&!ifShow.leftTree&&!ifShow.meetingPanel"
            style="margin-top:1%;background-color: #ffffff00;"
            :pagination="{ pageSize: 12 }"
            class="ant-table-striped"
            size="middle"
            :columns="user_columns"
            :data-source="currList"
            bordered>
            <template #emptyText>
              <empty-table />
            </template>
            <template #operation="{ record}">
                  <!-- <div class="editable-row-operations"> -->
                  <div class="demo-dropdown-wrap">
                    <a-dropdown :disabled = "if_userlist_dropdown">
                      <template #overlay>
                        <a-menu @click="handleMenuClick">
                          <a-menu-item key="1">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              呼叫
                            </a>
                          </a-menu-item>
                          <a-menu-item key="3">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              挂断
                            </a>
                          </a-menu-item>
                          <a-menu-item key="4">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              监听
                            </a>
                          </a-menu-item>
                          <a-menu-item key="5">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              设为坐席
                            </a>
                          </a-menu-item>
                          <a-menu-item key="6">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              转接
                            </a>
                          </a-menu-item>
                          <a-menu-item key="7">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              代答
                            </a>
                          </a-menu-item>
                          <a-menu-item key="8">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              强拆
                            </a>
                          </a-menu-item>
                          <a-menu-item key="9">
                            <UserOutlined />
                            <a @click="handleMenuClickByKey(record)">
                              强插
                            </a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button >
                        选择
                        <!-- <DownOutlined /> -->
                      </a-button>
                    </a-dropdown>
                  </div>
            </template>
           </a-table>
        </dv-border-box-13>
          <!-- 会议列表的table -->
          <a-table v-if = "ifShow.meetList&&ifShow.leftTree"
            style="margin-top:0.5%;background-color: #ffffff00;"
            :pagination="{ pageSize: 8 }"
            class="ant-table-striped"
            size="middle"
            :columns="meet_columns"
            :data-source="currMeetList"
            bordered>
            <template #emptyText>
              <empty-table />
            </template>
            <template #operation="{ record}">
              <div class="demo-dropdown-wrap">
                    <a-dropdown :disabled = "if_userlist_dropdown">
                      <template #overlay>
                        <a-menu @click="handleMeetMenuClick">
                          <a-menu-item key="1">
                            <UserOutlined />
                            <a @click="handleMeetMenuClickByKey(record)">
                              详情
                            </a>
                          </a-menu-item>
                          <a-menu-item key="2">
                            <UserOutlined />
                            <a @click="handleMeetMenuClickByKey(record)">
                              结束
                            </a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button >
                        选择
                      </a-button>
                    </a-dropdown>
                  </div>
            </template>
           </a-table>
            <a-modal v-model:visible="ifShow.broadcastPanel" title="广播设置" style="width:50%" ok-text="确定" cancel-text="取消">
              <template #footer>
              </template>
              <a-checkbox
                v-model:checked="broadcast_state.mute"
              >
              禁言
              </a-checkbox>
              <a-button @click="queryFileClickByKey">
                  查询
              </a-button>
              <a-divider />
              <a-upload
                action="api/fs_interface/upload_file_url"
                :multiple="true"
                :file-list="fileList"
                @change="handleFileChange"
              >
                <a-button>
                  <UploadOutlined></UploadOutlined>
                  上传
                </a-button>
              </a-upload>
              <a-table
                style="margin-top:0.5%;background-color: #ffffff00;"
                :pagination="{ pageSize: 3 }"
                class="ant-table-striped"
                size="middle"
                :columns="file_columns"
                :data-source="fileBroadList"
                bordered>
                <template #emptyText>
                  <empty-table />
                </template>
                <template #operation="{ record}">
                  <a-button @click="selFileClickByKey(record)">
                      选中
                  </a-button>
                  <a-button @click="delFileClickByKey(record)">
                      删除
                  </a-button>
                </template>
              </a-table>
              <a-divider />
              <a-radio-group v-model:value="broadcastPlayValue">
                <a-radio :value="1">循环播放</a-radio>
                <a-radio :value="2">播放一次</a-radio>
                <a-radio :value="3">停止播放</a-radio>
              </a-radio-group>
              <a-divider />
              <a-button  @click="handlebroadcastClick" style="font-size: 12px;">
                 确定
              </a-button>
            </a-modal>
            <!-- 中间底部控制条按钮 -->
            <ctrlBar  v-if = "!ifShow.userList&&!ifShow.leftTree&&!ifShow.userManage&&!ifShow.meetingPanel" class="content_center_ctrl_bar"  @bt-click="btclick"></ctrlBar>
      </div>
        <!-- 第三列 -->

        <div class="content_right">
          <div class="item_title" style="height: 60%;">
                <dv-border-box-13 style="padding: 2%">
                    <div v-if="queueList.length != 0">
                        <a-table
                        style="background-color: #ffffff00;font-size: 80%"
                        :pagination="{ pageSize: 5 }"
                        class="ant-table-striped"
                        :columns="queue_columns"
                        :data-source="queueList"
                        :bordered="false">
                        <template #emptyText>
                          <empty-table />
                        </template>
                        <template #operation="{ record}">
                                <a-button  @click = handleQueueMenuClickByKey(record) style="font-size: 12px;">
                                接听
                                </a-button>
                        </template>
                        </a-table>
                    </div>
                    <div v-if="queueList.length == 0" style="margin: 30% auto; width: 50%;height: 100%">
                        <dv-decoration-12 style="height:150px;margin: 40% auto;width: 80%" />
                        <div sstyle="top: 30%;" >等待呼入中。。。</div>
                    </div>
                </dv-border-box-13>
          </div>
          <div class="item_title" style="height: 38%;">
                <dv-border-box-13 style="padding: 1%">
                    <div class="item_title">
                        <div class="zuo">
                        </div>
                        <span class="title-inner"> &nbsp;&nbsp;消息队列 &nbsp;&nbsp; </span>
                        <div class="you"></div>
                    </div>
                        <a-modal v-model:visible="messVisible" title="消息处理" class="tech-modal" ok-text="确定" cancel-text="取消">
                            <a-table
                                :pagination="{ pageSize: 8 }"
                                class="ant-table-striped tech-table"
                                size="middle"
                                :columns="info_columns"
                                :data-source="infoList"
                                bordered
                            >
                                <template #operation="{ record}">
                                  <div class="editable-row-operations">
                                    <span>
                                      <a-button @click="deleteInfo((record.key))" class="tech-button tech-button-danger">清除</a-button>
                                    </span>
                                  </div>
                                </template>
                            </a-table>
                        </a-modal>

                    <!--队列滚动列表-->
                    <div class="message-queue-content">
                        <rightTop v-if="warningInfo.length != 0" @click="warningInfoClick" :warningInfo="warningInfo"></rightTop>
                        <!--队列列表为空的占位-->
                        <div v-if="warningInfo.length == 0 || warningInfo == undefined" class="empty-message-container">
                            <dv-decoration-12 style="height:120px;margin: 15% auto;width: 60%" />
                            <div class="empty-message-text">当前无消息</div>
                        </div>
                    </div>
                </dv-border-box-13>
          </div>
        </div>
      </div>

      <div v-if = showMeeting style="height: 95%;padding: 1%;">
        <dv-border-box-13>
        <div class="parent" style="height: 100%; padding: 1%;" >
            <div class="div1">
                    <div class="radio-input">
                        <input @click="clickMeetingList" value="value-1" name="value-radio" id="value-1" type="radio" checked>
                        <label for="value-1">会议列表</label>
                        <input @click="clickRuingMeeting" value="value-2" name="value-radio" id="value-2" type="radio">
                        <label for="value-2">当前会议</label>
                        <input @click="clickHistoryMeetingList" value="value-3" name="value-radio" id="value-3" type="radio">
                        <label for="value-3">会议历史</label>
                    </div>
            </div>
            <div class="div2">
                <meetingList
                :meetingLsit="showMeetingLsit"
                :currMeetList="currMeetList"
                :personalList="currList"
                :his_meeting_dataSource="his_meeting_dataSource"
                :addMember="addMember"
                :delMember="delMember"
                :memberStatus="memberStatus"
                @creatMeeting="createMeeting"
                @stopMeeting = "stopMeeting"
                @forbiddenSpeech= "forbiddenSpeech"
                @disForbiddenSpeech = "disForbiddenSpeech"
                @quarantine = "quarantine"
                @disQuarantine = "disQuarantine"
                @kickOut = "kickOut"
                >
              </meetingList>
            </div>
        </div>
    </dv-border-box-13>
      </div>

      <!-- 广播页面 -->
      <div v-if="showBroadcast" style="height: 95%;padding: 1%;">
        <dv-border-box-13>
          <div class="parent" style="height: 100%; padding: 1%;">
            <div class="div1">
              <div class="radio-input">
                <input @click="clickBroadcastList" value="value-1" name="broadcast-radio" id="broadcast-1" type="radio" checked>
                <label for="broadcast-1">广播列表</label>
                <input @click="clickCurrentBroadcast" value="value-2" name="broadcast-radio" id="broadcast-2" type="radio">
                <label for="broadcast-2">当前广播</label>
                <input @click="clickHistoryBroadcast" value="value-3" name="broadcast-radio" id="broadcast-3" type="radio">
                <label for="broadcast-3">广播历史</label>
              </div>
            </div>
            <div class="div2">
              <broadcast
                :meetingLsit="showBroadcastLsit"
                :currMeetList="currBoradcastList"
                :personalList="currList"
                :his_meeting_dataSource="his_meeting_dataSource"
                :addMember="addMember"
                :delMember="delMember"
                :memberStatus="memberStatus"
                @creatMeeting="createBrocast"
                @stopMeeting="stopBrocast"
                @forbiddenSpeech="forbiddenSpeech"
                @disForbiddenSpeech="disForbiddenSpeech"
                @quarantine="quarantine"
                @disQuarantine="disQuarantine"
                @kickOut="kickOut"
              >
              </broadcast>
            </div>
          </div>
        </dv-border-box-13>
      </div>
  </dv-full-screen-container>
</template>
<script>
/*                            _ooOoo_
 *                           o8888888o
 *                           88" . "88
 *                           (| -_- |)
 *                            O\ = /O
 *                        ____/`---'\____
 *                      .   ' \\| |// `.
 *                       / \\||| : |||// \
 *                    / _||||| -卍- |||||- \
 *                       | | \\\ - /// | |
 *                     | \_| ''\---/'' | |
 *                      \ .-\__ `-` ___/-. /
 *                   ___`. .' /--.--\ `. . __
 *                ."" '< `.___\_<|>_/___.' >'"".
 *               | | : `- \`.;`\ _ /`;.`/ - ` : | |
 *                 \ \ `-. \_ __\ /__ _/ .-` / /
 *         ======`-.____`-.___\_____/___.-`____.-'======
 *                            `=---='
 *
 *         .............................................
 *        佛祖保佑                  永无BUG
 */
import { defineComponent, ref, reactive, onUnmounted, onMounted, watch, toRaw, toRefs, h } from 'vue'
import { message, notification } from 'ant-design-vue'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import Router from '@/router/index'
import { FsAction } from '@/views/deviceinfo/action/fsAction'
import rightTop from '@/views/fs_sip/right_top.vue'
import dayjs from 'dayjs'
import { Utils } from '@/common/utils'
import Pattern from '@/common/pattern'
import personnelList from '@/views/components/dispatch/personnel_list.vue'
import hostInfoCard from '@/views/components/dispatch/host_info_card.vue'
import loding2 from '@/views/components/dispatch/loding2.vue'
import ctrlBar from '@/views/components/dispatch/ctrl_bar.vue'
import button2 from '@/views/components/dispatch/button2.vue'
import meetingList from '@/views/components/dispatch/meeting_list.vue'
import broadcast from '@/views/components/dispatch/broadcast.vue'
import EmptyTable from '@/components/EmptyTable.vue'
import {
  PlusSquareOutlined,
  FormOutlined,
  DeleteOutlined,
  UserOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'fs_main',
  components: {
    rightTop,
    personnelList,
    hostInfoCard,
    ctrlBar,
    loding2,
    button2,
    meetingList,
    broadcast,
    EmptyTable,
    PlusSquareOutlined,
    FormOutlined,
    DeleteOutlined,
    UserOutlined,
    UploadOutlined
  },
  setup () {
    const fsAction = new FsAction()

    // mqtt clicent 对象
    let mqttClient = null

    const utils = new Utils() // 创建工具类
    const currList = ref([]) // 用户列表
    const queueList = ref([]) // 呼叫队列
    const queueOptions = ref([]) // 呼叫队列下拉列表项
    const search_user = ref() // 搜索指定用户
    let currAllQueue = null
    let currAllUser = []
    const userStatusChange = ref({})
    // 坐席用户
    let hostNumber = null
    // 消息队列
    const queueValue = ref()
    const warningInfo = ref([])
    const infoList = ref([]) // 表单中的消息队列
    let warningTemp = [] // 实时告警的临时变量，用于接收实时告警信息
    const expandedKeys = ref([])
    const selectedKeys = ref([])
    const tmpTreeData = ref([])
    const treeData = []
    let hostUser = null
    const hostUserForComponents = ref(null)
    const currHostUser = ref({})
    let curr_key = null // 当前key
    let curr_selectkey = '' // 当前选中用户树节点，默认选中根节点
    // let repeat_tree = null // 用户树刷新定时器
    // let repeat_queue = null // 呼叫队列刷新定时器

    const messVisible = ref(false)
    const if_userlist_dropdown = ref(false) // 防止重复选中
    const if_queuelist_dropdown = ref(false) // 防止重复选中

    let selectUserListForCard = [] // 卡片视图中已经选中的人员列表

    const ifShow = ref({
      hisCard: false, // 是否显示历史通话表单
      buttonHis: true, // 是否显示历史通话按钮
      userList: false, // 是否显示用户列表
      userCard: true, // 是否显示用户卡片
      meetList: false, // // 是否显示会议列表
      meetCard: false, // // 是否显示会议卡片
      hisMeetList: false, // 是否显示历史会议列表
      userManage: false, // 是否显示用户管理界面
      deptManage: false, // 是否显示部门管理界面
      inputNumber: false, // 是否显示人员部门管理的用户号码输入框
      checkbox: false, // 部门和人员切换
      meetingPanel: false, // 语音会议输入面板
      searchUser: true, // 用户搜索框
      leftTree: false, // 左侧人员或会议目录
      addMember: false, // 拉人选择用户面板
      broadcastPanel: false // 广播选择项面板
    })

    // 历史通话表单
    const selectHisUser = ref()
    const hisUserOptions = ref([])
    const selectedHisUser = ref()
    const rangpickdate = ref()
    let queryHisUser = {}
    const ifSpinning = ref(false)
    let spinTip = ''
    const spinWaitTime = 500
    let if_server_response = false
    const inputTitle = ref({
      checkTitle: '部门管理',
      title: '',
      type: '',
      number: ''
    })
    const arrall = {}

    // 用户管理相关变量

    const mytreeData = ref([])
    const parentNode = ref({})
    const compName = ref('')
    const compNum = ref('')
    const isUpdate = ref(false)
    const state = reactive({
      checked_deptOrUser: false,
      checked_date: false
    })
    const userManageQueue = ref([])
    let userManage_select = {}
    const showLeftTree = ref(true)

    const showDispatch = ref(true)

    const showBroadcast = ref(false)

    const showMeeting = ref(false)

    // 当前活跃的导航标签
    const currentActiveTab = ref('dispatch')

    // 会议用相关变量
    const meetingMember = ref([]) // 会议成员
    const selectedMember = ref([]) // 选中的成员
    const showMeetingLsit = ref(1) // 默认打开会议列表
    const showBroadcastLsit = ref(1) // 默认打开广播列表
    let createMeetingSendData = [] // 创建会议时候发送给后端的会议信息数据
    let createBrocastSendData = [] // 创建会议时候发送给后端的会议信息数据

    const checkedKeys = ref([])
    let nociceMeet = ref({
      meet_title: '',
      meet_topic: '',
      meet_number: ''
    })
    const holdMeet = ref({})
    holdMeet.value.userList = ''
    const expandedMeetKeys = ref([])
    const selectedMeetKeys = ref([])
    const tmpMeetTreeData = ref([])
    const formRef = ref()
    let currMeet_key = null // 当前会议key
    let meetNumber = null // 会议数目
    let currHisMeet_key = null // 历史会议key
    const currMeetUser = ref({}) // 当前查看用户
    const currMeetList = ref([]) // 会议列表
    const currBoradcastList = ref([]) // 广播列表
    const his_meeting_dataSource = ref([]) // 历史会议列表
    const fileBroadList = ref([]) // 播放文件列表
    const currMeet = ref({}) // 当前选中会议
    const hisMeetList = ref([]) // 历史会议列表
    const meetUserOptions = [] // 会议用户多选
    const meetUser_state = reactive({
      indeterminate: true,
      checkAll: false,
      checkedList: []
    }) // 会议用户状态
    const broadcast_state = reactive({
      mute: true
    }) // 会议用户状态
    const addMemberUserValue = ref([]) // 会议添加人员
    const addMemberUserOption = ref([]) // 会议添加人员选择项
    const broadcastPlayValue = ref(1) // 会议广播方式
    const muteList = '' // 会议a禁言名单
    const blockList = '' // 会议广播隔离名单

    const addMember = ref([])
    const delMember = ref([])
    const memberStatus = ref([])
    let refresh_key
    // 回调信息

    let upload_file_url = ''
    let curr_file = {}
    const user_columns = [
      {
        title: '用户姓名',
        dataIndex: 'name',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'name'
        }
      },
      {
        title: '用户号码',
        dataIndex: 'number',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'number'
        }
      },
      {
        title: '部门',
        dataIndex: 'dept',
        width: '15%',
        align: 'center',
        resizable: false,
        ellipsis: true,
        slots: {
          customRender: 'dept'
        }
      },
      {
        title: '状态',
        dataIndex: 'call_status',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'call_status'
        }
      },
      {
        title: '对端号码',
        dataIndex: 'peer_number',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'peer_number'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      },
      {
        title: '',
        dataIndex: 'call_msg',
        width: '0%',
        align: 'center',
        slots: {
          customRender: 'call_msg'
        }
      }
    ]

    const queue_columns = [
      {
        title: '姓名',
        dataIndex: 'name',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'name'
        }
      },
      {
        title: '状态',
        dataIndex: 'call_status',
        width: '40%',
        align: 'center',
        slots: {
          customRender: 'call_status'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '28%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      },
      {
        title: '',
        dataIndex: 'call_msg',
        width: '0%',
        align: 'center',
        slots: {
          customRender: 'call_msg'
        }
      }
    ]

    const info_columns = [
      {
        title: '姓名',
        dataIndex: 'name',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'name'
        }
      },
      {
        title: '时间',
        dataIndex: 'dataTime',
        width: '20%',
        align: 'center',
        defaultSortOrder: 'descend', // 默认上到下为由大到小的顺序
        slots: {
          customRender: 'dataTime'
        }
      },
      {
        title: '事件',
        dataIndex: 'event',
        width: '55%',
        align: 'center',
        slots: {
          customRender: 'event'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      },
      {
        title: '',
        dataIndex: 'call_msg',
        width: '0%',
        align: 'center',
        slots: {
          customRender: 'call_msg'
        }
      }
    ]

    const meet_columns = [
      {
        title: '会议名称',
        dataIndex: 'name',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'name'
        }
      },
      {
        title: '会议号码',
        dataIndex: 'meet_number',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_number'
        }
      },
      {
        title: '会议时间',
        dataIndex: 'meet_time',
        width: '20%',
        align: 'center',
        slots: {
          customRender: 'meet_time'
        }
      },
      {
        title: '参会人员',
        dataIndex: 'member',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'member'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      },
      {
        title: '',
        dataIndex: 'meet_msg',
        width: '0%',
        align: 'center',
        slots: {
          customRender: 'meet_msg'
        }
      }
    ]

    const file_columns = [
      {
        title: '文件名称',
        dataIndex: 'fileName',
        width: '45%',
        align: 'center',
        slots: {
          customRender: 'fileName'
        }
      },
      {
        title: '上传时间',
        dataIndex: 'uploadTime',
        width: '15%',
        align: 'center',
        slots: {
          customRender: 'uploadTime'
        }
      },
      {
        title: '文件路径',
        dataIndex: 'fileUrl',
        width: '30%',
        align: 'center',
        slots: {
          customRender: 'fileUrl'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '10%',
        align: 'center',
        slots: {
          customRender: 'operation'
        }
      }
    ]

    /**
     *@description 会议列表上方导航按钮（会议列表）点击事件
     */
    const clickMeetingList = () => {
      // 打开会议列表
      showMeetingLsit.value = 1
      // 当点击会议列表选项的时候需要重新获取一下会议列表（更新操作）
      getMeetingList()
    }

    /**
     *@description 会议列表上方导航按钮（当前会议）点击事件
     */
    const clickRuingMeeting = () => {
      // 打开正在进行的会议
      showMeetingLsit.value = 2
      // 当点击当前会议选项的时候需要重新获取一下会议列表（更新操作）
      getMeetingList()
    }

    /**
     *@description 会议列表上方导航按钮（会议历史）点击事件
     */
    const clickHistoryMeetingList = () => {
      // 打开历史会议
      showMeetingLsit.value = 3

      // 获取历史会议列表
      fsAction.his_conference_list([], fsQueryHisMeetingSuccess, fsQueryHisMeetingcallbackError, null)
    }

    /**
     *@description 广播列表上方导航按钮（广播列表）点击事件
     */
    const clickBroadcastList = () => {
      // 打开广播列表
      showBroadcastLsit.value = 1
      // 当点击广播列表选项的时候需要重新获取一下广播列表（更新操作）
      getBroadcast()
    }

    /**
     *@description 广播列表上方导航按钮（当前广播）点击事件
     */
    const clickCurrentBroadcast = () => {
      // 打开正在进行的广播
      showBroadcastLsit.value = 2
      // 当点击当前广播选项的时候需要重新获取一下广播列表（更新操作）
      getBroadcast()
    }

    /**
     *@description 广播列表上方导航按钮（广播历史）点击事件
     */
    const clickHistoryBroadcast = () => {
      // 打开历史广播
      showBroadcastLsit.value = 3
      // 获取历史广播列表（如果有相关接口的话）
      // fsAction.his_broadcast_list([], fsQueryHisBroadcastSuccess, fsQueryHisBroadcastcallbackError, null)
    }

    const fileList = ref([])
    const handleFileChange = info => {
      console.log('info:', info)
      const maxSize = 1024 * 1024 // 1MB 示例限制
      const size = info.file.size
      if ((size >= maxSize) && (info.file.percent >= 80)) {
        message.error('上传文件不能大于1M！')
        return
      }

      let tmpList = []
      let resFileList = [...info.fileList]

      // 1. Limit the number of uploaded files
      //    Only to show two recent uploaded files, and old ones will be replaced by the new
      resFileList = resFileList.slice(-1)

      // 2. read from response and show file link
      resFileList = resFileList.map(file => {
        if (file.response) {
          // Component will show file.url as link
          // file.url = file.response.url
          tmpList = file.response.fileList
        }

        fileBroadList.value = []
        tmpList.forEach((item, index, arr) => {
          fileBroadList.value.push({
            id: item.id,
            fileName: item.fileName,
            aliasname: '',
            fileUrl: item.fileUrl,
            uploadTime: item.uploadTime
          })
        })

        return file
      })
      console.log('resFileList', resFileList)
      fileList.value = resFileList

      // 收到上传文件返回的文件列表信息
      // fileBroadList =
    }

    const exit = () => {
      Router.push({ path: '/' })
    }

    /**
     * @description 进入调度的按钮
     */
    const go_dispatch = () => {
      if (showDispatch.value !== undefined || showDispatch.value !== null) {
        // 更新当前活跃标签
        currentActiveTab.value = 'dispatch'
        showDispatch.value = true
        showBroadcast.value = false
        showMeeting.value = false
        // 人员
        getLeftUserTree()
        // 获取所有人员
        getAllUser()
        // 设定坐席
        currAllUser.forEach((item) => {
          if (item.number === hostNumber) {
            setTimeout(() => {
              hostUserForComponents.value = item
            }, 500)
          }
        })
      }
    }

    /**
     * @description 进入广播的按钮
     */
    const go_broadcast = () => {
      if (showBroadcast.value !== undefined || showBroadcast.value !== null) {
        // 更新当前活跃标签
        currentActiveTab.value = 'broadcast'
        showBroadcast.value = true
        showDispatch.value = false
        showMeeting.value = false
        // 获取广播列表
        getBroadcast()
        // // 人员
        // getLeftUserTree()
        // 获取所有广播
        // getAll()
      }
    }

    /**
     * @description 进入会议
     */
    const go_meeting = () => {
      if (showMeeting.value !== undefined || showMeeting.value !== null) {
        // 更新当前活跃标签
        currentActiveTab.value = 'meeting'
        showMeeting.value = true
        showDispatch.value = false
        showBroadcast.value = false
      }
      getMeetingList()
    }

    watch(
      () => meetUser_state.checkedList,
      val => {
        meetUser_state.indeterminate = !!val.length && val.length < meetUserOptions.length
        meetUser_state.checkAll = val.length === meetUserOptions.length
        console.log('meetUser_state.checkedList:', meetUser_state.checkedList)
      }
    )
    const onCheckAllChange = e => {
      console.log('onCheckAllChange, e:', e)
      meetUser_state.checkedList = []
      if (e.target.checked) {
        meetUserOptions.forEach((item) => {
          meetUser_state.checkedList.push(item.value)
        })
      }
    }

    watch(selectedKeys, () => {
      if (ifShow.value.meetingPanel) return
      //   //console.log('selectedKeys', selectedKeys)
      if (selectedKeys.value.length === 0) {
        // //console.log('selectedKeys.value.length === 0')
        return
      }
      //   //console.log('selectedKeys.value', selectedKeys.value)
      const selectArr = selectedKeys.value
      //   //console.log('selectArr.length', selectArr.length)
      //   //console.log('selectArr[0]', selectArr[0])
      curr_selectkey = selectArr[0]
      const ret_data = refreshTreeByKey(curr_selectkey)
      //   //console.log('ret_data', ret_data)
      clear_column2()
      show_defaultColumn2()
      refresh_list(ret_data)
      search_user.value = ''
      ifShow.value.userList = false
      ifShow.value.userManage = false
    })

    /******************************************************************************************
     * ****************************************************************************************
     * @name watch(selectedMeetKeys) 监视会议树形组织图选中操作。
     *  @param data 无
     *
     *@description 根据左侧选中会议在中间区域显示会议列表或卡片
        【已存在的已知问题】：无              【可能存在的问题】：无
     *@description 【是否验证：已经验证         编写人：马三元            日期：2024年9月18日】
     *
     * ***************************************************************************************
     *****************************************************************************************/
    watch(selectedMeetKeys, () => {
      if (ifShow.value.meetingPanel) return
      //   //console.log('selectedKeys', selectedKeys)
      if (selectedMeetKeys.value.length === 0) {
        // //console.log('selectedKeys.value.length === 0')
        return
      }
      console.log('selectedMeetKeys.value', selectedMeetKeys.value)
      const selectArr = selectedMeetKeys.value
      console.log('selectArr.length', selectArr.length)
      console.log('selectArr[0]', selectArr[0])
      currMeet_key = selectArr[0]
      const ret_data = refreshMeetTreeByKey(currMeet_key)
      //   //console.log('ret_data', ret_data)
      clear_column2()
      show_defaultColumn2()
      refresh_meet_list(ret_data)
      search_user.value = ''
      ifShow.value.meetList = true
    })
    /******************************************************************************************
     * ****************************************************************************************
     * @name btclick（id） 按钮的点击响应事件。
     * @param id 由不同按钮绑定的不同ID，用于区分不同按钮的点击事件
     *
     * @description 页面中间下方组件响应的方法，当点击对应按钮的时候，会传入对应按钮的ID，通过不同id就
     * 可以区分按钮，因为绑定原先的按钮的点击事件，所以方法命名有些奇怪。
     *
     * 【已存在的已知问题】：无                   【可能存在的问题】：组呼功能目前没有做，后面需要调试
     * 【是否验证】：已经验证
     *
     *  编写人：孟家宝            日期：2024年12月11日
     *
     * ***************************************************************************************
     *****************************************************************************************/
    const btclick = (id) => {
      switch (id) {
        case 'h-j' : // 呼叫
          dialClick()
          break
        case 'j-t' : // 监听
          dialListenClick()
          break
        case 'z-j' : // 转接
          dialTransClick()
          break
        case 'd-d' : // 代答
          answeringClick()
          break
        case 'q-chai' : //  强拆
          dialSpitClick()
          break
        case 'q-cha' : // 强插
          dialInsertClick()
          break
        case 'z-h' : // 组呼
          alert('组呼')
          break
        case 'g-d' : // 挂断
          dialOffClick()
          break
        case 'q-d' : // 群答
          multiAnswerClick()
          break
      }
    }

    const changeShowUserListMode = () => {
      switch_show()
    }

    const onSubmit = () => {
      formRef.value
        .validate()
        .then(() => {
          //   //console.log('values', nociceMeet, toRaw(nociceMeet))
          confirm_meeting()
        })
        .catch(() => {
          //   //console.log('error', error)
        })
    }

    function refresh_list (data) {
      const curr = []
      //   //console.log('refresh_list', data)
      if (data.type === 'member') {
        curr.push({
          key: data.data.key, // 可以根据实际更改对应的key
          name: data.data.title,
          call_status: data.type === 'department' ? '' : data.data.call_status,
          superUuid: data.superUuid,
          dept: data.data.dept,
          number: data.data.number,
          peer_number: data.data.peer_number,
          call_msg: data.data.call_msg
        })
      } else if (data.type === 'department') {
        data.members.forEach((item) => {
          if ((item.type === 'member') && (item.dept !== '无组织成员')) {
            curr.push({
              key: item.key, // 可以根据实际更改对应的key
              name: item.title,
              call_status: item.call_status,
              superUuid: item.superUuid,
              dept: item.dept,
              number: item.number,
              peer_number: item.peer_number,
              call_msg: item.call_msg
            })
          }
        })
      } else if (data.type === 'none') {
        data.members.forEach((item) => {
          curr.push({
            key: item.key, // 可以根据实际更改对应的key
            name: item.title,
            call_status: item.call_status,
            superUuid: item.superUuid,
            dept: item.dept,
            number: item.number,
            peer_number: item.peer_number,
            call_msg: item.call_msg
          })
        })
      } else if (data.type === 'all') {
        data.members.forEach((item) => {
          curr.push({
            key: item.uuid, // 可以根据实际更改对应的key
            name: item.name,
            call_status: item.call_status,
            superUuid: item.superUuid,
            dept: item.dept,
            number: item.number,
            peer_number: item.peer_number,
            call_msg: item.call_msg
          })
        })
      }
      //   //console.log('refresh_list curr', curr)
      currList.value = curr
    }

    /**
     * @description 对获取到的会议列表数据进行格式化并写入
     * @param data 需要写入的源数据
     * <AUTHOR>
     * @data 2024年12月31日11点23分
     */
    function refresh_meet_list (data) {
      console.log(data)
      // 对数组进行清空，清空后再进行按条加入
      currMeetList.value = []

      // 遍历列表数据
      data.data.forEach((item, index) => {
        console.log(item)
        currMeetList.value.push({
          key: index, // 表格索引
          number: item.meet_number, // 会议号码
          description: item.meet_topic, // 会议室描述
          member: item.member, // 会议成员
          name: item.title, // 会议名称
          master: item.member[0].name, // 会议主持人
          people_number: item.membercountlist // 会议室当前人员数量
        })
      })
    }

    /**
     * @description 对获取到的广播列表数据进行格式化并写入
     * @param data 需要写入的源数据
     * <AUTHOR>
     * @data 2024年12月31日11点23分
     */
    function refresh_brodcast_list (data) {
      console.log(data)
      // 对数组进行清空，清空后再进行按条加入
      currBoradcastList.value = []

      // 遍历列表数据
      data.data.forEach((item, index) => {
        console.log(item)
        currBoradcastList.value.push({
          key: index, // 表格索引
          number: item.meet_number, // 会议号码
          description: item.meet_topic, // 会议室描述
          member: item.member, // 会议成员
          name: item.title, // 会议名称
          master: item.member[0].name, // 会议主持人
          people_number: item.membercountlist // 会议室当前人员数量
        })
      })
    }

    const getListByKey1 = (key) => {
      //   //console.log('getListByKey1', key)
      return searchTreeData(tmpTreeData.value, key)
    }

    function searchTreeData (data, key) {
      let arr = []
      function tree (innerData) {
        innerData.forEach((item) => {
          if (item.key === key) {
            arr = item.children
            // //console.log('key find: ', arr)
          } else {
            tree(item.children)
          }
        })
        // return arr
      }
      tree(data)
      return arr
    }

    const refreshTreeByKey = (key) => {
      let find_member = false
      let find_dept = false
      let arr = {}
      arr.type = ''
      arr.members = []
      if (key === tmpTreeData.value[0].key) {
        find_dept = true
        getAllUser()
        arr = arrall
      }
      if (find_dept) return arr
      const data = tmpTreeData.value[0].children
      //   //console.log('refreshTreeByKey data:', data)
      function tree (innerData) {
        innerData.forEach((item) => {
          if (find_member) return
          if (key === item.key) {
            if (item.type === 'member') {
              arr.type = item.type
              arr.data = item
              find_member = true
            }
          }
          // if ((key === item.key) && (item.type === 'department')) {
          //   arr.type = item.type
          //   find_dept = true
          // }
          if (key === item.key) {
            if (item.type === 'department') {
              arr.type = item.type
              find_dept = true
            }
            if (item.title === '无组织成员') {
              arr.type = 'none'
            }
          }

          if (find_dept === true) {
            arr.members = arr.members.concat(item.children)
            // tree(item.children)
          }
          if (item.children.length > 0) {
            tree(item.children)
          }
        })
        return arr
      }
      return tree(data)
    }

    const refreshMeetTreeByKey = (key) => {
      const meetTree = tmpMeetTreeData.value[0]
      console.log('meetTree:', meetTree)
      let ret = {}
      if (key === 0) {
        ret = meetTree.children
        meetNumber = meetTree.children.length
      } else {
        meetTree.children.forEach((item, index, arr) => {
          if (key === item.key) {
            ret = item
          }
        })
      }

      return ret
    }

    const refreshHost = () => {
      if (currHostUser.value.name !== null) {
        currList.value.forEach((item, index, arr) => {
          if (item.name === currHostUser.value.name) {
            currHostUser.value = item
            // //console.log('refreshHost')
          }
        })
      }
    }

    const operation = (data) => {
      //   //console.log('operation', data)
    }

    let tmpTreeData2 = []
    const fsQuerySuccess1 = (data) => {
      //   //console.log('fsQuerySuccess1: ', data)
      convertoLeftTree(data.data)
    }
    function convertoLeftTree (data) {
      const my_treeData = {}
      my_treeData.children = []
      tmpTreeData2 = toLeftTreeData(data.children)
      //   //console.log('tmpTreeData2', tmpTreeData2)
      const obj = {}
      obj.title = data.name
      obj.key = data.uuid
      obj.type = data.type
      obj.children = []
      tmpTreeData2.forEach((item) => {
        obj.children.push(item)
      })
      tmpTreeData.value = []
      tmpTreeData.value.push(obj)
      //   //console.log('tmpTreeData', tmpTreeData)
      if (curr_selectkey === '') {
        curr_selectkey = obj.key
      }
      const ret_data = refreshTreeByKey(curr_selectkey)
      refresh_list(ret_data)
      refreshHost()
    }

    function updateBackEndData (data) {
      //   //console.log('convertoBackEndData', data)
    }

    function toLeftTreeData (data) {
      function tree (innerData) {
        const arr = []
        innerData.forEach((item) => {
          arr.push({
            key: item.uuid, // 可以根据实际更改对应的key
            title: item.name,
            call_status: item.type === 'department' ? '' : item.call_status,
            superUuid: item.superUuid,
            dept: item.department,
            type: item.type,
            number: item.number,
            call_msg: item.call_msg,
            peer_number: item.peer_number,
            // children: item.departments.length && tree(item.departments)
            children: item.children.length ? tree(item.children) : []
          })
        })
        return arr
      }
      return tree(data)
    }

    const switch_show = () => {
      const info = {}
      if (!ifShow.value.leftTree) {
        ifShow.value.userList = !ifShow.value.userList
        ifShow.value.userCard = !ifShow.value.userCard
        ifShow.value.meetList = false
        ifShow.value.meetCard = false
        info.event = '切换用户列表'
      } else {
        ifShow.value.meetList = !ifShow.value.meetList
        ifShow.value.meetCard = !ifShow.value.meetCard
        ifShow.value.userList = false
        ifShow.value.userCard = false
        info.event = '切换会议列表'
        console.log('switch_show, meetUserOptions:', meetUserOptions)
      }
      addInfo(info)
      getAllUser()
    }

    const show_card = () => {
      const card_box = document.getElementById('test_div')
      const col = document.createElement('a-card')
      col.setAttribute('title', 'asdfasdfadfasdf')
      col.innerText = '郭磊'
      card_box.appendChild(col)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name getLeftTree 刷新左侧树形组织图操作。                                              ***
     *  @param data 无                                                                       ***
     *                                                                                       ***
     *@description 根据ifShow.leftTree刷新左侧人员或会议树形组织图                              ***
        【已存在的已知问题】：无              【可能存在的问题】：无                             ***
     *@description 【是否验证：已经验证         编写人：马三元            日期：2024年9月4日】   ***
     *                                                                                       ***
     * ***************************************************************************************
     *****************************************************************************************/

    const getLeftTree = () => {
      if (!ifShow.value.leftTree) {
        getLeftUserTree()
      }
    }

    const getLeftUserTree = () => {
      // tmpTreeData.value = []
      fsAction.user_query('', fsQuerySuccess1, callbackError, null)
    }

    const getAllUser = (data) => {
      fsAction.alluser_query('', fsGetAllUserSuccess, callbackError, null)
    }

    const fsGetAllUserSuccess = (data) => {
      const curr = []
      currAllUser = []
      //   //console.log('in fsGetAllUserSuccess data:' + JSON.stringify(data))
      data.data.members.forEach((item, index, arr) => {
        curr.push({
          name: item.name,
          number: item.number,
          call_status: item.call_status,
          user_status_code: item.user_status_code,
          peer_number: item.peer_number,
          dept: item.department,
          call_msg: item.call_msg,
          uuid: item.uuid,
          superUuid: item.superUuid
        })
      })
      currAllUser = curr
      currList.value = curr

      // 创建变量临时获取session_config,目的：获取设定的坐席号码
      let session_config = null
      session_config = JSON.parse(sessionStorage.getItem('mesh_Config')) // 将json字符串转为json对象

      // 或者无法获取到session配置信息的时候不设置坐席。
      if (session_config !== null || session_config !== undefined) {
        // 当hostNumber不为空的时候（证明已经设置过坐席用户，不需要再自动设置）
        if (hostNumber == null) {
          hostNumber = session_config.host_number // 获取坐席号码

          currAllUser.forEach((item) => {
            if (item.number === hostNumber) {
              // 将找到的坐用户设置为坐席
              sethost(item)
            }
          })
        }
      }

      arrall.type = 'all'
      arrall.members = curr
      hisUserOptions.value = []
      addMemberUserOption.value = []
      currAllUser.forEach((item, index, arr) => {
        hisUserOptions.value.push({
          value: item.name + '(' + item.number + ')',
          lable: item.name + '(' + item.number + ')',
          number: item.number
        })
        if ((item.call_status === '已注册') || (item.call_status === '成员：离开会议')) {
          item.callerIdNumber = item.number
          // item.realm = item.call_msg.realm
          addMemberUserOption.value.push({
            value: item.name,
            lable: item.name,
            data: item
          })
        }
      })
      if (!ifShow.value.leftTree) {
        const box = document.getElementById('button_col1_person')
        box.style.color = 'rgb(122, 242, 170)'
      }
      console.log('addMemberUserOption:', addMemberUserOption.value)
    }

    const callbackError = (data) => {
      //   //console.log(data)
    }

    const userManage = (data) => {
      fsAction.dept_query('', fsDeptQuerySuccess, callbackError, null)
      clear_column2()
      setTimeout(() => {
        ifShow.value.userManage = true
        ifShow.value.userList = false
        userManageQueue.value = []
      }, 500)
    }

    // 后台交互部门人员数据
    const deptUser_operate = (data) => {
      fsAction.deptUser_operate(data, fsDeptUserOperateSuccess, fsDeptUserOperateCallbackError, null)
    }

    const fsDeptUserOperateSuccess = (data) => {
      //   //console.log(data)
      message.success(data.data)
    }

    const fsDeptUserOperateCallbackError = (data) => {
      //   //console.log(data)
      message.error(data.data)
    }

    const clearWarningClick = (data) => {
      //   //console.log('clearWarningClick' + data)
      warningTemp = []
      warningInfo.value = []
      infoList.value = []
    }

    // 人员列表卡片呼叫按钮
    const dialClick = () => {
      // 如果没有设置坐席
      if (hostUser === null) {
        message.error('请设置坐席')
        return 0
      }
      // 如果没有选择对象
      //   if (selectedMember.value.length === 0) {
      //     message.error('请选择拨号对象')
      //     return 0
      //   }

      if (selectUserListForCard.length === 0) {
        message.error('请选择拨号对象')
      }

      // 如果选择多个拨号用户
      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个拨号用户')
        return 0
      }
      dial(selectUserListForCard)
    }

    // 用户卡片列表代答按钮点击事件
    const answeringClick = () => {
      if (selectUserListForCard.length === 0 || selectUserListForCard === null || selectUserListForCard === undefined) {
        message.error('请选择代答对象')
        return 0
      }

      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个代答监听用户')
        return 0
      }

      answering(selectUserListForCard)
    }

    // 用户卡片列表挂断按钮点击事件
    const dialOffClick = () => {
      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个挂断用户')
        return 0
      }

      dial_off(selectUserListForCard)
    }

    // 用户卡片监听按钮点击事件
    const dialListenClick = () => {
      if (selectUserListForCard.length === 0 || selectUserListForCard === null || selectUserListForCard === undefined) {
        message.error('请选择监听对象')
        return 0
      }

      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个监听用户')
        return 0
      }
      // 当选择的用户处于通话状态的时候才可以被监听
      listen_dial(selectUserListForCard)
    }

    //  用户卡片强插按钮点击事件

    const dialInsertClick = () => {
      if (selectUserListForCard.length === 0 || selectUserListForCard === null || selectUserListForCard === undefined) {
        message.error('请选择强插对象')
        return 0
      }

      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个用户')
        return 0
      }

      // 当选择的用户处于通话状态的时候才可以被强插
      if (selectUserListForCard.user_status_code === 3 || selectUserListForCard.user_status_code === 2) {
        insert_dial(selectUserListForCard)
      } else {
        insert_dial(selectUserListForCard)
        // message.error('选择的用户，没有在通话中。')
        // return 0
      }
    }

    // 用户卡片强拆按钮点击事件

    const dialSpitClick = () => {
      if (selectUserListForCard.length === 0 || selectUserListForCard === null || selectUserListForCard === undefined) {
        message.error('请选择强拆对象')
        return 0
      }
      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个用户')
        return 0
      }
      // 当选择的用户处于通话状态的时候才可以被强插
      if (selectUserListForCard.user_status_code === 3 || selectUserListForCard.user_status_code === 2) {
        split_dial(selectUserListForCard)
      } else {
        split_dial(selectUserListForCard)
        // message.error('选择的用户，没有在通话中。')
        return 0
      }
    }

    const dialTransClick = () => {
      if (selectUserListForCard.length === 0 || selectUserListForCard === null || selectUserListForCard === undefined) {
        message.error('请选转接对象')
        return 0
      }
      if (selectUserListForCard.length > 1) {
        message.error('只能选择一个用户')
        return 0
      }
      // 如果坐席没有通话中则抛出错误提示
      trans_dial(selectUserListForCard)
    }
    /**
     * @description 呼叫方法，呼叫指定用户（data）
     * @param data 需要呼叫的用户数组
     */
    const dial = (data) => {
      // 判断是否在呼叫自己
      if (hostUser.name === data.name) {
        message.error('不能呼叫自己')
        // //console.log('不能拨打自己，返回重新拨打！')
        return
      }
      const sendData = []
      sendData.push(hostUser)
      sendData.push(data)
      //   //console.log('in dial sendData' + JSON.stringify(sendData))
      fsAction.dial(sendData, fsDialSuccess, fsDialcallbackError, null)
    }

    const answering = (data) => {
      const sendData = []
      sendData.push(hostUser)
      sendData.push(data)
      fsAction.intercept_answer(sendData, fsAnsweringSuccess, fsAnsweringcallbackError, null)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name dial_up_qeue 呼叫队列中的接听操作。                                              ***
     * @param data 包含选择的用户数据                                                        ***
     *                                                                                      ***
     *@description 当按下行后的接听按钮的时候会向后端发送一个包含坐席和待接听用户的数据           ***
        【已存在的已知问题】：无              【可能存在的问题】：整个data向后端返回，浪费资源    ***
     *@description 【是否验证：已经验证         编写人：孟家宝            日期：2024年9月2日】  ***
     *                                                                                      ***
     * ***************************************************************************************
     *****************************************************************************************/
    const dial_up_queue = (data) => {
      const sendData = []
      sendData.push(hostUser)
      sendData.push(data)
      fsAction.dial_up_queue(sendData, dialUpQueueSuccess, fsDialcallbackError, null)
    }

    const dialUpQueueSuccess = () => {
    }

    const multiAnswerClick = () => {
      console.log('in multiAnswerClick, holdMeet.value', holdMeet.value)
      const sendData = []
      sendData.push(hostUser)
      // 从呼叫队列中取出群答人员
      queueList.value.forEach((item, index, arr) => {
        sendData.push(item)
      })

      fsAction.group_answer_queue(sendData, fsStartMultiAnswerSuccess, fsStartMultiAnswercallbackError, null)
      clear_column1()
      clear_column2()
      setTimeout(() => {
        message.success('正在下发群答...')
      }, 400)
    }

    const fsStartMultiAnswerSuccess = (data) => {
      // //console.log('fsStartMeetingSuccess', data)
      message.success('群答创建成功')
      instantRefresh()
    }

    const fsStartMultiAnswercallbackError = (error) => {
      // //console.log('fsStartMeetingcallbackError1:', error)
      const currfunc = 'start_meeting'
      checkError(error, currfunc)
    }

    const fsDialSuccess = (data) => {
      //   //console.log('fsDialSuccess' + JSON.stringify(data))
      instantRefresh()
      const info = {}
      info.event = '拨打电话'
      addInfo(info)
    }

    const fsAnsweringSuccess = (data) => {
      message.success('代答成功')
    }

    const fsDialcallbackError = (data) => {
      //   //console.log('fsDialcallbackError:' + data)
    }

    const fsAnsweringcallbackError = (data) => {
      //   //console.log('fsDialcallbackError:' + data)
      message.error('代答失败')
    }

    /**
     * @description 设为坐席的方法
     * @param data 传入数据结构，通过这个结构将坐席解析出来
     */

    const sethost = (data) => {
      //   //console.log('sethost' + JSON.stringify(data))
      hostUser = data
      currHostUser.value = data
      hostUserForComponents.value = data
      const info = {}
      info.event = '主叫用户设定为:' + data.name
      addInfo(info)
    }

    const dial_answer = (data) => {
      //   //console.log('in dial_answer data:' + JSON.stringify(data))
      const info = {}
      info.event = '接听电话'
      addInfo(info)
    }

    const dial_off = (data) => {
      //   //console.log('in dial_off data:' + JSON.stringify(data))
      const sendData = []
      sendData.push(data)
      //   //console.log('in dial sendData' + JSON.stringify(sendData))
      fsAction.dial_off(sendData, fsDialOffSuccess, callbackError, null)
      const info = {}
      info.event = '挂断电话:' + data.peer_number
      addInfo(info)
    }

    const fsDialOffSuccess = (data) => {
      //   //console.log('in fsDialOffSuccess data:' + data)
      instantRefresh()
    }

    const handleMenuClick = e => {
      //   //console.log('handleMenuClick', e)
      curr_key = e.key
    }

    const handleMeetMenuClick = e => {
      currMeet_key = e.key
    }

    const handleHisMeetMenuClick = e => {
      currHisMeet_key = e.key
    }

    const handleQueueMenuClick = e => {
      curr_key = e.key
    }

    const listen_dial = (data) => {
      //   //console.log('in listen_dial' + JSON.stringify(data))
      const if_inCall = data.call_status.includes('通话中')
      if (if_inCall === true) {
        const sendData = []
        sendData.push(hostUser)
        sendData.push(data)
        // //console.log('in listen_dial' + JSON.stringify(sendData))
        fsAction.listen_dial(sendData, fsListenDialSuccess, fsDialcallbackError, null)
      }
    }

    const fsListenDialSuccess = (data) => {
      //   //console.log('fsListenDialSuccess' + JSON.stringify(data))
      instantRefresh()
      const info = {}
      info.event = '监听电话'
      addInfo(info)
    }

    const trans_dial = (data) => {
      //   //console.log('in trans_dial' + JSON.stringify(data))
      const if_inCall = data.call_status.includes('通话中')
      if (if_inCall === false) {
        const sendData = []
        sendData.push(data)
        sendData.push(hostUser)
        console.log(data)
        // //console.log('in trans_dial' + JSON.stringify(sendData))
        fsAction.trans_dial(sendData, fsTransDialSuccess(data), fsDialcallbackError, null)
      } else {
        message.error('转接失败！您选择的用户正在通话中。')
      }
    }

    const fsTransDialSuccess = (data) => {
      //   //console.log('fsTransDialSuccess' + JSON.stringify(data))
      instantRefresh()
      const info = {}
      info.event = '转接电话给：' + data.name
      addInfo(info)
    }

    const insert_dial = (data) => {
      //   //console.log('in insert_dial' + JSON.stringify(data))
      const if_inCall = data.call_status.includes('通话中')
      if (if_inCall === true) {
        const sendData = []
        sendData.push(hostUser)
        sendData.push(data)
        // //console.log('in insert_dial' + JSON.stringify(sendData))
        fsAction.insert_dial(sendData, fsInsertDialSuccess, fsDialcallbackError, null)
      } else {
        // //console.log(data.name + '未在通话中')
      }
    }

    /********************************************************************************************************************************
     * ******************************************************************************************************************************
     * @description 接收人员卡片列表子组件向父组件传递的已经选择的人员列表的参数，可以根据这个已经选择的人员列表执行一系列方法
     * @param selectUser 人员卡片列表的子组件向父组件传递的已经选择人员的参数
     * @description 【可能存在的问题： 子组件传参的时候，无法声明emit,在setup()参数列表中进行传递，但仍未声明且可使用，后期需要充分测试。】
     *
     * @description 【是否验证：已经验证，但需要再次详细验证         编写人：孟家宝            日期：2024年9月26日】
     * ******************************************************************************************************************************
     ********************************************************************************************************************************/

    const userListUpdate = (selectUser) => {
      // 将子组件传递过来的参数进行父组件本地化
      if (selectUser.value[0] !== undefined) {
        selectUserListForCard = selectUser.value[0]
      } else {
        selectUserListForCard = null
      }
    }

    const fsInsertDialSuccess = (data) => {
      //   //console.log('fsInsertDialSuccess' + JSON.stringify(data))
      instantRefresh()
      const info = {}
      info.event = '强插电话'
      addInfo(info)
    }

    const split_dial = (data) => {
      //   //console.log('in split_dial' + JSON.stringify(data))
      const if_inCall = data.call_status.includes('通话中')
      if (if_inCall === true) {
        const sendData = []
        sendData.push(hostUser)
        sendData.push(data)
        // //console.log('in split_dial' + JSON.stringify(sendData))
        fsAction.split_dial(sendData, fsSplitDialSuccess, fsDialcallbackError, null)
      } else {
        // //console.log(data.name + '未在通话中')
      }
    }

    const fsSplitDialSuccess = (data) => {
      //   //console.log('fsSplitDialSuccess' + JSON.stringify(data))
      instantRefresh()
      const info = {}
      info.event = '强拆电话'
      addInfo(info)
    }

    const handleMenuClickByKey = (data) => {
      //   //console.log('handleMenuClickByKey data', data)
      pressButton_delay()
      const if_inCall = data.call_status.includes('离线')
      if (if_inCall === true) {
        // //console.log('离线状态，无法操作')
        return
      }
      let ifHostSet = false
      if ((currHostUser.value !== null) || (currHostUser.value !== undefined)) {
        ifHostSet = true
      }
      setTimeout(() => {
        switch (curr_key) {
          case '1': // 拨号
            if (ifHostSet === true) {
              dial(data)
            } else {
              //   //console.log('请先设置主叫！')
            }
            break
          case '3': // 挂断
            dial_off(data)
            break
          case '4': // 监听
            if (ifHostSet === true) {
              listen_dial(data)
            } else {
              // //console.log('请先设置监听者！')
            }
            break
          case '5': // 设为主叫
            sethost(data)
            break
          case '6': // 转接
            if (ifHostSet === true) {
              trans_dial(data)
            } else {
              // //console.log('请先设置坐席')
            }
            break
          case '7': // 代答
            if (ifHostSet === true) {
              intercept_answer(data)
            } else {
              // //console.log('请先设置坐席')
            }
            break
          case '8': // 强拆
            if (ifHostSet === true) {
              split_dial(data)
            } else {
              // //console.log('请先设置坐席')
            }
            break
          case '9': // 强拆
            if (ifHostSet === true) {
              insert_dial(data)
            } else {
              console.log('请先设置坐席')
            }
            break
          case '10': // 强插
            if (ifHostSet === true) {
              insert_dial(data)
            } else {
              // //console.log('请先设置坐席')
            }
            break
          default:
            break
        }
      }, 500)
      instantRefresh()
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name handleMeetMenuClickByKey 会议列表按钮操作                                              ***
     *  @param data 包含选择的用户数据                                                        ***
     *                                                                                      ***
     *@description 当按下执行对应的操作           ***
        【已存在的已知问题】：无              【可能存在的问题】：按钮操作索引currMeet_key为全局变量    ***
     *@description 【是否验证：已经验证         编写人：马三元            日期：2024年9月4日】  ***
     *                                                                                      ***
     * ***************************************************************************************
     *****************************************************************************************/

    const handleMeetMenuClickByKey = (data) => {
      pressButton_delay()
      setTimeout(() => {
        switch (currMeet_key) {
          case '1': // 详细
            break
          case '2': // 结束
            end_meeting(data)
            break
          default:
            break
        }
      }, 500)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name handleHisMeetMenuClickByKey 历史会议列表按钮操作
     *  @param data 包含选择的用户数据
     *@description 当按下执行对应的操作
        【已存在的已知问题】：无              【可能存在的问题】：按钮操作索引currHisMeet_key为全局变量
     *@description 【是否验证：已经验证         编写人：马三元            日期：2024年9月19日】
     *
     * ***************************************************************************************
     *****************************************************************************************/

    const handleHisMeetMenuClickByKey = (data) => {
      pressButton_delay()
      setTimeout(() => {
        switch (currHisMeet_key) {
          case '1': // 详细
            break
          case '2': // 重新召开
            end_meeting(data)
            break
          default:
            break
        }
      }, 500)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name createMeeting 会议界面创建会议传入的创建会议的事件
     * @param data 包含创建会议的全部信息
        【已存在的已知问题】：无              【可能存在的问题】：
     * 【是否验证：待验证         编写人：孟家宝            日期：2025年1月6日】
     * ***************************************************************************************
     *****************************************************************************************/
    const createMeeting = (data) => {
      createMeetingSendData = []

      // 获取当前时间为会议创建时间
      let meetCreateTime = ''
      meetCreateTime = new Date().getTime()

      createMeetingSendData.push({
        meet_master_id: data.masterId, // 主持人ID
        meet_title: data.meetName, // 会议名称
        meet_method: 0, // 会议方式，0 及时会议，1 预约会议
        meet_topic: data.meetTheme, // 会议主题
        meet_number: data.meetNumner, // 会议号码
        meet_time: meetCreateTime, // 会议创建时间
        meeting_member: data.meetMembers // 会议成员
      })

      // 发送创建会议请求
      fsAction.start_meeting(createMeetingSendData, fsStartMeetingSuccess, fsStartMeetingcallbackError, null)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name createBrocast 创建广播传入的创建事件
     * @param data 包含创建广播的全部信息
        【已存在的已知问题】：无              【可能存在的问题】：
     * 【是否验证：待验证         编写人：孟家宝            日期：2025年1月6日】
     * ***************************************************************************************
     *****************************************************************************************/
    const createBrocast = (data) => {
      createBrocastSendData = []

      // 获取当前时间为会议创建时间
      let brocastCreateTime = ''
      brocastCreateTime = new Date().getTime()

      createBrocastSendData.push({
        meet_master_id: data.masterId, // 主持人ID
        meet_title: data.meetName, // 会议名称
        meet_method: 0, // 会议方式，0 及时会议，1 预约会议
        meet_topic: data.meetTheme, // 会议主题
        meet_number: brocastCreateTime.toString(), // 会议号码
        meet_time: brocastCreateTime, // 会议创建时间
        meeting_member: data.meetMembers, // 会议成员
        fileplayaction: data.fileplayaction,
        curr_file: data.path
      })

      // 发送创建会议请求
      fsAction.conference_broadcast_direct(createBrocastSendData, fsStartBrocastSuccess, fsStartMeetingcallbackError, null)
    }

    /**
     * @name stopMeeting 停止会议的方法
     * @param data 子组件传给父组件的需要结束会议的会议号
     * @description 当子组件调用这个方法的时候会结束对应会议号的会议
     */
    const stopMeeting = (data) => {
      let stopMeetingNumber = '' // 需要结束的会议号
      stopMeetingNumber = [{
        meet_number: data
      }]
      fsAction.conference_stop(stopMeetingNumber, fsEndMeetingSuccess, fsEndMeetingCallbackError, null)
    }

    /**
     * @name stopMeeting 停止广播的方法
     * @param data 子组件传给父组件的需要结束会议的会议号
     * @description 当子组件调用这个方法的时候会结束对应会议号的会议
     */
    const stopBrocast = (data) => {
      let stopMeetingNumber = '' // 需要结束的会议号
      stopMeetingNumber = [{
        meet_number: data
      }]
      fsAction.conference_stop(stopMeetingNumber, fsEndBrocastSuccess, fsEndMeetingCallbackError, null)
    }

    /******************************************************************************************
     * ****************************************************************************************
     * @name editMeeting 会议列表界面修改某一条会议的表单数据
     * @param data 包含创建会议的全部信息，点击按钮后会获取该会议的所有信息
     * @description 修改会议信息页面可以对页面的会议名称、会议主题进行修改，目前暂定会议号码不能更改
     * @description 此功能暂时不开发
        【已存在的已知问题】：无              【可能存在的问题】：
     * 【是否验证：待验证         编写人：孟家宝            日期：2025年1月6日】
     * ***************************************************************************************
     *****************************************************************************************/
    /* const editMeeting = (data) => {
      createMeetingSendData = []

      // 获取当前时间为会议创建时间
      let meetCreateTime = ''
      meetCreateTime = new Date().getTime()

      createMeetingSendData.push({
        meet_title: data.meetName, // 会议名称
        meet_method: 0, // 会议方式，0 及时会议，1 预约会议
        meet_topic: data.meetTheme, // 会议主题
        meet_number: data.meetNumner, // 会议号码
        meet_time: meetCreateTime, // 会议创建时间
        meeting_member: data.meetMembers // 会议成员
      })

      // 发送创建会议请求
      fsAction.start_meeting(createMeetingSendData, fsStartMeetingSuccess, fsStartMeetingcallbackError, null)
    } */

    const end_meeting = (data) => {
      console.log('end_meeting:', data)
      const sendData = []
      sendData.push(data)
      fsAction.conference_stop(sendData, fsEndMeetingSuccess, fsEndMeetingCallbackError, null)
    }

    const fsEndMeetingSuccess = (data) => {
      console.log('fsEndMeetingSuccess:', data)
      nociceMeet = ref({
        meet_title: '',
        meet_topic: '',
        meet_number: ''
      })
    }

    const fsEndBrocastSuccess = (data) => {
      console.log('fsEndMeetingSuccess:', data)
      nociceMeet = ref({
        meet_title: '',
        meet_topic: '',
        meet_number: ''
      })
      // 获取广播列表
      getBroadcast()
    }

    const fsEndMeetingCallbackError = (data) => {
      console.log('fsEndMeetingCallbackError:', data)
    }

    /**
     * @name handeQueueMenuClickByKey 点击接听的点击事件
     * @param data 点击的数据
     */
    const handleQueueMenuClickByKey = (data) => {
      pressQueueButton_delay()
      const if_inCall = data.call_status.includes('离线')
      if (if_inCall === true) {
        // //console.log('离线状态，无法操作')
        return
      }
      let ifHostSet = false
      if ((currHostUser.value !== null) || (currHostUser.value !== undefined)) {
        ifHostSet = true
      }

      console.log(data)
      dial_up_queue(data)
      setTimeout(() => {
        // //console.log('handleQueueMenuClickByKey curr_key', curr_key)
        switch (curr_key) {
          case '1': // 拨号
            break
          case '2': // 接听
            dial_answer(data)
            break
          case '3': // 挂断
            dial_off(data)
            break
          case '4': // 监听
            break
          case '5': // 设为主叫
            break
          case '6': // 转接
            if (ifHostSet === true) {
              trans_dial(data)
            } else {
              // //console.log('请先设置主叫')
            }
            break
          case '7': // 代答
            intercept_answer(data)
            break
          default:
            break
        }
      }, 500)
    }

    const intercept_answer = (data) => {
      console.log('intercept_answer', data)
      if (!data.call_status.includes('被叫：振铃中')) {
        console.log('代答未在振铃中！')
        message.error('代答未在振铃中！')
        return
      }
      const sendData = []
      sendData.push(hostUser)
      sendData.push(data)
      console.log('in intercept_answer sendData' + JSON.stringify(sendData))
      fsAction.intercept_answer(sendData, fsInterceptAnswerSuccess, fsInterceptAnswercallbackError, null)
    }

    const fsInterceptAnswerSuccess = (data) => {
      console.log('fsInterceptAnswerSuccess:', data)
    }

    const fsInterceptAnswercallbackError = (data) => {
      console.log('fsInterceptAnswercallbackError:', data)
    }

    const startRefresh = () => {
      // console.log('重新启动定时器' + ' repeat_tree:' + repeat_tree + ' repeat_queue:' + repeat_queue)
      //   refresh_tree()
      //   getAllUser()
      //   refresh_queue()
    }

    const stopRefresh = () => {
      // //console.log('停止定时器' + ' repeat_tree:' + repeat_tree + ' repeat_queue:' + repeat_queue)
      //   clearInterval(repeat_tree)
      //   clearInterval(repeat_queue)
    }

    /**
     * function_name: instantRefresh
     * description: 执行业务后，立刻刷新数据，只执行一次。
     */
    const instantRefresh = () => {
      // //console.log('instantRefresh')
      setTimeout(() => {
        // 人员
        getLeftUserTree()

        // // 会议树
        // getLeftMeetTree(0)

        // 当前坐席
        refreshHost()

        // 获取呼叫队列
        fsAction.getDialQueue('', fsGetDialQueueSuccess, callbackError, null)
      }, 500)
    }

    /**
      * function_name: instantRefresh
      * description: 定时十秒钟刷新一次组织架构的数据。
     */
    // const refresh_tree = () => {
    //   repeat_tree = setInterval(() => {
    //     // 人员树
    //     getLeftTree()

    //     // 会议树
    //     getLeftMeetTree()
    //   }, 10000)
    // }

    /**
     * 呼叫队列定时器，每10s钟刷新一次
     */
    // const refresh_queue = () => {
    //   repeat_queue = setInterval(() => {
    //     fsAction.getDialQueue('', fsGetDialQueueSuccess, callbackError, null)
    //   }, 10000)
    // }

    /**
     * @name fsGetDialQueueSuccess
     * @description 呼叫队列回调函数
     */
    const fsGetDialQueueSuccess = (data) => {
      // //console.log('in fsGetDialQueueSuccess:' + JSON.stringify(data))
      // 呼叫队列，列表
      queueList.value = []

      // 下拉选项
      queueOptions.value = []

      // 所有的
      currAllQueue = data.data.allQueue
      currAllQueue.forEach((item0, index0, arr0) => {
        console.info('callQueue:' + item0.callQueue)
        queueOptions.value.push({
          value: item0.callQueue,
          lable: item0.callQueue + '-' + index0
        })
        console.info(item0)
        const obj_member = item0.members
        if (obj_member != null) {
          obj_member.forEach((item, index, arr) => {
            queueList.value.push({
              name: item.name,
              number: item.number,
              call_status: item.call_status,
              peer_number: item.peer_number,
              dept: item.department,
              call_msg: item.call_msg,
              key: item.uuid
            })
          })
        }
      })

      queueOptions.value.push({
        value: '全部队列',
        lable: 'all'
      })
      if_server_response = true
    }

    const handleQueueChange = value => {
      const select = value
      // //console.log(`handleQueueChange selected ${select.value}`)
      queueList.value = []
      if (select.value === 'all') {
        currAllQueue.forEach((item0, index0, arr0) => {
          const obj_member = item0.members
          if (obj_member != null) {
            obj_member.forEach((item, index, arr) => {
              queueList.value.push(item)
            })
          }
        })
      } else {
        currAllQueue.forEach((item, index, arr) => {
          if (item.callQueue === select.value) {
            queueList.value = item.members
          }
        })
      }
    }

    const onSearchUser = (data) => {
      const curr = []
      // //console.log('search_user:', search_user.value)
      // //console.log('currList.value.length:', currList.value.length)
      if (search_user.value === '') {
        // //console.log('search_user.value === \'\', reload!')
        currList.value = currAllUser
      }
      if (currList.value.length === 0) {
        // //console.log('currList.value.length === 0, reload!')
        currList.value = currAllUser
      }
      currList.value.forEach((item, index, arr) => {
        let redo = false
        if ((item.name.includes(search_user.value)) || (item.number.includes(search_user.value)) ||
            (item.call_status.includes(search_user.value)) || (item.dept.includes(search_user.value))) {
          if (curr.length > 0) {
            curr.forEach((item0, index0, arr0) => {
              if (item0.number === item.number) {
                redo = true
                // //console.log('in item0', index0, redo)
              }
            })
          }
          if (redo !== true) {
            curr.push(item)
            // //console.log('in item', index, JSON.stringify(item))
          }
        }
      })
      currList.value = curr
      stopRefresh()
    }

    const infoClick = (data) => {
      messVisible.value = true
      // //console.log('messVisible', messVisible.value)
    }

    const addInfo = (info) => {
      const length = infoList.value.length
      infoList.value.push({
        key: length,
        name: currHostUser.value.name,
        dataTime: utils.formatDate(new Date()),
        event: info.event
      })
      warningTemp = []
      warningInfo.value = []
      for (let i = 0; i < infoList.value.length; i++) {
        warningInfo.value.push([
          infoList.value[i].name,
          infoList.value[i].dataTime,
          infoList.value[i].event
        ])
      }
      //   console.info('warningTemp' + warningTemp)
      //   warningInfo.value = warningTemp
    }

    const deleteInfo = (index) => {
      // //console.log('index:' + index)
      infoList.value.splice(index, 1)
      // 删除后对数组中data的key进行重新排列 下次删除该位置的元素时候就不会发生错误
      for (let i = 0; i < infoList.value.length; i++) {
        infoList.value[i].key = i
      }
      warningTemp = []
      for (let i = 0; i < infoList.value.length; i++) {
        warningTemp.push([
          infoList.value[i].name,
          infoList.value[i].dataTime,
          infoList.value[i].event
        ])
      }
      warningInfo.value = warningTemp
    }

    const warningInfoClick = () => {
      infoClick()
    }

    const pressButton_delay = () => {
      if_userlist_dropdown.value = true
      setTimeout(() => {
        if_userlist_dropdown.value = false
      }, 300)
    }

    const pressQueueButton_delay = () => {
      if_queuelist_dropdown.value = true
      setTimeout(() => {
        if_queuelist_dropdown.value = false
      }, 300)
    }

    // const ShowHis = () => {
    //   // //console.log('in ShowHis')
    //   clear_column2()
    //   getAllUser()
    //   ifShow.value.hisCard = true
    // }

    // const ShowHisMeeting = () => {
    //   fsAction.his_conference_list([], fsQueryHisMeetingSuccess, fsQueryHisMeetingcallbackError, null)
    // }

    /**
     * @name fsQueryHisMeetingSuccess 历史会议请求成功回调函数
     * @param data  历史会议请求成功后的响应数据
     * @description 【已知存在的问题：无】
     * @description 【可能存在的问题：可能后面添加的字段无法匹配】
     */
    const fsQueryHisMeetingSuccess = (data) => {
      console.log('fsQueryHisMeetingSuccess:' + JSON.stringify(data))
      // const curr = {}

      // 对获取到的数据进行判断,如果有数据则进行操作
      if (data.length !== 0) {
        console.log('************************')
        his_meeting_dataSource.value = [] // 清空之前的历史列表，避免数据污染

        data.data.forEach((element, index) => {
          his_meeting_dataSource.value.push({
            key: index, // 索引从0开始
            number: element.meet_number, // 会议号码
            description: element.meet_topic, // 会议主题
            name: element.title, // 会议名称
            master: element.meet_master, // 会议主持人
            people_number: element.member.length, // 参会人数
            member: element.member, // 参会人员
            start: element.meet_time, // 会议开始时间
            end: element.end_time // 会议结束时间
          })
        })
      }
    }

    const fsQueryHisMeetingcallbackError = (data) => {
      console.log('fsQueryHisMeetingcallbackError:' + data)
    }

    const handleHisChange = value => {
      // //console.log(`selected ${value}`)
      // //console.log('selectedHisUser:', selectedHisUser.value)
      // //console.log('hisUserOptions:', hisUserOptions.value)

      // hisUserOptions.value.forEach((item, index, arr) => {
      //   if(item.name === )

      // })
    }
    const handleHisBlur = () => {
      // //console.log('blur')
    }
    const handleHisFocus = () => {
      // //console.log('focus')
    }
    const hisFilterOption = (input, option) => {
      return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    const queryHis = value => {
      // //console.log('queryHis: ', value)

      queryHisUser = {}
      queryHisUser.userNmuber = selectedHisUser.value
      if (rangpickdate.value !== undefined) {
        const start_time = ref(rangpickdate).value[0].format('YYYY-MM-DD HH:mm:ss')
        const end_time = ref(rangpickdate).value[1].format('YYYY-MM-DD HH:mm:ss')
        // //console.log('start_time: ' + start_time + '  end_time: ' + end_time)
        queryHisUser.start_time = start_time
        queryHisUser.end_time = end_time
      } else {
        notification.error({
          message: ` 未输入时间范围!${value}`,
          description: ''
        })
        return
      }
      // //console.log('queryHisUser', queryHisUser)
      startWaiting()
      ifShow.value.hisCard = false
      ifShow.value.userList = true
      ifShow.value.buttonHis = true
    }

    const cancel_queryHis = () => {
      ifShow.value.buttonHis = true
      if (ifShow.value.hisCard === true) ifShow.value.hisCard = false
      ifShow.value.userList = true
    }

    const startWaiting = () => {
      let if_quit = false
      let j = 0
      spinTip = '等待服务器响应......'
      ifSpinning.value = true
      for (let i = 0; i < 20; i++) {
        setTimeout(() => {
          if (if_server_response === true) {
            if_quit = true
          }
          j = 10 - (i + 1) / 2
          spinTip = '等待服务器响应......' + j
          if (if_quit === true) {
            // //console.log('if_quit: ', if_quit)
            ifSpinning.value = false
          }
        }, spinWaitTime)
        if (if_quit === true) {
          // //console.log('return: ')
          return
        }
      }
    }

    const showModal = () => {
      ifShow.value.deptManage = true
      ifShow.value.userList = false
    }

    const onCheckChange = () => {
      if (state.checked_deptOrUser === true) {
        // //console.log('部门管理')
        inputTitle.value.title = '新增部门'
        inputTitle.value.checkTitle = '部门'
        inputTitle.value.type = '部门名称'
        ifShow.value.inputNumber = false
        inputTitle.value.number = ''
      } else {
        // //console.log('人员管理')
        inputTitle.value.title = '新增人员'
        inputTitle.value.checkTitle = '部门'
        inputTitle.value.type = '人员名称'
        ifShow.value.inputNumber = true
        inputTitle.value.number = '人员号码'
      }
    }

    const fsDeptQuerySuccess = (data) => {
      // //console.log('fsDeptQuerySuccess', data)
      convertoTree(data.data)
      // mytreeData.value = deptConvert(data)
    }

    // const deptConvert = (data) => {
    //   //console.log('fsDeptQuerySuccess', data)
    //   // let tmpTree = []
    // }

    const handleOk = () => {
      // //console.log('handleOk userManage_select', userManage_select)
      const select = {}
      userManageQueue.value = []

      if (isUpdate.value) {
        select.operation = 'mod'
        select.uuid = userManage_select.uuid
        select.superUuid = userManage_select.superUuid
        select.name = compName.value
        if (state.checked_deptOrUser === true) {
          select.type = 'department'
        } else {
          select.type = 'member'
          select.number = compNum.value
        }
        Object.assign(parentNode.value.dataRef, { name: compName.value, number: compNum.value })
      } else {
        if (!parentNode.value.name) {
          select.operation = 'add'
          select.uuid = userManage_select.key
          select.superUuid = userManage_select.superUuid
          select.name = compName.value
          select.number = ''
          select.type = 'department'
          mytreeData.value.push({ name: compName.value, number: compNum.value, children: [], key: 0 })
        } else {
          select.operation = 'add'
          select.uuid = null
          select.superUuid = userManage_select.superUuid
          select.name = compName.value
          if (state.checked_deptOrUser === true) {
            select.type = 'department'
          } else {
            select.type = 'member'
            select.number = compNum.value
          }
          parentNode.value.children.push({ name: compName.value, number: compNum.value, children: [], key: Math.random() })
        }
      }
      ifShow.value.deptManage = false
      ifShow.value.userList = false
      // //console.log('End handleOk parentNode:', parentNode.value)
      // //console.log('End handleOk mytreeData:', toRaw(mytreeData.value))
      // //console.log('handleOk select', JSON.stringify(select))
      userManageQueue.value.push(select)
      deptUser_operate(userManageQueue.value)
      updateBackEndData(mytreeData.value)
      setTimeout(() => {
        userManage()
      }, 1000)
    }

    function showNode (nodeData) {
      // //console.log('showNode nodeData:', nodeData)
      // //console.log('End showNode parentNode:', parentNode.value)
    }

    // 增加下级节点
    function addComp (nodeData) {
      ifShow.value.userList = false
      if (!mytreeData.value.length) {
        // //console.log('新增根部门')
        inputTitle.value.title = '新增根部门'
        ifShow.value.checkbox = false
      } else {
        inputTitle.value.title = '新增部门'
        ifShow.value.checkbox = true
      }
      inputTitle.value.checkTitle = '部门'
      inputTitle.value.type = '部门名称'
      ifShow.value.inputNumber = false
      inputTitle.value.number = ''

      isUpdate.value = false
      compName.value = ''
      parentNode.value = nodeData
      userManage_select = {}
      userManage_select.superUuid = parentNode.value.dataRef.key
      state.checked_deptOrUser = true
      showModal()
      // //console.log('End addComp parentNode:', parentNode.value)
      // //console.log(toRaw(mytreeData.value))
    }

    // 修改当前节点
    function slotModify (nodeData) {
      // //console.log('slotModify nodeData:', nodeData)
      isUpdate.value = true
      parentNode.value = nodeData
      compName.value = nodeData.dataRef.name
      if (nodeData.dataRef.type === 'department') {
        state.checked_deptOrUser = true
        inputTitle.value.title = '修改部门'
        inputTitle.value.type = '部门名称'
        ifShow.value.inputNumber = false
        inputTitle.value.number = ''
      } else {
        state.checked_deptOrUser = false
        compNum.value = nodeData.dataRef.number
        inputTitle.value.title = '修改人员'
        inputTitle.value.type = '人员名称'
        ifShow.value.inputNumber = true
        inputTitle.value.number = '人员号码'
      }
      ifShow.value.checkbox = false
      // onCheckChange()
      showModal()
      // //console.log('End slotModify parentNode:', parentNode.value)
      // //console.log(toRaw(mytreeData.value))
      userManage_select = {}
      userManage_select.uuid = nodeData.dataRef.key
      userManage_select.superUuid = nodeData.dataRef.superUuid
      userManage_select.name = nodeData.dataRef.name
      userManage_select.number = nodeData.dataRef.number
    }

    // 删除当前节点
    function slotDelete (nodeData) {
      parentNode.value = nodeData
      // //console.log('End slotDelete parentNode:', parentNode.value)
      // //console.log(toRaw(mytreeData.value))
    }

    // 确认删除当前节点
    function confirmDel () {
      // //console.log('Begin confirmDel parentNode:', parentNode.value)
      // //console.log(toRaw(mytreeData.value))
      Object.assign(parentNode.value.dataRef, null)
      searchOption(parentNode.value.dataRef, mytreeData.value)
      // //console.log('End confirmDel parentNode:', parentNode.value)
      // //console.log(toRaw(mytreeData.value))
      setTimeout(() => {
        userManage()
      }, 1000)
    }

    //  递归查找操作的节点，在父节点的children中删除
    function searchOption (option, arr, obj = {}) {
      // 首先循环arr最外层数据
      for (let s = 0; s < arr.length; s++) {
        // 如果匹配到了arr最外层中的我需要删除的数据
        if (arr[s].key === option.key) {
          // 删除即删除即可
          const select = {}
          select.operation = 'del'
          select.uuid = arr[s].key
          select.superUuid = arr[s].superUuid
          select.name = arr[s].name
          select.number = arr[s].number
          select.type = arr[s].type
          // //console.log('confirmDel select', JSON.stringify(select))
          userManageQueue.value = []
          userManageQueue.value.push(select)
          arr.splice(s, 1)
          deptUser_operate(userManageQueue.value)
          break
        } else if (arr[s].children && arr[s].children.length > 0) {
          // 递归条件
          searchOption(option, arr[s].children, obj)
        } else {
          continue
        }
      }
    }

    let tmpTreeData1 = []

    function convertoTree (data) {
      mytreeData.value = []
      mytreeData.value.children = []
      tmpTreeData1 = toTreeData(data.children)
      // //console.log('tmpTreeData1', tmpTreeData1)
      const obj = {}
      obj.name = data.name
      obj.key = data.uuid
      obj.type = data.type
      obj.level = data.level
      obj.children = []
      tmpTreeData1.forEach((item) => {
        obj.children.push(item)
      })

      mytreeData.value.push(obj)
      // //console.log('mytreeData', mytreeData)
      showLeftTree.value = false
    }

    function toTreeData (data) {
      function tree (innerData) {
        const arr = []
        innerData.forEach((item) => {
          arr.push({
            key: item.uuid, // 可以根据实际更改对应的key
            superUuid: item.superUuid, // 可以根据实际更改对应的key
            name: item.name,
            number: item.number,
            type: item.type,
            // children: item.departments.length && tree(item.departments)
            children: item.children.length ? tree(item.children) : []
          })
        })
        return arr
      }
      return tree(data)
    }

    // 弹框
    const openNotification = (title, msg) => {
      notification.open({
        message: title,
        description: msg,
        onClick: () => {
          // //console.log('Notification Clicked!')
        },
        duration: 3
      })
    }

    // 获取当前树的值
    function getTreeData () {
      const details = toRaw(mytreeData.value)
      // //console.log('toRaw(mytreeData.value)', details)
      openNotification('tree', JSON.stringify(details))
    }

    function quit_userManage () {
      ifShow.value.userManage = false
      ifShow.value.userList = true
      ifShow.value.buttonHis = true
    }

    /**
     *
     * @description 获取会议列表
     */
    const getMeetingList = () => {
      fsAction.conference_list([], fsQueryMeetingSuccess, fsQueryMeetingcallbackError, null)
    }

    /**
     *
     * @description 获取广播列表
     */
    const getBroadcast = () => {
      fsAction.conference_list_broadcast([], fsQueryBoradcastSuccess, fsQueryBoradcastcallbackError, null)
    }

    /**
     * @description 会议列表请求成功回调
     * @param data 会议列表响应的数据，json结构[{key,number,time,topic,member,membercounlist,tittle}...]
     */
    const fsQueryMeetingSuccess = (data) => {
      // console.log('fsQueryMeetingSuccess: ', data)
      // 成功获取到会议列表数据后对数据进行响应写入组件
      refresh_meet_list(data)
    }

    /**
     * @description 广播列表请求成功回调
     * @param data 广播列表响应的数据
     */
    const fsQueryBoradcastSuccess = (data) => {
      // console.log('fsQueryMeetingSuccess: ', data)
      // 成功获取到会议列表数据后对数据进行响应写入组件
      refresh_brodcast_list(data)
    }

    /**
     * @description 会议列表错误回调
     * @param error 错误描述
     */
    const fsQueryMeetingcallbackError = (error) => {
      // //console.log('fsQueryMeetingcallbackError: ', error)
      const currfunc = 'query_meeting'
      checkError(error, currfunc)
    }

    /**
     * @description 会议列表错误回调
     * @param error 错误描述
     */
    const fsQueryBoradcastcallbackError = (error) => {
      // //console.log('fsQueryMeetingcallbackError: ', error)
      checkError(error)
    }

    function containsMultipleStrings (str, ...substrings) {
      return substrings.every(substr => str.includes(substr))
    }

    const checkError = (error, func) => {
      // //console.log('err_code,checkError:', error)
      // //console.log('err_code,func:', func)
      let substr = ['Error', '404']
      //   let err_code = ''
      let result = containsMultipleStrings(JSON.stringify(error), ...substr)
      if (result) {
        // err_code = 404
      }
      substr = ['Error', '400']
      result = containsMultipleStrings(JSON.stringify(error), ...substr)
      if (result) {
        // err_code = 400
      }
      // //console.log('Err_code:', err_code)
      //   message.error('后端接口' + func + '报错:' + err_code)
    }

    const start_meeting = () => {
      // //console.log('start_meeting', meetingMember.value)
      clear_column2()
      ifShow.value.leftTree = false
      ifShow.value.meetingPanel = true
      checkedKeys.value = []
      // fsAction.start_meeting(meetingMember.value, fsStartMeetingSuccess, fsDialcallbackError, null)
    }

    const confirm_meeting = () => {
      if (holdMeet.value.userList.length === 0) {
        alert('请选择用户！')
        return
      }

      holdMeet.value.meet_number = nociceMeet.value.meet_number
      holdMeet.value.meet_topic = nociceMeet.value.meet_topic
      holdMeet.value.meet_title = nociceMeet.value.meet_title
      holdMeet.value.meet_master_id = currHostUser.value.name
      holdMeet.value.meeting_member = meetingMember.value

      // 会议类型
      if (state.checked_date === true) {
        holdMeet.value.meet_method = 1
        if (holdMeet.value.meet_time === '') {
          notification.error({
            message: ` 未选择预约时间！${''}`,
            description: ''
          })
          return
        }
      } else {
        holdMeet.value.meet_method = 0
        holdMeet.value.meet_time = Date.now()
      }

      console.log('in confirm_meeting, holdMeet.value', holdMeet.value)
      const sendData = []
      sendData.push(holdMeet.value)

      fsAction.start_meeting(sendData, fsStartMeetingSuccess, fsStartMeetingcallbackError, null)
      clear_column1()
      clear_column2()
      setTimeout(() => {
        message.success('会议正在下发...')
      }, 400)
      setTimeout(() => {
        show_defaultColumn2()
      }, 200)
    }

    const cancel_meetingPanel = () => {
      clear_column1()
      clear_column2()
      ifShow.value.meetingPanel = false
      show_defaultColumn2()
      clear_meeting()
    }

    /**
     * @description 禁言的方法，从子组件传来的需要禁言的成员列表
     * @param arr 需要禁言的人员列表
     */
    const forbiddenSpeech = (arr) => {
      console.log('禁言')
      console.log(arr)
      fsAction.conference_mute(arr, fsMeetOperateSuccess, fsMeetOperateCallbackError, null)
    }
    /**
     * @description 取消禁言的方法，从子组件传来的需要禁言的成员列表
     * @param arr 需要取消禁言的人员列表
     */
    const disForbiddenSpeech = (arr) => {
      console.log('解除禁言')
      console.log(arr)
      fsAction.conference_mute(arr, fsMeetOperateSuccess, fsMeetOperateCallbackError, null)
    }
    /**
     * @description 禁言的方法，从子组件传来的需要禁言的成员列表
     * @param arr 需要禁言的人员列表
     */
    const quarantine = (arr) => {
      console.log('隔离')
      console.log(arr)
      fsAction.conference_isolate(arr, fsMeetOperateSuccess, fsMeetOperateCallbackError, null)
    }
    /**
     * @description 禁言的方法，从子组件传来的需要禁言的成员列表
     * @param arr 需要禁言的人员列表
     */
    const disQuarantine = (arr) => {
      console.log('解除隔离')
      console.log(arr)
      fsAction.conference_isolate(arr, fsMeetOperateSuccess, fsMeetOperateCallbackError, null)
    }
    /**
     * @description 禁言的方法，从子组件传来的需要禁言的成员列表
     * @param arr 需要禁言的人员列表
     */
    const kickOut = (arr) => {
      console.log('踢人')
      console.log(arr)
      fsAction.conference_kick(arr, fsMeetOperateSuccess, fsMeetOperateCallbackError, null)
    }

    /**
      会议创建成功的回调函数
    */
    const fsStartMeetingSuccess = (data) => {
      // console.log('fsStartMeetingSuccess', data)
      if (data.error_code === 0) {
        message.success('会议创建成功')
      } else if (data.error_code === 100) {
        message.error(data.error_msg)
      } else { message.info('网络连接质量较差，请检查') }
    }

    /**
      广播创建成功的回调函数
    */
    const fsStartBrocastSuccess = (data) => {
      // console.log('fsStartMeetingSuccess', data)
      if (data.error_code === 0) {
        message.success('广播创建成功')
        // 获取广播列表
        getBroadcast()
      } else if (data.error_code === 100) {
        message.error(data.error_msg)
      } else { message.info('网络连接质量较差，请检查') }
    }

    const fsStartMeetingcallbackError = (error) => {
      console.log('fsStartMeetingcallbackError1:', error)
      const currfunc = 'start_meeting'
      checkError(error, currfunc)
    }

    const meet_quiet = () => {
      const action = 'mute-member'
      meet_operate(action)
    }

    const meet_quiet_cancel = () => {
      const action = 'unmute-member'
      meet_operate(action)
    }

    const meet_kick = () => {
      const action = 'kick-member'
      meet_operate(action)
    }

    const meet_kick_cancel = () => {
      ifShow.value.addMember = true
      fsAction.alluser_query('', fsGetAllUserSuccess, callbackError, null)
    }

    const meet_block = () => {
      const action = 'isolate'
      meet_operate(action)
    }

    const meet_block_cancel = () => {
      const action = 'no-isolate'
      meet_operate(action)
    }

    const meet_broard = () => {
      ifShow.value.broadcastPanel = true
      queryFileClickByKey()
    }

    const meetUserEnter = (e) => {
      /*       console.log('in meetUserEnter, e:', e)
      console.log('in meetUserEnter, e.target.innerText:', e.target.innerText)
      console.log('in meetUserEnter, currMeetList.value:', JSON.stringify(currMeetList.value)) */
      currMeetUser.value = {}
      currMeetList.value[0].meetUser.forEach((item, index, arr) => {
        if (item.name === e.target.innerText) {
          currMeetUser.value = item
        }
      })
      console.log('in meetUserEnter, currMeetUser.value.allData:', JSON.stringify(currMeetUser.value))
    }

    const meetUserLeave = (e) => {
      currMeetUser.value = {}
      // console.log('in meetUserLeave, currMeetUser.value:', JSON.stringify(currMeetUser.value))
    }

    const meet_operate = (action) => {
      console.log('in meet_operate, meetNumber:', meetNumber)
      // if (currMeetList.value.length)
      if (currMeet_key === 0) {
        message.error('请选择会议！')
        return
      }
      if ((meetUser_state.checkedList.length === 0) && (action !== 'add-member')) {
        message.error('请选择参会用户！')
        return
      }
      console.log('currMeetList.value[0]:', JSON.stringify(currMeetList.value[0]))
      const obj = {}
      const sendData = []
      obj.action = action
      obj.key = currMeetList.value[0].key
      obj.title = currMeetList.value[0].title
      obj.meet_topic = currMeetList.value[0].meet_topic
      obj.meet_number = currMeetList.value[0].meet_number
      obj.meet_time = currMeetList.value[0].meet_time

      obj.meeting_member = []
      if (action === 'add-member') {
        addMemberUserValue.value.forEach((item, index, arr) => {
          addMemberUserOption.value.forEach((item1, index1, arr1) => {
            if (item === item1.value) {
              obj.meeting_member.push(item1.data)
            }
          })
        })
      } else {
        meetUser_state.checkedList.forEach((item, index, arr) => {
          currMeetList.value[0].meetUser.forEach((item1, index1, arr1) => {
            if (item === item1.name) {
              if (action === 'broadcast') {
                console.log('broadcast_state.mute:', broadcast_state.mute)
                console.log('broadcastPlayValue.value:', broadcastPlayValue.value)
                if (broadcast_state.mute === true) {
                  obj.mutestatus = 'mute'
                } else {
                  obj.mutestatus = 'unmute'
                }
                if (broadcastPlayValue.value === 1) {
                  obj.fileplayaction = 'loop'
                } else if (broadcastPlayValue.value === 2) {
                  obj.fileplayaction = 'once'
                } else if (broadcastPlayValue.value === 3) {
                  obj.fileplayaction = 'stop'
                }
                obj.curr_file = curr_file
              }
              obj.meeting_member.push(item1)
            }
          })
        })
      }

      sendData.push(obj)
      console.log('sendData:', JSON.stringify(sendData))
      if (action === 'mute-member') {
        fsAction.conference_mute(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 禁言
      }
      if (action === 'unmute-member') {
        fsAction.conference_mute(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 解除禁言
      }
      if (action === 'kick-member') {
        fsAction.conference_kick(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 踢人
      }
      if (action === 'add-member') {
        fsAction.conference_kick(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 邀请
      }
      if (action === 'isolate') {
        fsAction.conference_isolate(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 隔离
      }
      if (action === 'no-isolate') {
        fsAction.conference_isolate(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // no-隔离
      }
      if (action === 'broadcast') {
        fsAction.conference_broadcast(sendData, fsMeetOperateSuccess(sendData[0]), fsMeetOperateCallbackError, null) // 广播
      }
      delay3s_refresh_meet(obj.key)
    }

    // const cmd_transfer = (data) => {
    //   let ret = ''
    //   switch (data) {
    //     case 'mute-member':
    //       ret = '禁言'
    //       break
    //     case 'unmute-member':
    //       ret = '取消禁言'
    //       break
    //     case 'kick-member':
    //       ret = '踢人'
    //       break
    //     case 'add-member':
    //       ret = '拉人'
    //       break
    //     case 'isolate':
    //       ret = '隔离'
    //       break
    //     case 'no-isolate':
    //       ret = '取消隔离'
    //       break
    //     case 'broadcast':
    //       ret = '广播'
    //       break
    //     default:
    //       break
    //   }
    //   return ret
    // }

    const fsMeetOperateSuccess = (data) => {
      console.log('fsMeetOperateSuccess', data)
      // const value = cmd_transfer(data.action)
      // message.success(` 下发 ${value} 命令成功!`)
    }

    const fsMeetOperateCallbackError = (data) => {
      console.log('fsMeetOperateCallbackError', data)
    }

    const clear_meeting = () => {
      meetingMember.value = []
      clear_input()
    }

    const clear_input = () => {
      holdMeet.value = {}
      holdMeet.value.userList = ''
    }

    const handleAddMemberChange = (value) => {
      console.log('handleAddMemberChange:', value)
    }

    const handleAddMemberClick = (data) => {
      console.log('handleAddMemberClick', data)
      const action = 'add-member'
      meet_operate(action)
      ifShow.value.addMember = false
    }

    const handlebroadcastClick = (data) => {
      console.log('handlebroadcastClick', data)
      const action = 'broadcast'
      meet_operate(action)
      ifShow.value.broadcastPanel = false
    }

    const onChangeTime = (value, dateString) => {
      // //console.log('Selected Time: ', value)
      // //console.log('Formatted Selected Time: ', dateString)
      holdMeet.value.meet_time = Date.parse(dateString)
      const currentTime = new Date()
      if (holdMeet.value.meet_time > currentTime) {
        const val = holdMeet.value.meet_time - currentTime
        // //console.log('val' + val)
        if (val < 600000) {
          notification.error({
            message: ` 预约会议需要间隔十分钟以上！${''}`,
            description: ''
          })
          ifShow.value.timepick = false
          holdMeet.value.meet_time = ''
        }
      } else {
        notification.error({
          message: ` 预约时间小于当前时间！${''}`,
          description: ''
        })
        ifShow.value.timepick = false
        holdMeet.value.meet_time = ''
      }
    }

    const onCheckTimeChange = () => {
      if (state.checked_date === true) {
        // //console.log('预约会议')
        holdMeet.value.meet_method = 1
        ifShow.value.timepick = true
      } else {
        // //console.log('立即会议')
        holdMeet.value.meet_method = 0
        ifShow.value.timepick = false
      }
    }

    const delay3s_refresh_meet = (key) => {
      setTimeout(asyncTask_refresh_meet(key), 6000)
    }

    const asyncTask_refresh_meet = (key) => {
      console.log('asyncTask_refresh_meet key:', key)
      refresh_key = key
      fsAction.conference_list([], fsRefreshMeetingSuccess, fsQueryMeetingcallbackError, null)
    }

    const fsRefreshMeetingSuccess = (data) => {
      console.log('fsRefreshMeetingSuccess data:', data)
      const ret_data = refreshTreeByKey(refresh_key)
      //   clear_column2()
      //   show_defaultColumn2()
      refresh_list(ret_data)
      search_user.value = ''
      ifShow.value.meetCard = false
    }

    watch(checkedKeys, () => {
      // //console.log('checkedKeys', checkedKeys.value)
      selectedMember.value = []
      meetingMember.value = []
      holdMeet.value.userList = ''
      const obj = checkedKeys.value
      Object.keys(obj).forEach((key) => {
        currAllUser.forEach((item) => {
          if (obj[key] === item.uuid) {
            selectedMember.value.push(item)
            if (ifShow.value.meetingPanel === true) {
              meetingMember.value.push(item)
              holdMeet.value.userList = holdMeet.value.userList.concat(item.name + '、')
            }
          }
        })
      })
      // //console.log('checkedKyes, selectedMember: ', selectedMember.value)
      // //console.log('checkedKyes, userList: ', holdMeet.value.userList)
      // //console.log('checkedKyes, holdMeet.value.userList.length: ', holdMeet.value.userList.length)
      if (holdMeet.value.userList.length >= 1) {
        holdMeet.value.userList = holdMeet.value.userList.slice(0, holdMeet.value.userList.length - 1)
      }
    })

    const clear_column1 = () => {
      checkedKeys.value = []
    }

    const parentMe = () => {
      alert('test')
    }

    const clear_column2 = () => {
      ifShow.value.hisCard = false
      ifShow.value.buttonHis = false
      ifShow.value.userList = false
      ifShow.value.userCard = false
      ifShow.value.meetList = false
      ifShow.value.meetCard = false
      ifShow.value.hisMeetList = false
      ifShow.value.userManage = false
      ifShow.value.deptManage = false
      ifShow.value.inputNumber = false
      ifShow.value.checkbox = false
      ifShow.value.meetingPanel = false
      ifShow.value.searchUser = false
    }
    const show_defaultColumn2 = () => {
      ifShow.value.buttonHis = true
      ifShow.value.userList = true
      ifShow.value.searchUser = true
      //   getAllUser()
    }

    const showLeftTreeUser = () => {
      ifShow.value.leftTree = false
      const box1 = document.getElementById('button_col1_person')
      box1.style.color = 'rgb(122, 242, 170)'
      const box2 = document.getElementById('button_col1_meeting')
      box2.style.color = 'rgb(149, 135, 145)'
      console.log('showLeftTreeUser, ifShow.value.leftTree:', ifShow.value.leftTree)
      // 显示调度用户卡片
      showUserCard()
    }
    /**
     * @name showUserCard 显示调度卡片
     * @description 切换为调度卡片的时候，卡片列表此时需要重新请求用户卡片列表数据
     */
    const showUserCard = () => {
      // 从会议页面切换到人员调度时候，对页面渲染做操作
      ifShow.value.userList = false
      ifShow.value.userCard = true
      ifShow.value.meetList = false
      ifShow.value.meetCard = false
      // 刚切换过来的时候没有用户列表，需要在后面进行请求
      getAllUser()
    }

    const showLeftTreeMeet = () => {
      ifShow.value.leftTree = true
      const box1 = document.getElementById('button_col1_person')
      box1.style.color = 'rgb(149, 135, 145)'
      const box2 = document.getElementById('button_col1_meeting')
      box2.style.color = 'rgb(122, 242, 170)'
      console.log('showLeftTreeMeet, ifShow.value.leftTree:', ifShow.value.leftTree)
    }

    const delFileClickByKey = (key) => {
      console.log('key: ', key)
      const sendData = []
      sendData.push(key)
      console.log('in delFileClickByKey sendData' + JSON.stringify(sendData))
      fsAction.broadcast_file_del(sendData, fsDelFileSuccess, fsDelFilecallbackError, null)
    }

    const fsDelFileSuccess = (data) => {
      console.log('fsDelFileSuccess:', data)
      fileBroadList.value = []
      data.fileList.forEach((item, index, arr) => {
        fileBroadList.value.push({
          id: item.id,
          fileName: item.fileName,
          aliasname: '',
          fileUrl: item.fileUrl,
          uploadTime: item.uploadTime
        })
      })
    }

    const fsDelFilecallbackError = (data) => {
      console.log('fsDelFilecallbackError:', data)
    }

    const queryFileClickByKey = (data) => {
      fsAction.broadcast_file_query('', fsQueryFileSuccess, fsQueryFilecallbackError, null)
    }

    const fsQueryFileSuccess = (data) => {
      console.log('fsQueryFileSuccess:', data)
      fileBroadList.value = []
      data.fileList.forEach((item, index, arr) => {
        fileBroadList.value.push({
          id: item.id,
          fileName: item.fileName,
          aliasname: '',
          fileUrl: item.fileUrl,
          uploadTime: item.uploadTime
        })
      })
    }

    const fsQueryFilecallbackError = (data) => {
      console.log('fsQueryFilecallbackError:', data)
    }

    const selFileClickByKey = (key) => {
      curr_file = key
      console.log('curr_file: ', curr_file)
    }

    /**
     * 从MQTT中获取用户状态数据，并将其同步更新到用户TABLE 表中
     * @param userStatusData  从MQTT消息中获取到的用户状态数据
     */
    const updateUserTableStatus = (userStatusData) => {
      console.log('userStatusData', userStatusData)
      // 找到表格对应用户
      userStatusData.forEach(userStatusElement => {
        // 获取到状态更改用户的UUID

        let uuid = ''
        uuid = userStatusElement.uuid

        // 遍历用户表，找到对应用户并进行状态更新
        currList.value.forEach(currListElement => {
          if (uuid === currListElement.uuid) {
            // 设置显示状态
            currListElement.call_status = userStatusElement.call_status
            // 设置显示对端号码
            currListElement.peer_number = userStatusElement.peer_number
          }
        })
      })
    }

    /**
 * @description 全屏功能
 */

    const allScreen = () => {
      const domElement = document.documentElement
      if (domElement.requestFullscreen) {
        domElement.requestFullscreen()
      } else if (domElement.mozRequestFullScreen) {
        domElement.mozRequestFullScreen()
      } else if (domElement.webkitRequestFullScreen) {
        domElement.webkitRequestFullScreen()
      }
    }

    onMounted(() => {
      // 进入页面后全屏
      allScreen()
      // 进入页面的时候可能会遇到还没加载完成的情况，这个时候就需要捕获异常
      try {
        // 判断mqtt不为空的情况下,也就是说window存在mqtt连接对象
        if ((window.mq_client !== null || window.mq_client !== undefined) && window.mq_client.connected !== undefined) {
          // 获取mqtt链接对象
          mqttClient = window.mq_client
          // 定义主题
          const str_topic = '/BGofDD1/web2RefreshMemberCallState'
          // 订阅主题
          mqttClient.subscribe(str_topic, (err) => {
            if (!err) {
              // 如果没出现错误，则控制台输出订阅的主题
              console.log('Subscribed to topic:', str_topic)
            }
          })
        } else {
          // 控制台打印错误
          console.error('获取mqtt连接对象为空或未连接')
        }

        // 添加mqtt消息监听事件
        mqttClient.on('message', (str_topic, message) => {
          console.log('收到消息来自主题：', str_topic)
          console.log('消息内容：', message.toString())
          console.log(JSON.parse(message.toString()).members)

          // 判断消息类型
          if (JSON.parse(message.toString()).msgType === 'call-state') { /// / 如果为用户呼叫消息，则需要传入组件属性，用于实时更新用户状态
            // 将消息字符串转为JSON对象后传入组件，此消息为人员状态改变的消息
            userStatusChange.value = JSON.parse(message.toString()).members

            // 遍历变动列表，判断是否为坐席用户
            userStatusChange.value.forEach(element => {
              // 判断是否为坐席用户，如果为坐席用户则更新坐席组件卡片
              if (hostUser.name === element.name) {
                hostUserForComponents.value = element
              }
            })
            // 将状态改变的用户的显示状态进行更新
            updateUserTableStatus(JSON.parse(message.toString()).members)
          } else if (JSON.parse(message.toString()).msgType === 'startup') { // 用户状态整表更新
            console.log('用户状态全部更新')
          } else if (JSON.parse(message.toString()).msgType === 'conference-state') { // 收到会议消息
            userStatusChange.value = JSON.parse(message.toString()).members

            // 判断事件类型，根据不同的事件类型从而走不同的分支
            if (userStatusChange.value[0].isBroadcast) {
              switch (userStatusChange.value[0].action) {
                case 'conference-create': // 创建会议
                  getBroadcast()
                  break
                case 'conference-destroy': // 删除用户
                  getBroadcast()
                  break
              }
            } else {
              switch (userStatusChange.value[0].action) {
                case 'add-member': // 用户加入
                  addMember.value = userStatusChange.value[0]
                  getMeetingList()
                  break
                case 'del-member': // 用户离开
                  delMember.value = userStatusChange.value[0]
                  getMeetingList()
                  break
                case 'conference-create': // 创建会议
                  getMeetingList()
                  // getBroadcast()
                  break
                case 'conference-destroy': // 删除用户
                  getMeetingList()
                  // getBroadcast()
                  break
                case 'mute-member': // 禁言
                  memberStatus.value = userStatusChange.value[0]
                  break
                case 'unmute-member': // 取消静音
                  memberStatus.value = userStatusChange.value[0]
                  break
                case 'deaf-member': // 隔离
                  memberStatus.value = userStatusChange.value[0]
                  break
                case 'undeaf-member': // 取消隔离
                  memberStatus.value = userStatusChange.value[0]
                  break
              }
            }
          } else if (JSON.parse(message.toString()).msgType === 'callqueue-state') { // 收到呼叫队列消息
            fsAction.getDialQueue('', fsGetDialQueueSuccess, callbackError, null)
            console.log('callqueue-state')
          }
        })
      } catch (error) {
        console.log('MQTT异常' + error)
      }

      setTimeout(() => {
        // 获取用户列表
        console.info('初始化组件完毕')
        ifShow.value.userList = false
        // if_showCard.value = true
        instantRefresh()
        // startRefresh()
        getAllUser()
      }, 1000)

      const jsonconfig_str = sessionStorage.getItem('mesh_Config')
      if (jsonconfig_str != null) {
        // console.log(jsonconfig_str)
        // const json_obj = JSON.parse(jsonconfig_str)
        // upload_file_url = json_obj.upload_file
        upload_file_url = 'http://msy'
      }
      upload_file_url = 'http://msy1 '
      console.log('upload_file_url:', upload_file_url)
    })

    onUnmounted(() => {
      // //console.log('人员卡片被销毁')
      stopRefresh()
    })

    return {
      showMeetingLsit,
      showBroadcastLsit,
      clickHistoryMeetingList,
      clickMeetingList,
      clickRuingMeeting,
      clickBroadcastList,
      clickCurrentBroadcast,
      clickHistoryBroadcast,
      LabelCaption,
      selectedKeys,
      expandedKeys,
      treeData,
      onUnmounted,
      onMounted,
      exit,
      show_card,
      dialSpitClick,
      user_columns,
      queue_columns,
      switch_show,
      currList,
      queueList,
      userListUpdate,
      currMeetList,
      currBoradcastList,
      hisMeetList,
      getLeftTree,
      callbackError,
      rightTop,
      warningInfo,
      tmpTreeData,
      operation,
      clearWarningClick,
      dial,
      sethost,
      btclick,
      dial_answer,
      dial_off,
      fsDialcallbackError,
      handleMenuClick,
      handleMenuClickByKey,
      handleMeetMenuClick,
      handleMeetMenuClickByKey,
      handleQueueMenuClick,
      handleQueueMenuClickByKey,
      handleHisMeetMenuClick,
      handleHisMeetMenuClickByKey,
      stopRefresh,
      startRefresh,
      hostUser,
      hostUserForComponents,
      currHostUser,
      queueOptions,
      handleQueueChange,
      onSearchUser,
      search_user,
      messVisible,
      infoClick,
      info_columns,
      meet_columns,
      deleteInfo,
      infoList,
      warningInfoClick,
      if_userlist_dropdown,
      if_queuelist_dropdown,
      dayjs,
      rangpickdate,
      cancel_queryHis,
      queryHis,
      Pattern,
      ifShow,
      currAllUser,
      selectHisUser,
      hisUserOptions,
      handleHisFocus,
      handleHisBlur,
      handleHisChange,
      hisFilterOption,
      selectedHisUser,
      ifSpinning,
      spinTip,
      answering,
      userManage,
      showModal,
      handleOk,
      addComp,
      slotModify,
      slotDelete,
      confirmDel,
      getTreeData,
      dialInsertClick,
      dialTransClick,
      answeringClick,
      mytreeData,
      parentNode,
      compName,
      compNum,
      isUpdate,
      showNode,
      inputTitle,
      onCheckChange,
      getListByKey1,
      quit_userManage,
      start_meeting,
      clear_meeting,
      queueValue,
      checkedKeys,
      confirm_meeting,
      cancel_meetingPanel,
      nociceMeet,
      onChangeTime,
      onCheckTimeChange,
      holdMeet,
      showLeftTreeUser,
      showLeftTreeMeet,
      tmpMeetTreeData,
      expandedMeetKeys,
      selectedMeetKeys,
      dialClick,
      onSubmit,
      formRef,
      parentMe,
      dialOffClick,
      dialListenClick,
      userStatusChange,
      personnelList,
      hostInfoCard,
      loding2,
      ctrlBar,
      currAllQueue,
      meetUserOptions,
      meetUser_state,
      broadcast_state,
      onCheckAllChange,
      meet_quiet,
      meet_quiet_cancel,
      meet_kick,
      meet_kick_cancel,
      meet_block,
      meet_block_cancel,
      meet_broard,
      meetUserEnter,
      meetUserLeave,
      currMeetUser,
      currMeet,
      addMemberUserValue,
      addMemberUserOption,
      handleAddMemberChange,
      handleAddMemberClick,
      broadcastPlayValue,
      handlebroadcastClick,
      fileList,
      upload_file_url,
      handleFileChange,
      file_columns,
      fileBroadList,
      delFileClickByKey,
      selFileClickByKey,
      queryFileClickByKey,
      multiAnswerClick,
      muteList,
      blockList,
      showLeftTree,
      button2,
      go_dispatch,
      go_meeting,
      changeShowUserListMode,
      showDispatch,
      showMeeting,
      meetingList,
      broadcast,
      stopMeeting,
      createMeeting,
      createBrocast,
      forbiddenSpeech,
      disForbiddenSpeech,
      quarantine,
      disQuarantine,
      kickOut,
      addMember,
      delMember,
      memberStatus,
      go_broadcast,
      his_meeting_dataSource,
      showBroadcast,
      stopBrocast,
      currentActiveTab,
      h,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped>
.background{
    background-color: #011329;
}

/* 自定义滚动条样式 - 对所有滚动条生效 */
:deep(::-webkit-scrollbar) {
    width: 5px;
    height: 5px;
}

:deep(::-webkit-scrollbar-thumb) {
    background: rgba(144, 147, 153, 0.3);
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

:deep(::-webkit-scrollbar-track) {
    background: transparent;
}

/* 鼠标悬停时的滚动条样式 */
:deep(:hover::-webkit-scrollbar-thumb) {
    background: rgba(144, 147, 153, 0.5);
}

/* 滚动条角落 */
:deep(::-webkit-scrollbar-corner) {
    background: transparent;
}

/* 兼容Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

/* 扩大树组件宽度 */
:deep(.ant-tree) {
    width: 100% !important;
    min-width: 200px;
}

:deep(.ant-tree-list) {
    width: 100%;
}

:deep(.ant-tree-node-content-wrapper) {
    width: auto !important;
    display: inline-block;
}

/* 保留旧样式以兼容性，但已不需要 */
.a-tree::-webkit-scrollbar {
    /* display: none; */ /* 原代码：对于WebKit浏览器隐藏滚动条 */
}

body{
    margin: 0;
}
.title_wrap {
  height: 60px;
  background-image: url("@/assets/img/top.png");
  background-size: cover;
  background-position: center center;
  position: relative;
  margin-bottom: 6px;
}
.zuojuxing {
    left: 11%;
  }

  .youjuxing {
    right: 11%;
    transform: rotate(180deg);
  }

  .guang {
    position: absolute;
    bottom: -26px;
    background-image: url("@/assets/img/guang.png");
    background-position: 80px center;
    width: 100%;
    height: 56px;
  }
  .title {
  position: relative;
  text-align: center;
  background-size: cover;
  color: transparent;
  height: 60px;
  line-height: 46px;

}

.title-text {
    font-size: 38px;
    font-weight: 900;
    letter-spacing: 6px;
    width: 100%;
    background: linear-gradient(
      92deg,
      #0072ff 0%,
      #00eaff 48.8525390625%,
      #01aaff 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
.chart{
     width: 95%;
     height: 78%;
     position: relative;
     margin-left:2%;
     margin-top: -1%;
}

.chart_top{
     width: 95%;
     height: 87%;
     position: relative;
     margin-left:2%;
}
.index-box {
  width: 100%;
  display: flex;
  min-height: calc(100% - 64px);
  justify-content: space-between;
}

.content_left,
.content_right {
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
  position: relative;
  width: 20%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.content_center {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  width: 60%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.content_center_personnel_list {
    flex-grow: 1;
}

.content_center_ctrl_bar {
    margin-bottom: 3%;
}

.item_title {
  height:20%;
  line-height: calc(100% - 38px);
  width: 100%;
  color: #23aceb;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item_title_top {
    height: 10%;
  line-height: calc(100% - 38px);
  width: 100%;
  color: #23aceb;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
  .zuo,
  .you {
    width: 58px;
    height: 14px;
    background-image: url("@/assets/img/titles/zuo.png");
  }

  .you {
    transform: rotate(180deg);
  }
  .title-inner {
    font-weight: 900;
    letter-spacing: 6px;
    background: linear-gradient(
      92deg,
      #0072ff 0%,
      #00eaff 48.8525390625%,
      #468cb4 100%
    );
    -webkit-background-clip: text;
  }
  :deep(.dv-border-box-content)  {
    box-sizing: border-box;
    padding: 6px 16px 0px;
  }

  .item_title_content {
  height: 8px;
}

.item_title_content_def {
  width: 100%;
  height: 100%;
}

.contetn_lr-item {
  height: 300px;
}
.editable-row-operations a {
  margin-right: 8px;
}

.dropdown-style {
  height:'300px';
  overflow: 'auto'
}

.exit-button {
    width:80px;
    height:40px;
    color: #ffffff;
}

/* 导航菜单样式 */
.navigation-container {
    z-index: 100;
}

.nav-menu {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: center;
}

.nav-item {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    transform: translateY(-3px);
}

.nav-text {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
}

/* 活跃状态样式 */
.nav-item-active .nav-text {
    color: #00eaff !important;
    font-weight: 700;
    text-shadow: 0 0 8px rgba(0, 234, 255, 0.6);
}

.nav-item-active {
    transform: translateY(-5px);
}

/* 为活跃状态添加发光边框效果 */
.nav-item-active :deep(.dv-border-box-6),
.nav-item-active :deep(.dv-border-box-8) {
    filter: drop-shadow(0 0 6px rgba(0, 234, 255, 0.4));
}
.parent {
display: grid;
grid-template-columns: repeat(6, 1fr);
grid-template-rows: repeat(13, 1fr);
grid-column-gap: 0px;
grid-row-gap: 0px;
}

.div1 { grid-area: 1 / 1 / 2 / 7; }
.div2 { grid-area: 2 / 1 / 14 / 7; overflow-y: scroll;overflow-x: hidden;}

.radio-input {
  display: flex;
  flex-direction: row;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  align:center;
  font-size: 20px;
  font-weight: 300;
  width:100%;
  align-content: center;
  justify-content: space-around;
  padding: 1%;
  color: white;
}

.radio-input input[type="radio"] {
  display: none;
}

.radio-input label {
  display: flex;
  align-items: center;
  align:"center";
  padding: 20px;
  border: 2px solid #468cb4;
  background-color: #011329;
  border-radius: 5px;
  margin-right: 12px;
  cursor: pointer;
  position: relative;
  width: 100%;
  justify-content: space-around;
  transition: all 0.3s ease-in-out;
}

.radio-input label:before {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #ccc;
  transition: all 0.3s ease-in-out;
}

.radio-input input[type="radio"]:checked + label:before {
  background-color: green;
  top: 0;
}

.radio-input input[type="radio"]:checked + label {
  background-color: royalblue;
  color: #ffffff;
  border-color: rgb(129, 235, 129);
  animation: radio-translate 0.5s ease-in-out;
}

.meet-list-table{
    background-color: #0073ff00;
}
@keyframes radio-translate {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateX(0);
  }
}

/* 自定义滚动条样式 */
:deep(*::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(*::-webkit-scrollbar-thumb) {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

:deep(*::-webkit-scrollbar-track) {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

:deep(*::-webkit-scrollbar-corner) {
  background: transparent;
}

/* 鼠标悬停时的滚动条样式 */
:deep(*:hover::-webkit-scrollbar-thumb) {
  background: rgba(144, 147, 153, 0.5);
}

/* 应用于树组件的滚动区域 */
:deep(.ant-tree) {
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) rgba(0, 0, 0, 0.1);
}

.message-queue-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 8px 0;
  margin-bottom: 5px;
}

.tech-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tech-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tech-button:hover::before {
  left: 100%;
}

.tech-button-info {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #40a9ff;
  color: #fff;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.tech-button-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.4);
  border-color: #73d13d;
}

.tech-button-clear {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border-color: #ff7875;
  color: #fff;
  box-shadow: 0 0 10px rgba(255, 77, 79, 0.3);
}

.tech-button-clear:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 77, 79, 0.4);
  border-color: #ff1744;
}

.tech-button-danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff1744 100%);
  border-color: #ff1744;
  color: #fff;
  box-shadow: 0 0 10px rgba(255, 23, 68, 0.3);
}

.tech-button-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 23, 68, 0.4);
}

.tech-modal :deep(.ant-modal-content) {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border: 1px solid #00bcd4;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 188, 212, 0.3);
}

.tech-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #1a2f5a 0%, #2d4a73 100%);
  border-bottom: 1px solid #00bcd4;
  border-radius: 7px 7px 0 0;
}

.tech-modal :deep(.ant-modal-title) {
  color: #00e5ff;
  font-weight: bold;
}

.tech-modal :deep(.ant-modal-body) {
  color: #ffffff;
}

.tech-table :deep(.ant-table) {
  background: transparent;
  border: 1px solid #00bcd4;
}

.tech-table :deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #1a2f5a 0%, #2d4a73 100%);
  color: #00e5ff;
  border-bottom: 1px solid #00bbd48e;
  font-weight: bold;
}

.tech-table :deep(.ant-table-tbody > tr > td) {
  background: rgba(30, 60, 114, 0.3);
  color: #ffffff;
  border-bottom: 1px solid rgba(0, 188, 212, 0.2);
}

.tech-table :deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(0, 188, 212, 0.2);
}

.message-queue-content {
  height: calc(100% - 50px);
  overflow-y: auto;
  padding: 5px;
}

.empty-message-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.empty-message-text {
  color: #00bcd4;
  font-size: 14px;
  margin-top: 10px;
  text-shadow: 0 0 5px rgba(0, 188, 212, 0.5);
}

/* 毛玻璃效果样式 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 10px;
  box-shadow:
    0 8px 32px rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}
.glass-effect:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 15px 40px rgba(31, 38, 135, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(0, 229, 255, 0.3);
}

/* 座席信息区域毛玻璃效果 */
:deep(.dv-border-box-13) {
  background: rgba(0, 20, 40, 0.3);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 188, 212, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

:deep(.dv-border-box-13:hover) {
  transform: scale(1.01);
  border-color: rgba(0, 229, 255, 0.4);
  box-shadow:
    0 0 30px rgba(0, 188, 212, 0.3),
    inset 0 0 20px rgba(0, 229, 255, 0.1);
}

/* 用户卡片增强效果 */
.personnel-card {
  background: rgba(0, 30, 60, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 188, 212, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.personnel-card:hover {
  transform: translateY(-3px);
  border-color: rgba(0, 229, 255, 0.6);
  box-shadow:
    0 10px 25px rgba(0, 188, 212, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 消息队列区域增强 */
.message-queue-content {
  background: rgba(0, 20, 40, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  border: 1px solid rgba(0, 188, 212, 0.15);
  margin: 5px;
  padding: 10px;
  transition: all 0.3s ease;
}

.message-queue-content:hover {
  background: rgba(0, 30, 60, 0.3);
  border-color: rgba(0, 229, 255, 0.3);
}

/* 控制按钮区域增强 */
.tech-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tech-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 座席状态信息增强 */
.status-info-panel {
  background: rgba(0, 40, 80, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 188, 212, 0.3);
  border-radius: 10px;
  padding: 15px;
  margin: 10px 0;
  transition: all 0.4s ease;
  position: relative;
}
.status-info-panel:hover {
  transform: scale(1.02);
  border-color: rgba(0, 229, 255, 0.5);
  box-shadow: 0 0 25px rgba(0, 188, 212, 0.4);
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 229, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 229, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 229, 255, 0);
  }
}

.pulse-effect {
  animation: pulse 2s infinite;
}

/* 浮动动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.float-effect {
  animation: float 3s ease-in-out infinite;
}

/* 闪烁边框效果 */
@keyframes borderGlow {
  0%, 100% {
    border-color: rgba(0, 188, 212, 0.3);
    box-shadow: 0 0 5px rgba(0, 188, 212, 0.2);
  }
  50% {
    border-color: rgba(0, 229, 255, 0.8);
    box-shadow: 0 0 20px rgba(0, 229, 255, 0.4);
  }
}

.glow-border {
  animation: borderGlow 2s ease-in-out infinite;
}
</style>
