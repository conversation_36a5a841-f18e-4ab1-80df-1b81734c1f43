<template>
    <div  class="card-info" >
        <div v-if="name==null||name==''" style="margin: 0 auto; width: 50%;">
            <loding style="margin: 0 auto; width: 50%">
            </loding>
            <div style="margin-top: 20%;">暂未坐席信息...</div>
        </div>
        <dv-border-box-8>
                    <a-card v-if="name!==null&&name!==''" class="a-card"  :bordered="false" >
                        <template #title>
                            <a-row span="24">
                                <a-col span="20">
                                    <img class="card-img" src="../../../assets/img/host-card/host.png"/>
                                    {{name}}
                                </a-col>
                                <a-col span="4">
                                    <dv-decoration-9 style="width:40px;height:40px;left: 0.5%"></dv-decoration-9>
                                </a-col>
                            </a-row>
                        </template>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/host-card/dept.png"/></a-col>
                            <a-col span="8" align="center"> 部门：</a-col>
                            <a-col span="11" align="start">{{department}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/host-card/number.png"/></a-col>
                            <a-col span="8" align="center">号码：</a-col>
                            <a-col span="11" align="start">{{number}}</a-col>
                        </a-row>
                        <a-row span:24 class="a-card-text">
                            <a-col span="2" align="start"><img class="card-img" src="../../../assets/img/host-card/status.png"/></a-col>
                            <a-col span="8" align="center"> 状态：</a-col>
                            <a-col span="11" align="start">{{status}}</a-col>
                        </a-row>
                    </a-card>
                </dv-border-box-8>
                </div>
  </template>

<script>
/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                 神兽保佑
 *                 代码无BUG!
 */

import { defineComponent, ref, onMounted, watch } from 'vue'
import loding from '@/views/components/dispatch/loding.vue'

export default defineComponent({
  props: ['host'],
  components: {
    loding
  },
  setup (props) {
    const hostUserInfo = ref([])
    const name = ref('')
    const number = ref('')
    const status = ref('')
    const department = ref('')
    const hasBennSetHostUser = ref(false)
    watch(() => props.host, (newValue, oldValue) => {
      console.log(newValue)
      name.value = '当前坐席：' + newValue.name
      number.value = newValue.number
      status.value = newValue.call_status
      if (newValue.dept !== undefined && newValue !== null) {
        department.value = newValue.dept
      } else {
        department.value = newValue.department
      }
    })

    onMounted(() => {
      // 监听 props.host 的变化，当 props.host 发生变化时，更卡片状态。
    })
    return {
      hostUserInfo,
      name,
      number,
      status,
      department,
      hasBennSetHostUser,
      loding
    }
  }

})
</script>
<style scoped>
    .card-info{
        margin-left: 20px;
        margin: 20px;
    }
    .a-card{
        width: 100%;
        height: 100%;
        text-align: start;
        padding: 0%;
        background-color: rgba(127, 255, 212, 0);
    }
    .a-card-text{
        text-align: 0%;
        margin-top: 5%;
    }
    .card-img{
        height: 20px;
        width: 20px;
    }
</style>
