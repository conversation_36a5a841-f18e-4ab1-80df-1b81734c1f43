// 正则校验的正则表达式，这里注意正则表达式中的‘\’要使用‘\\’转义
const patterns = {
  name: /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,10}$/,
  tel: /^1[2-9]\\d{0,}$/,
  email: /^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/,
  pwd: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W]{8,}$/,
  // IP: /^1\\d{2}|2[0-4]\\d|25[0-5][1-9]\\d|[1-9](\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)){3}$/,
  IP: /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])$/,
  IDCard: /(^\\d{15}$)|(^\\d{17}([0-9]|X)$)/,
  Port: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
  Temp: /^([6-9][0-9]|[100])$/,
  Voltage: /^([1][3-5]|[1][3-5].[0-9])$/,
  MeshNo: /^([1-9]|[1-3][0-2])$/,
  RegisterPeriod: /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
  MeshKey: /^[a-zA-Z0-9]{1,32}$/,
  Outputrate: /^(-[1-9]|-[1][0-9]|-20|0|[1-9]|[1-4][0-9]|50)$/,
  Pass: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W]{8,}$/,
  UserName: /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,10}$/,
  Number: /^[0-9]*$/
}

// 对应正则表达式的提示信息
const patternMsg = {
  name: '长度在10个字符以内，可包含字母、数字、下划线（_）',
  tel: '非正确的号码',
  email: '非正确的邮箱地址',
  pwd: '密码至少由8位组成，必须包含字母、数字',
  IP: '非正确IP地址',
  IDCard: '非正确身份证号码',
  Port: '非正确端口',
  Temp: '请输入60-100℃以内的温度数值',
  Voltage: '请输入13.0-15.0伏之间的电压',
  MeshNo: 'Mesh编号范围：1-32',
  RegisterPeriod: '注册间隔范围为：0~65535,0为默认10s(单位：秒)',
  MeshKey: '空口密钥的长度范围为：1~32位置',
  Outputrate: '输出功率范围为：-40dBm~50dBm',
  Pass: '密码长度不低于八位，必须包含字母、数字',
  UserName: '长度在10个字符以内，可包含字母、数字、下划线（_）',
  Number: '只能用数字'
}

// 根据使用的正则返回对应的正则和信息对象
export default function Pattern (name, para = 'g') {
  return {
    pattern: new RegExp(patterns[name], para),
    message: patternMsg[name]
  }
}
