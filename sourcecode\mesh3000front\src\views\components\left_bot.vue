<template>
    <dv-scroll-board @click="addNewRows()" :config="config" style="width:100%;height:100%" />
  </template>

<script>
import { defineComponent, onMounted, ref, watch } from 'vue'

export default defineComponent({
  props: ['warningInfo'],
  setup (props) {
    const config = ref({
      header: ['名称', '级别', '描述'],
      data: [],
      index: true,
      columnWidth: [50, 100, 60, 240],
      align: ['center']
    })

    const updateRows = (rows, startIndex) => {
      // 检查是否需要重置轮播数据
      // 追加新行数据到现有数据后面
      config.value.data.push(rows)

      console.log(config.value.data)
    }

    // 监听 props.warningInfo 的变化
    watch(() => props.warningInfo, (newValue, oldValue) => {
      // 创建新的 props 对象
      const newConfig = { ...config.value, data: newValue }
      config.value = newConfig

      // 在 props.warningInfo 变化后追加新的行数据
    //   const newRows = newValue[0]
    //   updateRows(newRows) // 0 表示从头开始轮播
    //   addNewRows()
    })

    const addNewRows = () => {
      console.log(config.value)
    }

    // const addNewRows = () => {
    //   // 调用 updateRows 方法，传递新的行数据和 startIndex（可选）
    //   const newRows = [['123', '一般', '新的警告']]
    //   updateRows(newRows, 0) // 0 表示从头开始轮播
    //   const newConfig = { ...config.value, data: newRows }
    //   config.value = newConfig
    // }

    onMounted(() => {
      // 在组件挂载后，你可以执行其他初始化操作
    })

    return {
      config,
      updateRows,
      addNewRows
    }
  }
})
</script>
