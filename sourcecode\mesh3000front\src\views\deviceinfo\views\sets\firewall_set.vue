<template>

  <a-card title="防火墙设置">
    <a-form :model="fireWall_IpPort" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">
      <a-row justify="center" style="margin-top:5%">
        <a-form-item :label="LabelCaption.fire_NetFilterType.label" :name="['configData', 'NetFilterType']">
          <a-select ref="select" v-model:value="model.configData.NetFilterType" v-bind:placeholder="LabelCaption.fire_NetFilterType.label">
            <a-select-option v-for="option in firewallOptions" v-bind:key="option.value" :value="value">{{ option.name }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-row>

      <a-row justify="center" :span="10">
        <a-col>
          <a-space v-for="(n, index) in fireWall_IpPort.ip_port" :key="n.id" style="display: flex; margin-bottom: 3px" align="baseline" v-show="model.configData.NetFilterType">
            <a-form-item :name="['ip_port', index, 'ip']" :rules="[{ required:model.configData.NetFilterType , message: 'IP地址不能为空' },Pattern('IP')]" :lable="LabelCaption.fire_IP.label">
              <a-input v-model:value="n.ip" :placeholder="LabelCaption.fire_IP.label" style="margin-top:4%" />
            </a-form-item>
            <a-form-item :name="['ip_port', index, 'port']" :rules="[{ required:model.configData.NetFilterType , message: '端口不能为空' },Pattern('Port')]" :lable="LabelCaption.fire_port.label">
              <a-input v-model:value="n.port" :placeholder="LabelCaption.fire_port.label" />
            </a-form-item>
            <MinusCircleOutlined @click="deleteIpAddress(index)" />
          </a-space>
        </a-col>
      </a-row>
      <a-row justify="center">
        <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button type="dashed" v-show="model.configData.NetFilterType" @click="addIpAddress" style="margin-top:8%" v-if="AuthUi.防火墙.statusW" :disabled="fireWall_IpPort.ip_port.length >= 10">
            <PlusOutlined />
            添加记录
          </a-button>
        </a-form-item>
      </a-row>
      <a-divider />
      <a-row justify="center">
        <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button type="primary" html-type="submit" @click="submitForm" :disabled="(fireWall_IpPort.ip_port.length === 0) && (model.configData.NetFilterType != 0)" v-if="AuthUi.防火墙.statusW">保存</a-button>
          <a-button type="primary" style="margin-left: 10px" @click="getFireWall">刷新</a-button>
        </a-form-item>
      </a-row>
    </a-form>
  </a-card>

</template>

<script>
import { QuestionOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { onMounted, defineComponent, createVNode, reactive } from 'vue'
import { Modal, message } from 'ant-design-vue'

// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { FireWallRequestAction } from '@/views/deviceinfo/action/fireWallAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
import { Firewall } from '@/views/deviceinfo/model/Firewall'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import {
  firewallOptions
} from '@/views/deviceinfo/constant/options'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

export default defineComponent({

  components: {
    MinusCircleOutlined,
    PlusOutlined
  },

  setup () {
    const onFinish = values => {
      console.log('Received values of form:', values)
    }

    const enabledAdd = reactive({
      add: 'false'
    })
    // 防火墙 IP&Port；用于存放防火墙白名单、黑名单的端口、ip字符串变量
    const fireWall_IpPort = reactive({
      // 存放IP、端口的数组
      ip_port: [],
      // IP字符串
      ipString: '',
      // 端口字符串
      portString: '',
      // 组件的KEY（保证不唯一）
      id: Date.now()
    })

    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    const firewall = new Firewall()
    model.configData = firewall

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const fireWallRequestAction = new FireWallRequestAction()
    const pageDirectAction = new PageDirectAction()

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }
    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('防火墙信息设置成功!')
        // pageDirectAction.goToIndex()
      } else {
        message.success(`设置防火墙信息失败!${data.error_code}`)
      }
    }

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      fireWallRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
      // 如果为关闭黑名单，清空数组
      clearArryList()
      // 组装字符串
      mergeIpString()
    }
    // 如果为关闭黑名单则清空数组
    const clearArryList = () => {
      // 如果关闭防火墙，将清空黑白名单列表
      if (model.configData.NetFilterType === 0) {
        //   model.configData.IpAddr=null
        // 并且需要数组列表不为空
        if (model.configData.IpAddr != null) {
          model.configData.IpAddr.splice(0, model.configData.IpAddr.length)
          fireWall_IpPort.ip_port.splice(0, model.configData.IpAddr.length)
        } else { // 如果为数组为空，则手动赋值，解决数组null从而引发的异常
          model.configData.IpAddr = []
          fireWall_IpPort.ip_port = []
        }
      }
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前防火墙参数 *******************
    const getFireWall = () => {
      fireWallRequestAction.query(baseRequestData, getFireWallSuccess, callbackError, null)
    }

    const getFireWallSuccess = (data) => {
      model.configData = data.data.configData
      // 进行IP与端口分割
      subIpString()
    }
    // ****************** 设备当前防火墙参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      getFireWall()
      // getDeviceinfoById()
    })
    // ****************** 删除一条记录 *******************
    const deleteIpAddress = (i) => {
      fireWall_IpPort.ip_port.splice(i, 1)
      model.configData.IpAddr.splice(i, 1)
      //   console.log('删除了一条记录')
    }
    // ****************** 添加一条记录 *******************
    const addIpAddress = () => {
      // 如果第一次添加则自动创建一条记录
      if (model.configData.IpAddr == null) {
        fireWall_IpPort.ip_port = []
        model.configData.IpAddr = []
        fireWall_IpPort.ip_port.push({
          ip: '',
          port: ''
        })
        model.configData.IpAddr.push('')
      } else {
        model.configData.IpAddr.push('')
        fireWall_IpPort.ip_port.push({
          ip: '',
          port: ''
        })
      }
    }
    // IP与端口进行分割
    const subIpString = () => {
      // 如果拿到的IP数组不为空则进行分割
      if (model.configData.IpAddr != null) {
        fireWall_IpPort.ip_port = []
        for (let i = 0; i < model.configData.IpAddr.length; i++) {
          // 拿到IP，并存储在IP数组中
          fireWall_IpPort.ipString = model.configData.IpAddr[i]
          // 拿到port，并存储在port数组中
          fireWall_IpPort.portString = model.configData.port[i]
          //   // 判断待添加的下标是否为空，如果为空则添加一条记录
          // 将fireWall_IpPort.ip_port数组的长度添加到和设备侧信息一样长
          if (fireWall_IpPort.ip_port.length < model.configData.IpAddr.length) {
            fireWall_IpPort.ip_port.push({
              ip: '',
              port: ''
            })
          }
          // 将数据写入到数组中
          fireWall_IpPort.ip_port[i].ip = fireWall_IpPort.ipString
          //   console.log('ip地址：' + fireWall_IpPort.ip_port[i].ip)
          fireWall_IpPort.ip_port[i].port = fireWall_IpPort.portString
          //   console.log('端口：' + fireWall_IpPort.ip_port[i].port)
        }
      }
    }

    // IP与端口进行合并
    const mergeIpString = () => {
      // 当存在IP与PORT时候才进行合并
      if (model.configData.IpAddr != null) {
        // eslint-disable-next-line no-empty
        for (let i = 0; i < model.configData.IpAddr.length; i++) {
          model.configData.IpAddr[i] = fireWall_IpPort.ip_port[i].ip
          model.configData.port[i] = fireWall_IpPort.ip_port[i].port
        }
      }
    }
    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      firewallOptions,
      enabledAdd,
      fireWall_IpPort,
      AuthUi,
      subIpString,
      confirm,
      Pattern,
      getFireWall,
      onFinish,
      addIpAddress,
      deleteIpAddress
    }
  }
})
</script>
<style>
.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
