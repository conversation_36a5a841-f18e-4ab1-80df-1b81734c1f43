<template>
    <dv-loading v-if="showLoading"></dv-loading>
    <div id="main" style="width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0); position: relative;"></div>
  </template>

<script>
import { defineComponent, onMounted, watch, ref } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  props: ['RSSI'],
  setup (props) {
    const dataSouceY = ref([])

    const dataSouceX = ref([])
    const dataZoomMove = {
      start: 0,
      end: 4
    }
    const option = {
      dataZoom: [
        {
          show: true, // 为true 滚动条出现
          realtime: true,
          type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
          startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
          endValue: dataZoomMove.end,
          width: 6,
          right: '20',
          top: '20%', // 位置和grid配置注意下
          height: '56%',
          yAxisIndex: [0, 1], // 关联多个y轴
          moveHandleStyle: {
            color: 'rgba(89, 202, 241,.5)'
          },
          moveHandleSize: '6',
          emphasis: {
            moveHandleStyle: {
              color: 'rgba(89, 202, 241,.5)'
            }
          },
          textStyle: {
            color: 'rgba(255,255,255,0)'
          },
          backgroundColor: 'rgba(255,255,255,.1)',
          borderColor: 'rgba(255,255,255,0)',
          fillerColor: 'rgba(0,0,0,0)',
          handleSize: '6',
          handleStyle: {
            color: 'rgba(255,255,255,0)'
          },
          brushStyle: {
            color: 'rgba(129, 243, 253)'
          }
        },
        { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
          type: 'inside',
          yAxisIndex: 0,
          zoomOnMouseWheel: false, // 滚轮是否触发缩放
          moveOnMouseMove: true, // 鼠标滚轮触发滚动
          moveOnMouseWheel: true
        }
      ],
      backgroundColor: '',
      color: ['#2adecf'],
      textStyle: {
        color: 'rgb(222,222,222)'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      yAxis: [{
        data: dataSouceY.value,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          interval: 0
        }
      },
      {
        data: dataSouceX.value,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          interval: 0
        }
      }
      ],
      xAxis: [{
        type: 'value',
        inverse: 'true',
        name: '',
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false,
          interval: 0
        }
      }],
      series: [{
        name: '',
        type: 'bar',
        barGap: '10%',
        barCategoryGap: '60%',
        barWidth: 17,
        itemStyle: {
          normal: {
            barBorderRadius: [30, 30, 30, 30],
            color: new echarts.graphic.LinearGradient(
              1, 0, 0, 0, [{
                offset: 0,
                color: '#90e6ff'
              },
              {
                offset: 1,
                color: '#6eb2fe'
              }
              ]
            )
          }
        },
        data: [
          -80,
          -11,
          -21,
          -32,
          -54,
          -65,
          -76,
          -85,
          -96,
          -100
        ],
        zlevel: 11
      }]
    }

    const showLoading = ref(false)

    onMounted(() => {
      const chartDom = document.getElementById('main')
      echarts.init(chartDom, 'dark').dispose()
      const myChart = echarts.init(chartDom, 'dark')

      const drawChart = () => {
        option.value = {
          dataZoom: [
            {
              show: true, // 为true 滚动条出现
              realtime: true,
              type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
              startValue: dataZoomMove.start, // 表示默认展示20%～80%这一段。
              endValue: dataZoomMove.end,
              width: 6,
              right: '20',
              top: '20%', // 位置和grid配置注意下
              height: '56%',
              yAxisIndex: [0, 1], // 关联多个y轴
              moveHandleStyle: {
                color: 'rgba(89, 202, 241,.5)'
              },
              moveHandleSize: '6',
              emphasis: {
                moveHandleStyle: {
                  color: 'rgba(89, 202, 241,.5)'
                }
              },
              textStyle: {
                color: 'rgba(255,255,255,0)'
              },
              backgroundColor: 'rgba(255,255,255,.1)',
              borderColor: 'rgba(255,255,255,0)',
              fillerColor: 'rgba(0,0,0,0)',
              handleSize: '6',
              handleStyle: {
                color: 'rgba(255,255,255,0)'
              },
              brushStyle: {
                color: 'rgba(129, 243, 253)'
              }
            },
            { // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
              type: 'inside',
              yAxisIndex: 0,
              zoomOnMouseWheel: false, // 滚轮是否触发缩放
              moveOnMouseMove: true, // 鼠标滚轮触发滚动
              moveOnMouseWheel: true
            }
          ],
          backgroundColor: '',
          color: ['#2adecf'],
          textStyle: {
            color: 'rgb(222,222,222)'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '3%',
            top: '10%',
            containLabel: true
          },
          yAxis: [{
            data: dataSouceY.value,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              interval: 0
            }
          },
          {
            data: dataSouceX.value,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              interval: 0
            }
          }
          ],
          xAxis: [{
            type: 'value',
            inverse: 'true',
            name: '',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false,
              interval: 0
            }
          }],
          series: [{
            name: '',
            type: 'bar',
            barGap: '10%',
            barCategoryGap: '60%',
            itemStyle: {
              normal: {
                barBorderRadius: [30, 30, 30, 30],
                color: new echarts.graphic.LinearGradient(
                  1, 0, 0, 0, [{
                    offset: 0,
                    color: '#90e6ff'
                  },
                  {
                    offset: 1,
                    color: '#6eb2fe'
                  }
                  ]
                )
              }
            },
            data: dataSouceX.value,
            zlevel: 11
          }]
        }
      }
      // 初始化图表
      myChart.setOption(option)

      // 监听 props.RSSI 的变化，当 props.a 发生变化时，更新柱状图数据
      watch(() => props.RSSI, (newValue, oldValue) => {
        showLoading.value = (newValue.x.length === 0) || (newValue.y.length === 0)
        // 在这里根据新的 props.a 值更新柱状图数据
        dataSouceX.value = newValue.x
        dataSouceY.value = newValue.y

        drawChart()

        // 更新图表配置
        myChart.setOption(option.value)
      })
      // 自动轮播和鼠标移入移出的停止和开启
      let dataZoomMoveTimer = null
      const startMoveDataZoom = (myChart, dataZoomMove) => {
        dataZoomMoveTimer = setInterval(() => {
          dataZoomMove.start += 1
          dataZoomMove.end += 1
          if (dataZoomMove.end > dataSouceY.value.length - 1) {
            dataZoomMove.start = 0
            dataZoomMove.end = 4
          }
          myChart.setOption({
            dataZoom: [
              {
                type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示通信距离顶端36px，一般就是在图上面。
                startValue: dataZoomMove.start,
                endValue: dataZoomMove.end
              }
            ]
          })
        }, 1000)
      }
      startMoveDataZoom(myChart, dataZoomMove)
      const chartDom2 = myChart.getDom()
      chartDom2.addEventListener('mouseout', () => {
        if (dataZoomMoveTimer) return
        const dataZoomMove_get = myChart.getOption().dataZoom[0]
        dataZoomMove.start = dataZoomMove_get.startValue
        dataZoomMove.end = dataZoomMove_get.endValue
        startMoveDataZoom(myChart, dataZoomMove)
      })
      // 移入
      // myChart.on
      chartDom2.addEventListener('mouseover', () => {
        clearInterval(dataZoomMoveTimer)
        dataZoomMoveTimer = undefined
      })
    })
    return {
      showLoading
    }
  }
})
</script>
