<template>
  <a-card title="AT调试">
    <a-form :model="model" name="nest-messages" layout="horizontal" @finish="confirm" :validate-messages="validate">

      <a-space>

        <label v-if="AuthUi.AT调试.statusW">命令</label>
        <a-input v-model:value="model.configData" v-if="AuthUi.AT调试.statusW"/>

        <a-button type="primary" html-type="submit" v-if="AuthUi.AT调试.statusW">确定</a-button>

        <a-button type="primary" @click="clearResponse">清除</a-button>
      </a-space>
      <a-row size="9">执行结果:</a-row>

      <a-textarea id="resp" v-model:value="response" :rows="15"></a-textarea>

    </a-form>

  </a-card>
</template>

<script>

import { onMounted, defineComponent, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
// import { QuestionOutlined } from '@ant-design/icons-vue'
// import { RequestAction } from '@/views/deviceinfo/action/requestAction'
import { AtCommandTestRequestAction } from '@/views/deviceinfo/action/atCommandTestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
// import Deviceinfo from '@/views/deviceinfo/model/modelTest'
// import { AtCommand } from '@/views/deviceinfo/model/AtCommand'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Router from '@/router/index'
import Pattern from '@/common/pattern'
import { AuthUi } from '@/views/deviceinfo/constant/authui'

import {
  WfModeOptions
} from '@/views/deviceinfo/constant/options'

export default defineComponent({

  setup () {
    // 对应后台数据表
    const model = reactive(new Deviceinfo())
    const model1 = reactive(new Deviceinfo())
    model1.configData = 'at^0'
    // const model = reactive(new Deviceinfo())
    // const atcmd = reactive(new AtCommand())
    model.configData = 'at^'

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const baseRequestData1 = reactive(new BaseRequestData(baseParam, model1))
    // const requestAction = new RequestAction()
    const atCommandModeRequestAction = new AtCommandTestRequestAction()
    const pageDirectAction = new PageDirectAction()
    const response = ref('')

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
      console.info(error)
    }

    /* const textchange = () => {
      const textarea = document.getElementById('resp')
      textarea.scrollTop = textarea.scrollHeight
    } */

    setInterval(() => {
      const textarea = document.getElementById('resp')
      textarea.scrollTop = textarea.scrollHeight
    }, 1000)

    // 定时获取设备调试信息

    setInterval(() => {
      if ((Router.currentRoute.value.fullPath).slice(0, 8) === '/mesh_at') {
      /* set() */ atCommandModeRequestAction.set(
          baseRequestData1,
          setSuccess,
          callbackError,
          null
        )
      }
    }, 6000)

    const confirm = (record) => {
      /* Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () { */
      return new Promise((resolve, reject) => {
        resolve()
        set(record)
        // model.configData = 'at^0'
      }).catch(() => console.log('Oops errors!'))
      // }
      // })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        // message.success('接入模式设置成功!')
        if (data.data.configData !== '"msg":no msgs') {
          response.value += data.data.configData + '\n'
        }
        console.info(response.value)
      } else {
        // message.success(`接入模式设置失败!${data.error_code}`)
      }
      // setAllData()
    }

    // 同步更新全局参数

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      atCommandModeRequestAction.set(
        baseRequestData,
        setSuccess,
        callbackError,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前接入模式参数 *******************
    /* const getAccessMode = () => {
      accessModeRequestAction.query(baseRequestData, getAccessModeSuccess, callbackError, null)
    }

    const getAccessModeSuccess = (data) => {
      data.data.configData.Origin = 0
      model.configData = data.data.configData
      // this.model = data.data
      console.info('ok_1')
    } */
    // ****************** 设备当前接入模式参数 *******************

    onMounted(() => {
      model.id = pageDirectAction.getCurrentRouteValue()
      model1.id = pageDirectAction.getCurrentRouteValue()
      // getAccessMode()
      // getDeviceinfoById()
    })

    /* const onFinish = (values) => {
      console.log('Success:', values)
    } */

    const clearResponse = () => {
      response.value = ''
      console.info(response.value)
    }

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      WfModeOptions,
      AuthUi,
      confirm,
      Pattern,
      clearResponse,
      response
    }
  }
})
</script>
