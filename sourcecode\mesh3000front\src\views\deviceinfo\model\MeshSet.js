class MeshSet {
  constructor () {
    // 设置固定功率开关
    this.powerMode = 1
    // *设置跳频功能开关. 跳频功能开关0：关闭 1：打开
    this.hoppingFre = null
    /**
     * 主路中心频率
     */
    this.pcenterFreq = null
    /**
      * 辅路中心频率
      */
    this.senterFreq = null
    // *主路带宽：0:1.4M 1:3M 2:5M 3:10M 4:15M(不支持) 5:20M
    this.pbandWidth = null
    // *辅路带宽：0:1.4M 1:3M 2:5M 3:10M 4:15M(不支持) 5:20M
    this.sbandWidth = null
    // *功率: 节点发射功率，单位dBm，范围“-40“到“50“
    this.txPower = null
    // *工作模式：integer type，设置节点的工作模式:
    // 0：正常模式;可以不需要携带后面的参数
    // 1：监听模式，处于该模式下节点只接收组播/广播消息，不占用固定资源；
    this.workMode = null
    // *中继 : integer type, 在监听模式情况下是否作为中继节点
    // 0：不作为中继节点;1：可作为中继节点
    this.trunk = null
    // *注册周期：监听节点从主控节点注册的时长，单位为s，取值范围[0,65535]，
    // 如果不携带或者0，则使用默认值10s
    this.registerPeriod = null
    // *传输距离等级：0：近距离 1：预留 2：远距离（100km）
    this.distance = null
    // *密钥：HEX字符串，64个字符，string type, in HEX format, 数据最长支持32字节（HEX字符串64个字符）输入的参数配置长度必须为偶数
    this.meshKey = null
    // *同步全局参数 0 本节点，1全局
    this.syngp = null
    // * 白名单总个数：取值范围[0,32]
    this.whiteList = null
    // *请求结果 0：所有成功 1：设置跳频失败 2：设置功率失败 3：设置监听模式失败 4：设置白名单失败 5：设置距离失败
    this.request = null
    /*
     * PCC预存频点总数和频点内容
     */
    this.Pcc = null
    this.Pccfreq = null
    /*
    * Scc预存频点总数和频点内容
    * */
    this.Scc = null
    this.Sccfreq = null
    /**
     * CA工作模式，
     * 0 非ca 非 MIMO
     * 1 支持CA
     * 2 支持MIMO
     * 3 支持CA+MIMO
     */
    this.camode = null

    /**
      * 设置工作模式是本地还是全网
      * 0 本地
      * 1 全网
      */
    this.catype = null
    /**
     * 修改CA
     */
    this.changeAC = false
    /**
     * 辅路功率
     */
    this.stxPower = null
    /**
     * 主路功率
     */
    this.ptxPower = null
    /**
     * 修改跳频开关状态
     */
    this.changeHoppFreq = false
    /**
     * 修改中心频率、带宽
     */
    this.changeCenter = false
    /**
     * 修改监听模式
     */
    this.changeListen = false
    /**
     * 修改输出功率
     */
    this.changePPower = false
    /**
     * 修改辅路输出功率
     */
    this.changeSPower = false
    /**
     * 修改注册间隔、距离、空口秘钥.
     */
    this.changeRegister = false
  }

  init () {
    this.powerMode = 1
    this.hoppingFre = null
    /**
     * 主路中心频率
     */
    this.pcenterFreq = null
    /**
     * 辅路中心频率
     */
    this.senterFreq = null

    // *主路带宽：0:1.4M 1:3M 2:5M 3:10M 4:15M(不支持) 5:20M
    this.pbandWidth = null
    // *辅路带宽：0:1.4M 1:3M 2:5M 3:10M 4:15M(不支持) 5:20M
    this.sbandWidth = null

    this.txPower = null

    this.workMode = null

    this.trunk = null

    this.registerPeriod = null

    this.distance = null

    this.meshKey = null

    this.syngp = null

    this.whiteList = null

    this.request = null

    this.Pcc = null

    this.Pccfreq = null

    this.Scc = null

    this.Sccfreq = null
    /**
     * 辅路功率
     */
    this.stxPower = null
    /**
     * 主路功率
     */
    this.ptxPower = null

    /**
     * 修改跳频开关状态
     */
    this.changeHoppFreq = false
    /**
      * 修改中心频率、带宽
      */
    this.changeCenter = false
    /**
     * 修改输出功率
     */
    this.changePower = false
    /**
      * 修改监听模式
      */
    this.changeListen = false
    /**
      * 修改注册间隔、距离、空口秘钥
      */
    this.changeRegister = false
  }
}

export { MeshSet }
