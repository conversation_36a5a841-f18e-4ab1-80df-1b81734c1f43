export const AuthList = [{
  id: 0,
  title: '设备管理',
  key: '0',
  children: [{
    id: 1,
    title: '网络拓扑',
    key: '0-0',
    children: [{
      name: '网络拓扑',
      title: '读权限',
      key: '0-0-0',
      url: '/api/deviceinfo/config/topo_draw'
    }]
  },
  {
    id: 2,
    title: '设备维护',
    key: '0-1',
    children: [{
      title: 'WiFi',
      key: '0-1-0',
      children: [{
        name: 'WiFi',
        title: '读权限',
        key: '0-1-0-0',
        url: '/api/deviceinfo/config/wifi'
      }, {
        name: 'WiFi',
        title: '写权限',
        key: '0-1-0-1',
        url: '/api/deviceinfo/config/wifi'
      }]
    },
    {
      title: 'DHCP',
      key: '0-1-1',
      children: [{
        name: 'DHC<PERSON>',
        title: '读权限',
        key: '0-1-1-0',
        url: '/api/deviceinfo/config/dhcp'
      }, {
        name: 'DHC<PERSON>',
        title: '写权限',
        key: '0-1-1-1',
        url: '/api/deviceinfo/config/dhcp'
      }]
    },
    {
      title: '防火墙',
      key: '0-1-2',
      children: [{
        name: '防火墙',
        title: '读权限',
        key: '0-1-2-0',
        url: '/api/deviceinfo/config/firewall'
      }, {
        name: '防火墙',
        title: '写权限',
        key: '0-1-2-1',
        url: '/api/deviceinfo/config/firewall'
      }]
    },
    {
      title: 'Uart0',
      key: '0-1-3',
      children: [{
        name: 'Uart0',
        title: '读权限',
        key: '0-1-3-0',
        url: '/api/deviceinfo/config/serial'
      }, {
        name: 'Uart0',
        title: '写权限',
        key: '0-1-3-1',
        url: '/api/deviceinfo/config/serial'
      }]
    },
    {
      title: 'Comart',
      key: '0-1-4',
      children: [{
        name: 'Comart',
        title: '读权限',
        key: '0-1-4-0',
        url: '/api/deviceinfo/config/comart'
      }, {
        name: 'Comart',
        title: '写权限',
        key: '0-1-4-1',
        url: '/api/deviceinfo/config/comart'
      }]
    },
    {
      title: '接入模式',
      key: '0-1-5',
      children: [{
        name: '接入模式',
        title: '读权限',
        key: '0-1-5-0',
        url: '/api/deviceinfo/config/wfmode'
      }, {
        name: '接入模式',
        title: '写权限',
        key: '0-1-5-1',
        url: '/api/deviceinfo/config/wfmode'
      }]
    },
    {
      title: '语音',
      key: '0-1-6',
      children: [{
        name: '语音',
        title: '读权限',
        key: '0-1-6-0',
        url: '/api/deviceinfo/config/voice'
      }, {
        name: '语音',
        title: '写权限',
        key: '0-1-6-1',
        url: '/api/deviceinfo/config/voice'
      }]
    },
    {
      title: '温度设置',
      key: '0-1-7',
      children: [{
        name: '温度设置',
        title: '读权限',
        key: '0-1-7-0',
        url: '/api/deviceinfo/config/health'
      }, {
        name: '温度设置',
        title: '写权限',
        key: '0-1-7-1',
        url: '/api/deviceinfo/config/health'
      }]
    },
    {
      title: '设备版本信息',
      key: '0-1-8',
      children: [{
        name: '设备版本信息',
        title: '读权限',
        key: '0-1-8-0',
        url: '/api/deviceinfo/config/deviceversion'
      }]
    },
    {
      title: 'Mesh设备信息',
      key: '0-1-9',
      children: [{
        name: 'Mesh设备信息',
        title: '读权限',
        key: '0-1-9-0',
        url: '/api/deviceinfo/config/meshdevinfo'
      }, {
        name: 'Mesh设备信息',
        title: '写权限',
        key: '0-1-9-1',
        url: '/api/deviceinfo/config/meshdevinfo'
      }]
    },
    {
      title: 'Mesh设置',
      key: '0-1-A',
      children: [{
        name: 'Mesh设置',
        title: '读权限',
        key: '0-1-A-0',
        url: '/api/deviceinfo/config/meshconfig'
      }, {
        name: 'Mesh设置',
        title: '写权限',
        key: '0-1-A-1',
        url: '/api/deviceinfo/config/meshconfig'
      }]
    },
    {
      title: 'AT调试',
      key: '0-1-D',
      children: [{
        name: 'AT调试',
        title: '读权限',
        key: '0-1-D-0',
        url: '/api/deviceinfo/config/atest'
      }, {
        name: 'AT调试',
        title: '写权限',
        key: '0-1-D-1',
        url: '/api/deviceinfo/config/atest'
      }]
    },
    {
      title: '视频参数设置',
      key: '0-1-B',
      children: [{
        name: '视频参数设置',
        title: '读权限',
        key: '0-1-B-0',
        url: '/api/deviceinfo/config/videoctr'
      }, {
        name: '视频参数设置',
        title: '写权限',
        key: '0-1-B-1',
        url: '/api/deviceinfo/config/videoctr'
      }]
    },
    {
      title: 'HDMI视频播放',
      key: '0-1-C',
      children: [{
        name: 'HDMI视频播放',
        title: '读权限',
        key: '0-1-C-0',
        url: '/api/deviceinfo/config/video'
      }]
    },
    {
      title: '蓝牙设置',
      key: '0-1-E',
      children: [{
        name: '蓝牙设置',
        title: '读权限',
        key: '0-1-E-0',
        url: '/api/deviceinfo/config/bluetooth_set'
      }, {
        name: '蓝牙设置',
        title: '写权限',
        key: '0-1-E-1',
        url: '/api/deviceinfo/config/bluetooth_set'
      }]
    },
    {
      title: 'GPS设置',
      key: '0-1-F',
      children: [{
        name: 'GPS设置',
        title: '读权限',
        key: '0-1-F-0',
        url: '/api/deviceinfo/config/bluetooth_set'
      }, {
        name: 'GPS设置',
        title: '写权限',
        key: '0-1-F-1',
        url: '/api/deviceinfo/config/bluetooth_set'
      }]
    }]
  },
  {
    title: '设备日志',
    key: '0-2',
    children: [{
      name: '设备日志',
      title: '读权限',
      key: '0-2-0',
      url: '/meshdevlog'
    }]
  },
  {
    title: '扫频',
    key: '0-3',
    children: [{
      name: '扫频',
      title: '读权限',
      key: '0-3-0',
      url: '/sweep_set'
    }]
  }]
},
{
  title: '视频播放',
  key: '1',
  children: [{
    name: '视频播放',
    title: '读权限',
    key: '1-0',
    url: '/api/deviceinfo/config/camera'
    // disabled: true
  }, {
    name: '视频播放',
    title: '写权限',
    key: '1-1',
    url: '/api/deviceinfo/config/camera'
  }]
},
// 说明：页面未开发完成
{
  title: '海康视频播放',
  key: '2',
  children: [{
    name: '海康视频播放',
    title: '读权限',
    key: '2-0',
    url: '/api/deviceinfo/config/hk_video'
    // disabled: true
  }, {
    name: '海康视频播放',
    title: '写权限',
    key: '2-1',
    url: '/api/deviceinfo/config/hk_video'
  }]
},
{
  title: '系统设置',
  key: '3',
  children: [{
    name: '系统设置',
    title: '读权限',
    key: '3-0',
    url: 'sys/config'
    // disabled: true
  }, {
    name: '系统设置',
    title: '写权限',
    key: '3-1',
    url: 'sys/config'
  }]
// },
// {
//   title: '用户管理',
//   key: '4',
//   children: [{
//     name: '用户管理',
//     title: '读权限',
//     key: '4-0',
//     url: '' // url为空
//     // disabled: true
//   }, {
//     name: '用户管理',
//     title: '写权限',
//     key: '4-1',
//     url: '' // url为空
//   }]
// },
// {
//   title: '角色管理',
//   key: '5',
//   children: [{
//     name: '角色管理',
//     title: '读权限',
//     key: '5-0',
//     url: '' // url为空
//     // disabled: true
//   }, {
//     name: '角色管理',
//     title: '写权限',
//     key: '5-1',
//     url: '' // url为空
//   }]
}
]
