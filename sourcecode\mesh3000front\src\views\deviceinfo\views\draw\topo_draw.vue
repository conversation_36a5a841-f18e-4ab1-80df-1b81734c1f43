<template>
    <div style="position: fixed; width: 100%; height: 100%; background-color: #162130ef;">
        <div id="myChart" style="width:99%;height: 99%;position : absolute"></div>
    </div>
  <a-card hoverable style="width: 350px;background-color:#1e3a476b" title="刷新设置">
    <a-row justify="space-around">
      <a-col>
        <label>
          自动刷新：
        </label>
        <a-switch v-model:checked="checked1" checked-children="开" un-checked-children="关" @change="switchChange()" />
        <a-switch v-if = "false" v-model:checked="checked2" checked-children="开" un-checked-children="关" @change="switchStyle()" />
      </a-col>

      <a-button type="primary" @click="getTopo()">手动刷新</a-button>
    </a-row>
    <a-divider />
    <a-row>
      <a-input-group compact>
        <a-input placeholder="请输入拓扑刷新间隔" v-model:value="repeat_count" style="width: 200px" />
        <a-button type="primary" @click="enter_repeat_count(repeat_count)">确定</a-button>
      </a-input-group>
    </a-row>

  </a-card>
  <!-- <a-space>
                        <a-button type="primary" @click="draw()">draw</a-button>
                      </a-space>
                      <a-space>
                        <a-button type="primary" @click="drawClear()">clear</a-button>
                      </a-space> -->
  <!-- <a-space>
                        <a-button type="primary" @click="draw_repeat()">draw_repeat</a-button>
                      </a-space> -->
  <!-- <a-space>
                        <a-button type="primary" @click="clear_repeat()">clear_repeat</a-button>
                      </a-space> -->

  <a-drawer id="drawer1" height="30%" title="设备详细信息" placement="bottom" :visible="bottom_visible" @close="detail_onClose">
    <div id="drawer" >
      <a-descriptions :bordered="true" :column=5>
        <a-descriptions-item label="设备类型" style="background-color: #162130ef;">{{drawer_data.device_type}}</a-descriptions-item>
        <a-descriptions-item label="MAC地址" style="background-color: #162130ef;">{{drawer_data.mac_addr}}</a-descriptions-item>
        <a-descriptions-item label="工作模式" style="background-color: #162130ef;">{{drawer_data.work_mode}}</a-descriptions-item>
        <a-descriptions-item label="电池电压" style="background-color: #162130ef;">{{drawer_data.voltage}}</a-descriptions-item>
        <a-descriptions-item label="剩余电量" style="background-color: #162130ef;" :span="2">{{drawer_data.remaining_battery}}</a-descriptions-item>
        <a-descriptions-item label="电池温度" style="background-color: #162130ef;">{{drawer_data.batterytemperature}}</a-descriptions-item>
        <a-descriptions-item label="主板温度" style="background-color: #162130ef;">{{drawer_data.hosttemperature}}</a-descriptions-item>
        <a-descriptions-item label="节点ID" style="background-color: #162130ef;">{{drawer_data.id}}</a-descriptions-item>
        <a-descriptions-item label="经纬度" style="background-color: #162130ef;">{{drawer_data.longitude}},{{drawer_data.latitude}}</a-descriptions-item>
      </a-descriptions>

    </div>
  </a-drawer>

</template>

<script>
import { onMounted, onUnmounted, defineComponent, ref, reactive, toRefs } from 'vue'
// import { Modal, message } from 'ant-design-vue'
import { message } from 'ant-design-vue'
// import { QuestionOutlined } from '@ant-design/icons-vue'
import { RequestAction } from '@/views/deviceinfo/action/requestAction'
// import { DhcpRequestAction } from '@/views/deviceinfo/action/dhcpRequestAction'
import { TopoRequestAction } from '@/views/deviceinfo/action/topoRequestAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { Deviceinfo } from '@/views/deviceinfo/model/model'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import {
  DebugModeOption
} from '@/views/deviceinfo/constant/options'

import * as echarts from 'echarts'
import { TitleComponent, TooltipComponent } from 'echarts/components'
import { GraphChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'

export default defineComponent({
  name: 'Topo_draw',
  setup () {
    echarts.use([TitleComponent, TooltipComponent, GraphChart, CanvasRenderer])
    // 对应后台数据表
    let model = reactive(new Deviceinfo())
    // const model = reactive(new Deviceinfo())
    model.configData = 'json'
    let repeat_count = 5

    // 保存查询的数据库数据
    const dataSource = ref({})
    const baseParam = reactive(new BaseParam())
    const baseRequestData = reactive(new BaseRequestData(baseParam, model))
    const requestAction = new RequestAction()
    const topoRequestAction = new TopoRequestAction()
    const pageDirectAction = new PageDirectAction()
    const ico_red = require('@/assets/img/red.png')
    const ico_green = require('@/assets/img/green.png')
    const ico_green_sim = require('@/assets/img/green_sim.png')
    const ico_blue = require('@/assets/img/blue.png')
    const state = reactive({
      checked1: false,
      checked2: false,
      checked3: false
    })
    const bottom_visible = ref(false)

    const drawer_data = ref({})
    let myChart = null
    let option = null
    let node_count = 0
    let onLine_node_count = 0
    let node_x = 0
    let node_y = 0
    const radis = 200
    const left_margin = 60
    const top_margin = 60
    let nodes = []
    // let json = {
    //   content: [{ name: 'Node 1' }, { name: 'Node 2' }, { name: 'Node 3' }, { name: 'Node 4' },
    //     { name: 'Node 5' }, { name: 'Node 6' }, { name: 'Node 7' }, { name: 'Node 8' }]
    // }
    let s1_nodes = []
    let s2_nodes = []
    let s_nodes = []
    let s_links = []
    let snNameList = []
    let repeat = 0
    let simulate_nodes = 0
    let simulate_style = 0
    let font_size = 25
    // let line_style

    // const echart = new Echarts()

    const enter_repeat_count = (data) => {
      console.info('enter_repeat_count:' + data)
      repeat_count = data
      clear_repeat()
      draw_repeat()
    }

    // 回调函数错误处理
    const callbackError = (error) => {
      message.error(error.message, 3)
    }
    // ****************** 根据主键查询数据 *******************
    const getDeviceinfoByIdSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        model = data.data
        // getDhcp()
        preDraw()
        getTopo()
      }
    }

    const getDeviceinfoByIdFinally = () => {
      console.info('OK')
    }

    const getDeviceinfoById = () => {
      requestAction.getOne(baseRequestData, getDeviceinfoByIdSuccess, callbackError, getDeviceinfoByIdFinally)
    }

    const getTopo = () => {
      // requestAction.getConfig(baseRequestData.value, getDhcpSuccess, callbackError, null
      if (simulate_nodes > 0) {
        topoRequestAction.query(baseRequestData, getSimTopoSuccess, callbackError, null)
      } else {
        topoRequestAction.query(baseRequestData, getTopoSuccess, callbackError, null)
      }
    }

    const getList = () => {
      requestAction.getList(baseRequestData, getListSuccess, callbackError, getListFinally)
    }

    const getListFinally = () => {
      console.log('getListFinally')
    }

    const getListSuccess = (data) => {
      console.log(data.data.content[0])

      model.id = data.data.content[0].id
      console.info('model.id:' + model.id)
      getDeviceinfoById()
    }

    const getNameBySn = (data) => {
      let name = null
      snNameList.forEach((item, index, arr) => {
        if (item.sn === data) {
          name = item.device_name
        }
      })
      return name
    }

    const getSimTopoSuccess = (data) => {
      let obj = data.data.configData
      console.info(data.data.configData)
      nodes = []
      s_nodes = []
      s1_nodes = []
      s2_nodes = []
      s_links = []
      snNameList = []
      onLine_node_count = 0
      let t_lineStyle = {}
      let t_color = '#000066'
      let t_width = 2
      let i
      let snr

      t_lineStyle = {
        width: t_width,
        curveness: 0.2,
        color: t_color
      }
      let number = 0

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].sn != null) {
          const strtmp = obj[key].sn
          let str = ''
          for (let i = 0; i < strtmp.length; i++) {
            if ((strtmp[i] >= '0') && (strtmp[i] <= '9')) {
              str += strtmp[i]
            }
          }
          obj[key].sn = str
          snNameList.push({
            sn: obj[key].sn,
            device_name: obj[key].device_name
          })
          number++
          if (number <= simulate_nodes) {
            if (obj[key].in_network === 1) {
              s1_nodes.push(obj[key])
            } else {
              obj[key].in_network = 1
              if (simulate_style === 0) {
                obj[key].relevantParameters.AdjacentNodeNumber = 31
              } else {
                obj[key].relevantParameters.AdjacentNodeNumber = 2
              }
              obj[key].pcenter_freq = 14900
              obj[key].scenter_freq = 15200
              s2_nodes.push(obj[key])
            }
          }
        }
      })
      if (simulate_nodes > number) {
        const short_node = simulate_nodes - number
        let node = {}
        let parts

        for (i = 0; i < short_node; i++) {
          if (i < s1_nodes.length) {
            // node = s1_nodes[i]
            if (s1_nodes.length === 0) {
              node = JSON.parse(JSON.stringify(s2_nodes[i]))
            } else {
              node = JSON.parse(JSON.stringify(s1_nodes[i]))
            }
          } else {
            if (s1_nodes.length === 0) {
              node = JSON.parse(JSON.stringify(s2_nodes[i]))
            } else {
              node = JSON.parse(JSON.stringify(s1_nodes[s1_nodes.length - 1]))
            }
          }
          node.in_network = 1
          node.sn = '0' + String(912300 + i)
          node.device_name = '测试' + String(i + 1)
          node.id = String(i + 800)
          parts = node.ip_addr.split('.')
          console.log(parts)
          node.ip_addr = parts[0] + '.' + parts[1] + '.' + parts[2] + '.' + String(100 + i)
          if (simulate_style === 0) {
            node.relevantParameters.AdjacentNodeNumber = 31
          } else {
            node.relevantParameters.AdjacentNodeNumber = 2
          }
          node.voltage = (120 + Math.floor(Math.random() * 39)) / 10
          node.pcenter_freq = 14900
          node.scenter_freq = 15200
          s2_nodes.push(node)
          snNameList.push({
            sn: node.sn,
            device_name: node.device_name
          })
        }
      }
      s_nodes = s1_nodes.concat(s2_nodes)
      // 划在线节点连接线
      obj = JSON.parse(JSON.stringify(s1_nodes))
      Object.keys(obj).forEach((key) => {
        console.info('obj[' + key + ']')
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].in_network === 1) {
          onLine_node_count++
          console.info('sn[' + obj[key].sn + '] 在线！')
          // 寻找邻接节点，并放到nodes队列
          const adjacentNode = obj[key].adjacentNodeSNR
          if (adjacentNode.AdjacentNodeNumber > 0) {
            // for(let i=0;i<adjacentNode.)
            adjacentNode.linkCharacteristics.forEach((item, index, arr) => {
              console.info(item)
              snr = (item.ANT1SNR + item.ANT2SNR) / 2
              if (snr > 20) {
                t_color = 'green'
              } else if (snr < 10) {
                t_color = 'red'
              } else {
                t_color = 'orange'
              }

              if (item.Distance > 50) {
                t_width = 2
              } else {
                t_width = 4
              }

              t_lineStyle = {
                width: t_width,
                curveness: 0.2,
                color: t_color
              }
              // console.info('id: ' + item.id + ' sn: ' + item.sn)
              if (item.AdjacentNodeSn != null) {
                // 离线节点不画连接线
                let if_push = true
                s2_nodes.forEach((item1, index, arr) => {
                  if (item1.sn === item.AdjacentNodeSn) {
                    if_push = false
                  }
                })

                if (if_push === true) {
                  s_links.push({
                  // source: item.SN,
                  // target: item.AdjacentNodeSn,
                    source: getNameBySn(item.SN),
                    target: getNameBySn(item.AdjacentNodeSn),
                    symbolSize: [1, 10],
                    label: {
                      show: false
                    },
                    lineStyle: t_lineStyle,
                    s_data: item
                  })
                }
              }
            })
          }
        } else {
          console.info('sn[' + obj[key].sn + '] 离线！')
        }
      })
      // 划离线和模拟节点连接线
      let s2_number = 0
      let pre_item = null
      obj = JSON.parse(JSON.stringify(s2_nodes))
      Object.keys(obj).forEach((key) => {
        console.info('obj[' + key + ']')
        console.info(obj[key])
        s2_number++
        // console.info(obj[key].sn)
        if (obj[key].in_network === 1) {
          onLine_node_count++
          console.info('sn[' + obj[key].sn + '] 在线！')
          t_color = 'green'
          t_width = 2
          t_lineStyle = {
            width: t_width,
            curveness: 0.2,
            color: t_color
          }

          if (simulate_style === 1) {
            obj[key].Distance = Math.floor(Math.random() * 100) + 20
            obj[key].SendingRate = Math.floor(Math.random() * 30) + 5
            obj[key].AirTime = Math.floor(Math.random() * 20)
            // item.BLER = Math.floor(Math.random() * 2)
            obj[key].BLER = 0
            obj[key].ANT1SNR = Math.floor(Math.random() * 10) + 20
            obj[key].ANT2SNR = obj[key].ANT1SNR + (Math.floor(Math.random() * 7) - 3)
            obj[key].ANT1RSSI = Math.floor(Math.random() * 10) - 69
            obj[key].ANT2RSSI = obj[key].ANT1RSSI + (Math.floor(Math.random() * 5) - 8)
            snr = (obj[key].ANT1SNR + obj[key].ANT2SNR) / 2
            if (snr > 20) {
              t_color = 'green'
            } else if (snr < 10) {
              t_color = 'red'
            } else {
              t_color = 'orange'
            }

            if (obj[key].Distance > 50) {
              t_width = 2
            } else {
              t_width = 4
            }

            t_lineStyle = {
              width: t_width,
              curveness: 0.2,
              color: t_color
            }
            if (pre_item !== null) {
              s_links.push({
                // source: item.SN,
                // target: item.AdjacentNodeSn,
                source: getNameBySn(obj[key].sn),
                target: getNameBySn(pre_item.sn),
                symbolSize: [1, 10],
                label: {
                  show: false
                },
                lineStyle: t_lineStyle,
                s_data: obj[key]
              })
              if (s2_number < s2_nodes.length) {
                s_links.push({
                // source: item.SN,
                // target: item.AdjacentNodeSn,
                  source: getNameBySn(obj[key].sn),
                  target: getNameBySn(obj[String(Number(key) + 1)].sn),
                  symbolSize: [1, 10],
                  label: {
                    show: true,
                    formatter: getNameBySn(obj[key].sn) + ' -> ' + getNameBySn(obj[String(Number(key) + 1)].sn)
                  },
                  lineStyle: t_lineStyle,
                  s_data: obj[key]
                })
              }
            } else {
              if (s2_number < s2_nodes.length) {
                s_links.push({
                // source: item.SN,
                // target: item.AdjacentNodeSn,
                  source: getNameBySn(obj[key].sn),
                  target: getNameBySn(obj[String(Number(key) + 1)].sn),
                  symbolSize: [1, 10],
                  label: {
                    show: true,
                    formatter: getNameBySn(obj[key].sn) + ' -> ' + getNameBySn(obj[String(Number(key) + 1)].sn)
                  },
                  lineStyle: t_lineStyle,
                  s_data: obj[key]
                })
              }
            }
            pre_item = obj[key]
          } else {
            s2_nodes.forEach((item, index, arr) => {
              if (item.sn !== obj[key].sn) {
                item.Distance = Math.floor(Math.random() * 100) + 20
                item.SendingRate = Math.floor(Math.random() * 30) + 5
                item.AirTime = Math.floor(Math.random() * 20)
                // item.BLER = Math.floor(Math.random() * 2)
                item.BLER = 0
                item.ANT1SNR = Math.floor(Math.random() * 10) + 20
                item.ANT2SNR = item.ANT1SNR + (Math.floor(Math.random() * 7) - 3)
                item.ANT1RSSI = Math.floor(Math.random() * 10) - 69
                item.ANT2RSSI = item.ANT1RSSI + (Math.floor(Math.random() * 5) - 8)
                snr = (item.ANT1SNR + item.ANT2SNR) / 2
                if (snr > 20) {
                  t_color = 'green'
                } else if (snr < 10) {
                  t_color = 'red'
                } else {
                  t_color = 'orange'
                }

                if (item.Distance > 50) {
                  t_width = 2
                } else {
                  t_width = 4
                }

                t_lineStyle = {
                  width: t_width,
                  curveness: 0.2,
                  color: t_color
                }
                if (simulate_style === 0) {
                  s_links.push({
                    // source: item.SN,
                    // target: item.AdjacentNodeSn,
                    source: getNameBySn(obj[key].sn),
                    target: getNameBySn(item.sn),
                    symbolSize: [1, 10],
                    label: {
                      show: true,
                      formatter: getNameBySn(obj[key].sn) + ' -> ' + getNameBySn(item.sn)
                    },
                    lineStyle: t_lineStyle,
                    s_data: item
                  })
                }
              }
            })
          }
        } else {
          console.info('sn[' + obj[key].sn + '] 离线！')
        }
      })
      console.info('s1_nodes: ' + JSON.stringify(s1_nodes))
      console.info('s_nodes: ' + JSON.stringify(s_nodes))
      console.info('s_links: ' + JSON.stringify(s_links))
      if (onLine_node_count > 16) {
        font_size = 10
      } else {
        font_size = 25
      }

      draw()
    }

    const getTopoSuccess = (data) => {
      const obj = data.data.configData
      console.info(data.data.configData)
      nodes = []
      s_nodes = []
      s1_nodes = []
      s2_nodes = []
      s_links = []
      snNameList = []
      onLine_node_count = 0
      let t_lineStyle = {}
      let t_color = '#000066'
      let t_width = 2

      t_lineStyle = {
        width: t_width,
        curveness: 0.2,
        color: t_color
      }

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].sn != null) {
          const strtmp = obj[key].sn
          let str = ''
          for (let i = 0; i < strtmp.length; i++) {
            if ((strtmp[i] >= '0') && (strtmp[i] <= '9')) {
              str += strtmp[i]
            }
          }
          obj[key].sn = str
          snNameList.push({
            sn: obj[key].sn,
            device_name: obj[key].device_name
          })
          if (obj[key].in_network === 1) {
            s1_nodes.push(obj[key])
          } else {
            s2_nodes.push(obj[key])
          }
        }
      })

      s_nodes = s1_nodes.concat(s2_nodes)

      Object.keys(obj).forEach((key) => {
        console.info(obj[key])
        // console.info(obj[key].sn)
        if (obj[key].in_network === 1) {
          onLine_node_count++
          console.info('sn[' + obj[key].sn + '] 在线！')
          // 寻找邻接节点，并放到nodes队列
          const adjacentNode = obj[key].adjacentNodeSNR
          if (adjacentNode.AdjacentNodeNumber > 0) {
            // for(let i=0;i<adjacentNode.)
            adjacentNode.linkCharacteristics.forEach((item, index, arr) => {
              console.info(item)
              const snr = (item.ANT1SNR + item.ANT2SNR) / 2
              if (snr > 20) {
                t_color = 'green'
              } else if (snr < 10) {
                t_color = 'red'
              } else {
                t_color = 'orange'
              }

              if (item.Distance > 50) {
                t_width = 2
              } else {
                t_width = 4
              }

              t_lineStyle = {
                width: t_width,
                curveness: 0.2,
                color: t_color
              }
              // console.info('id: ' + item.id + ' sn: ' + item.sn)
              if (item.AdjacentNodeSn != null) {
                // 离线节点不画连接线
                let if_push = true
                s2_nodes.forEach((item1, index, arr) => {
                  if (item1.sn === item.AdjacentNodeSn) {
                    if_push = false
                  }
                })

                if (if_push === true) {
                  s_links.push({
                  // source: item.SN,
                  // target: item.AdjacentNodeSn,
                    source: getNameBySn(item.SN),
                    target: getNameBySn(item.AdjacentNodeSn),
                    symbolSize: [1, 10],
                    label: {
                      show: true,
                      formatter: function (params) {
                        return item.ANT1SNR + ' / ' + item.ANT2SNR
                      }
                    },
                    lineStyle: t_lineStyle,
                    s_data: item
                  })
                }
              }
            })
          }
        } else {
          console.info('sn[' + obj[key].sn + '] 离线！')
        }
      })

      console.info('s_nodes: ' + JSON.stringify(s_nodes))
      console.info('s_links: ' + JSON.stringify(s_links))
      if (onLine_node_count > 16) {
        font_size = 10
      } else {
        font_size = 25
      }
      draw()

      // 插入到nodes队列
      // model.configData.forEach((item, index, arr) => {
      //   console.info(item)
      //   // console.info('id: ' + item.id + ' sn: ' + item.sn)
      // })
    }

    const drawClear = () => {
      option = {

        series: [
          {

            data: null,
            links: []
          }
        ]
      }
      option && myChart.setOption(option)

      // myChart.setOption(option)
    }

    const preDraw = () => {
      myChart.setOption({
        title: {
        //   text: 'Mesh拓扑图'
        },
        animation: false,
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        dataZoom: [
          {
            type: 'inside',
            show: true
          }
        ],

        series: [
          {
            type: 'graph',
            layout: 'none',
            symbolSize: 50,
            roam: true,
            label: {
              show: true,
              position: 'bottom',
              formatter: '{b}',
              fontSize: font_size,
              fontWeight: 'bold',
              fontFamily: 'Arial',
              shadowColor: 'transparent',
              textShadowColor: 'transparent',
              color: 'white'
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            edgeLabel: {
              fontSize: 20,
              fontWeight: 'bold',
              fontFamily: 'Arial',
              shadowColor: 'transparent',
              textShadowColor: 'transparent',
              color: 'white'
            },
            data: null,
            links: [],
            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0.3
            }
          }
        ]
      })
    }

    const switchChange = () => {
      if (state.checked1) {
        draw_repeat()
      } else {
        clear_repeat()
        // draw()
      }
    }
    const switchStyle = () => {
      if (state.checked2) {
        simulate_style = 1
      } else {
        simulate_style = 0
      }
    }

    const draw_repeat = () => {
      // let count = 0
      repeat = setInterval(() => {
        getTopo()
        // count++
        // if (count >= 5)count = 0
      }, repeat_count * 1000)
    }

    const clear_repeat = () => {
      clearInterval(repeat)
    }

    window.goToDetail = function (data) {
      console.log('goToDetail: ' + data)
      bottom_visible.value = true
      s_nodes.forEach((item, index, arr) => {
        if (item.sn === data) {
          drawer_data.value = item
          if (item.device_type === 1) {
            drawer_data.value.device_type = '手持式设备'
          } else if (item.device_type === 2) {
            drawer_data.value.device_type = '背负式设备'
          } else if (item.device_type === 3) {
            drawer_data.value.device_type = '车载式设备'
          } else if (item.device_type === 4) {
            drawer_data.value.device_type = '机载式设备'
          }
          console.info(item)
        }
      })
    }

    const detail_onClose = () => {
      bottom_visible.value = false
    }

    const draw = () => {
      // 根据接口传回的拓扑数据绘制，需获得mesh设备数目，连接关系

      // nodes = []
      // node_count = json.content.length
      drawClear()
      node_count = s_nodes.length
      console.log('node_count: ' + node_count)
      // node_count = 8
      let u_angel = 0
      let angel = 0
      // let index_node = 0
      const angel_val = 360 / onLine_node_count
      let ico_url // 节点图标
      let net_state
      let band_width
      let sband_width
      let tmp_online = 0
      let tmp_offline = 0
      let angel3_count = 0
      // const angel4_count = 0

      // angel = ico_url
      // json.content.forEach((item, index, arr) => {

      s_nodes.forEach((item, index, arr) => {
        // item.name = 'Node ' + index
        // 判断网络状态，设定节点图标颜色

        if (item.in_network === 1) { // 在线
          if (onLine_node_count <= 16) {
            ico_url = 'image://' + ico_green
          } else {
            ico_url = 'image://' + ico_green_sim
          }
          net_state = '在线'

          if (onLine_node_count === 3) {
            u_angel = angel_val * angel3_count + 90
            if (u_angel === 90) {
              node_x = left_margin + radis
              node_y = top_margin
            }
            if ((u_angel > 180) && (u_angel < 270)) {
              angel = u_angel - 180

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            if ((u_angel > 270) && (u_angel < 360)) {
              angel = 360 - u_angel

              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            angel3_count++
          } else {
            u_angel = angel_val * tmp_online
            if (u_angel === 0) {
              node_x = left_margin
              node_y = top_margin + radis
            }
            if ((u_angel > 0) && (u_angel < 90)) {
              angel = u_angel
              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis * (1 - (Math.sin(angel * Math.PI / 180)))
            }
            if (u_angel === 90) {
              node_x = left_margin + radis
              node_y = top_margin
            }
            if ((u_angel > 90) && (u_angel < 180)) {
              angel = 180 - u_angel

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis * (1 - (Math.sin(angel * Math.PI / 180)))
            }
            if (u_angel === 180) {
              node_x = left_margin + radis + radis
              node_y = top_margin + radis
            }
            if ((u_angel > 180) && (u_angel < 270)) {
              angel = u_angel - 180

              node_x = left_margin + radis + radis * (Math.cos(angel * Math.PI / 180))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            if (u_angel === 270) {
              node_x = left_margin + radis
              node_y = top_margin + radis + radis
            }
            if ((u_angel > 270) && (u_angel < 360)) {
              angel = 360 - u_angel

              node_x = left_margin + radis * (1 - (Math.cos(angel * Math.PI / 180)))
              node_y = top_margin + radis + radis * (Math.sin(angel * Math.PI / 180))
            }
            tmp_online++
          }
        } else {
          node_x = left_margin + 3 * radis
          node_y = (tmp_offline + 1) * 60
          if (item.in_network === 0) { // 在线不在网
            ico_url = 'image://' + ico_blue
            net_state = '在线不在网'
          } else if ((item.in_network === 2) || (item.in_network === null)) { // 离线
            ico_url = 'image://' + ico_red
            net_state = '离线'
          }
          tmp_offline++
        }

        // 带宽值转换
        if (item.pband_width === 0) {
          band_width = '1.4'
        } else if (item.pband_width === 1) {
          band_width = '3'
        } else if (item.pband_width === 2) {
          band_width = '5'
        } else if (item.pband_width === 3) {
          band_width = '10'
        } else if (item.pband_width === 5) {
          band_width = '20'
        }
        if (item.sband_width === 0) {
          sband_width = '1.4'
        } else if (item.sband_width === 1) {
          sband_width = '3'
        } else if (item.sband_width === 2) {
          sband_width = '5'
        } else if (item.sband_width === 3) {
          sband_width = '10'
        } else if (item.sband_width === 5) {
          sband_width = '20'
        }
        // index_node = index + 1
        // console.log('nodes' + index_node + ' u_angel:' + u_angel + ' angel:' + angel + ' node_x:' + node_x + ' node_y:' + node_y)
        nodes.push({
          // name: item.sn,
          name: item.device_name,
          x: node_x,
          y: node_y,
          s_data: item,
          symbol: ico_url,
          netState: net_state,
          band_width: band_width,
          sband_width: sband_width,
          symbolSize: [50, 50],
          sn: item.sn
        })
      })

      option = {
        tooltip: {
          trigger: 'item', //
          triggerOn: 'mousemove', // 什么时候触发提示小图标，点击click的时候，或者鼠标滑过的时候，默认是mousemove鼠标滑过
          /* formatter可以以字符串模板方式写，也可以用回调函数写，不过字符串模板略有限制，我们使用回调函数会灵活点 */
          enterable: true,
          formatter: function (params) {
            let data = ''
            let res
            if (params.dataType === 'edge') {
              data = '方向：' + params.data.source + '-->' + params.data.target
              res = // 字符串形式的html标签会被echarts转换渲染成数据，这个res主要是画的tooltip里的上部分的标题部分
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                data +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '距离: ' + params.data.s_data.Distance + ' 米' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '数据发送速率: ' + params.data.s_data.SendingRate + ' Mbps' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '空中时间占比: ' + params.data.s_data.AirTime +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '误码百分比: ' + params.data.s_data.BLER + '% ' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '天线1/天线2信噪比: ' + params.data.s_data.ANT1SNR + '/' + params.data.s_data.ANT2SNR +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '信号强度1/信号强度2: ' + params.data.s_data.ANT1RSSI + '/' + params.data.s_data.ANT2RSSI +
                ' </p></div>'
            }
            if (params.dataType === 'node') {
              // data = params.data.name + ' x:' + params.data.x.toPrecision(8) + ' y:' + params.data.y.toPrecision(8)
              data = '设备名称: ' + params.data.s_data.device_name
              res = // 字符串形式的html标签会被echarts转换渲染成数据，这个res主要是画的tooltip里的上部分的标题部分
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                data + ' ' + `<button onclick="goToDetail('${params.data.sn}')">${params.data.sn}</button>` +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '节点ID: ' + params.data.s_data.id +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '网络状态: ' + params.data.netState +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                'IP地址: ' + params.data.s_data.ip_addr +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '网络ID: ' + params.data.s_data.net_id +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '电压: ' + params.data.s_data.voltage + ' V' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主中心频率: ' + params.data.s_data.pcenter_freq + ' Mhz' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主带宽: ' + params.data.band_width + ' M' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主功率: ' + params.data.s_data.ptx_power + ' dbm' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '辅功率: ' + params.data.s_data.stx_power + ' dbm' +
                ' </p></div>' +
                // "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                // '辅中心频率: ' + params.data.s_data.scenter_freq + ' Mhz' +
                // ' </p></div>' +
                // "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                // '辅带宽: ' + params.data.sband_width + ' M' +
                // ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '主功率: ' + params.data.s_data.ptx_power + ' dbm' +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:3px;'><p>" +
                '邻接点数量: ' + params.data.s_data.relevantParameters.AdjacentNodeNumber +
                ' </p></div>' +
                "<div style='margin-bottom:5px;padding:0 12px;width:100%;height:24px;line-height:24px;background:white;border-radius:5px;'><p>" +
                'HDMI连接状态: ' + (params.data.s_data.hdmi_in ? '已连接' : '未连接') +
                ' </p></div>'
            }
            // console.log('params:', params)

            return res
          }

        },
        series: [
          {
            // label: {
            //   show: true,
            //   position: 'middle' // 设置 label 居中显示
            // },
            data: nodes,
            links: s_links
          }
        ]
      }
      // option && myChart.setOption(option)
      // myChart.clear()
      setTimeout(() => {
        // myChart.setOption(option)
        myChart.setOption(option)
      }, 500)
    }

    // ****************** 设备当前DHCP参数 *******************

    onMounted(() => {
      // model.id = pageDirectAction.getCurrentRouteValue()
      // getList()
      setTimeout(() => {
        // myChart.setOption(option)
        getList()
      }, 1000)
      // console.info('model.id:' + model.id)
      // getDeviceinfoById()

      // window.goToDetail = function (data) {
      //   console.log('goToDetail: ' + data)
      //   bottom_visible.value = true
      // }

      myChart = echarts.init(
        window.document.getElementById('myChart')
      )
      const config_str = sessionStorage.getItem('mesh_Config')
      if (config_str != null) {
        const json_obj = JSON.parse(config_str)
        if (json_obj.simulate_nodes > 0) {
          console.log('simulate_nodes: ' + json_obj.simulate_nodes)
          simulate_nodes = json_obj.simulate_nodes
        }
        simulate_style = json_obj.simulate_style
      }
      // simulate_style = 1
      // simulate_nodes = 4
    })

    onUnmounted(() => {
      clearInterval(repeat)
      console.log('echart组件被销毁')
      if (DebugModeOption.value === 0) {
        myChart.dispose()
        myChart = undefined
        document.getElementById('myChart').remove()
      }
    })

    return {
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DebugModeOption,
      DeviceinfoColumns,
      pageDirectAction,
      draw,
      getTopo,
      myChart,
      drawClear,
      preDraw,
      // draw_repeat,
      clear_repeat,
      draw_repeat,
      switchChange,
      switchStyle,
      bottom_visible,
      detail_onClose,
      enter_repeat_count,
      drawer_data,
      getNameBySn,
      ...toRefs(state)

    }
  }
})

</script>
