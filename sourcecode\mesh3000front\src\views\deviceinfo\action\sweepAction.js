import baseRequest from '@/request/request'
class SweepAction {
  constructor () {
    this.BASE_API_URL_SET = 'deviceinfo/config/rtp3001cmd'
    this.BASE_API_URL_QUERY = 'deviceinfo/config'
    this.urlSet = `${this.BASE_API_URL_SET}/set`
    this.urlquery = `${this.BASE_API_URL_QUERY}/rtp3001res`
  }

  /**
   * 设置扫频参数
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
   set = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
     baseRequest
       .post(this.urlSet, baseRequestData)
       .then(response => {
         if (typeof successCallback === 'function') {
           successCallback(response.data)
         }
       })
       .catch(error => {
         if (typeof errorCallback === 'function') {
           errorCallback(error)
         }
       })
       .finally(() => {
         if (typeof finalllyCallback === 'function') {
           finalllyCallback()
         }
       })
   }

   /**
   * 查询扫频上报结果
   * @param {*} baseRequestData
   * @param {*} successCallback
   * @param {*} errorCallback
   * @param {*} finalllyCallback
   * @returns
   */
    query = (baseRequestData, successCallback, errorCallback, finalllyCallback) => {
      baseRequest
        .post(this.urlquery, baseRequestData)
        .then(response => {
          if (typeof successCallback === 'function') {
            successCallback(response.data)
          }
        })
        .catch(error => {
          if (typeof errorCallback === 'function') {
            errorCallback(error)
          }
        })
        .finally(() => {
          if (typeof finalllyCallback === 'function') {
            finalllyCallback()
          }
        })
    }
}

export { SweepAction }
