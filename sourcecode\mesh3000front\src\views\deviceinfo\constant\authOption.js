// authOption 是给权限列表提供遍历操作，如果权限列表中没有对应的权限则将对应的权限置为0
import { reactive } from 'vue'
export const AuthOption = reactive({
  网络拓扑: {
    name: '网络拓扑',
    id: '001',
    url: '/api/deviceinfo/config/topo_draw'
  },
  设备日志: {
    name: '设备日志',
    id: '003',
    url: '/meshdevlog'
  },
  视频播放: {
    name: '视频播放',
    url: '/api/deviceinfo/config/camera'
  },
  海康视频播放: {
    name: '海康视频播放',
    url: '/api/deviceinfo/config/hk_video'
  },
  系统设置: {
    name: '系统设置',
    url: 'sys/config'
  },
  Uart0: {
    name: 'Uart0',
    url: '/api/deviceinfo/config/serial'
  },
  防火墙: {
    name: '防火墙',
    url: '/api/deviceinfo/config/firewall'
  },
  WiFi: {
    name: 'WiFi',
    url: '/api/deviceinfo/config/wifi'
  },
  DHCP: {
    name: 'DHCP',
    url: '/api/deviceinfo/config/dhcp'
  },
  Comart: {
    name: 'Comart',
    url: '/api/deviceinfo/config/comart'
  },
  接入模式: {
    name: '接入模式',
    url: '/api/deviceinfo/config/wfmode'
  },
  语音: {
    name: '语音',
    url: '/api/deviceinfo/config/voice'
  },
  温度设置: {
    name: '温度设置',
    url: '/api/deviceinfo/config/health'
  },
  设备版本信息: {
    name: '设备版本信息',
    url: '/api/deviceinfo/config/deviceversion'
  },
  Mesh设备信息: {
    name: 'Mesh设备信息',
    url: '/api/deviceinfo/config/meshdevinfo'
  },
  Mesh设置: {
    name: 'Mesh设置',
    url: '/api/deviceinfo/config/meshconfig'
  },
  视频参数设置: {
    name: '视频参数设置',
    url: '/api/deviceinfo/config/video'
  },
  HDMI视频播放: {
    name: 'HDMI视频播放',
    url: '/api/deviceinfo/config/videoctr'
  },
  AT调试: {
    name: 'AT调试',
    url: '/api/deviceinfo/config/atest'
  }
})
