<template>
  <a-card size="big" title="视频播放">
    <template #extra>
      <a-button id="start" type="primary" style="margin-right:30px">
        开始录制</a-button>
      <!-- <a-button @mouseover="mouseover()" @mouseout="mouseout()" id="start" type="primary" style="margin-right:30px">
                    开始录制</a-button> -->
      <a-button id="stop" type="primary" style="margin-right:30px" v-show="false">
        <template #icon>
          <CloseCircleOutlined />
        </template>
        全部结束
      </a-button>
      <a-button id="save" type="primary" style="margin-right:100px" v-show="false">
        <template #icon>
          <DownloadOutlined />
        </template>
        下载视频
      </a-button>
      <!-- <a-button type="primary">全部下载</a-button> -->
      <label>播放路数：</label>
      <a-select style="width:160px;margin-right: 160px" v-model:value="play_number" v-bind:placeholder="LabelCaption.work_mode.label" id="playNum" :disabled="canSelect" @change="change">
        <a-select-option v-for="option in Plays" v-bind:key="option.value">{{ option.name }}</a-select-option>
      </a-select>
      <a-button type="primary" @click="showDrawer" size="large">
        <template #icon>
          <MenuFoldOutlined />
        </template>
        视频列表
      </a-button>
    </template>
    <a-drawer :width="500" title="视频列表" :placement="placement" :visible="visible" @close="onClose">
      <template #extra>
        <a-button @click="handleRefresh()" style="margin-right:20px">
          <template #icon>
            <redoOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="openAddCamera" v-if="AuthUi.视频播放.statusW">
          <template #icon>
            <PlusCircleOutlined />
          </template>
          添加设备
        </a-button>
      </template>

      <a-row :span="24" justify="space-around" v-show="showSearch">
        <a-col :span="19">
          <a-input v-model:value="searchData" placeholder="请输入设备名称" />
        </a-col>
        <a-col :span="5">
          <a-button type="primary" style="margin-left:5%" @click="closeSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            搜索
          </a-button>
        </a-col>
        <a-divider />
      </a-row>
      <a-row align="end" v-show="!showSearch" @click="openSearch">
        <a-button type="primary">
          <template #icon>
            <LeftOutlined />
          </template>
          返回
        </a-button>
      </a-row>

      <a-list v-show="!showSearch" item-layout="vertical" size="large" :pagination="pagination" :data-source="searchList">
        <template #emptyText>
      <empty-table />
    </template>
        <template #renderItem="{ item }">
          <a-list-item key="item.title">
            <template #actions>
              <span>
                <a-button @click="showPlayOne(item)">
                  <template #icon>
                    <PlayCircleOutlined />
                  </template>
                  播放
                </a-button>
                <a-button @click="showEdit(item)" style="margin-left:5px" v-if="AuthUi.视频播放.statusW">
                  <template #icon>
                    <EditOutlined />
                  </template>
                  编辑
                </a-button>
                <a-button @click="delConfirm(item)" style="margin-left:5px" danger v-if="AuthUi.视频播放.statusW">
                  删除</a-button>
              </span>
            </template>
            <a-list-item-meta :description="item.description">
              <template #title>
                <a-row :span="24">
                  <a-col :span="18">
                    <label>名称：</label>
                    <label>{{ item.title }}</label>
                  </a-col>
                  <a-col :span="6" align="start">
                    <label>ID:</label>
                    <label>{{ item.key}}</label>
                  </a-col>
                </a-row>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>

      <a-modal v-model:visible="addCameraVisible" title="添加摄像头" @ok="closeAddCamera" width="950px" :footer="null">
  <a-form :model="addData" name="nest-messages" layout="horizontal" @finish="addConfirm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">

    <!-- 基本信息 -->
    <a-divider orientation="left">
      <camera-outlined /> 基本信息
    </a-divider>

    <a-row :gutter="32" style="margin-bottom: 24px">
      <a-col :span="8">
        <a-form-item :name="['device_type']" :rules="[{ required: true, message: '该选项为必选项' }]" label="类型：">
          <a-select v-model:value="addData.device_type" size="large">
            <template #prefix><video-camera-outlined /></template>
            <a-select-option v-for="option in type" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item v-if="addData.device_type!==3" :name="['device_name']" :rules="[{ required: true, message: '摄像头名称不能为空' }]" label="名称：">
          <a-input v-model:value="addData.device_name" size="large">
            <template #prefix><tag-outlined /></template>
          </a-input>
        </a-form-item>
        <a-form-item v-if="addData.device_type==3" :name="['device_name']" :rules="[{ required: true, message: '名称不能为空' }]" label="名称：">
          <a-input v-model:value="addData.device_name" size="large">
            <template #prefix><video-outlined /></template>
          </a-input>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item :name="['description']" :rules="[{ required: true, message: '描述不能为空' }]" label="描述：" v-if="addData.device_type!==3">
          <a-input v-model:value="addData.description" size="large">
            <template #prefix><info-circle-outlined /></template>
          </a-input>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 连接信息 -->
    <a-divider orientation="left" style="margin-top: 36px">
      <link-outlined /> 连接信息
    </a-divider>

    <a-row :gutter="32" style="margin-bottom: 24px">
      <!-- 普通摄像头 -->
      <template v-if="addData.device_type!==3 && addData.device_type!==2">
        <a-col :span="8">
          <a-form-item :name="['firm']" :rules="[{ required: true, message: '该选项为必选项' }]" label="厂商：">
            <a-select v-model:value="addData.firm" size="large">
              <template #prefix><bank-outlined /></template>
              <a-select-option v-for="option in firm" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['ip']" :rules="[
            { required: true, message: '摄像头IP地址不能为空' },
            { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确' }
          ]" label="IP：">
            <a-input v-model:value="addData.ip" size="large">
              <template #prefix><global-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['port']" :rules="[
            { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '请输入1-65535之间的有效端口号' }
          ]" label="RTSP端口：">
            <a-input placeholder="不填默认为554端口" v-model:value="addData.port" size="large">
              <template #prefix><number-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>

      <!-- 设备类型2 -->
      <template v-if="addData.device_type==2">
        <a-col :span="12">
          <a-form-item :name="['ip']" :rules="[
            { required: true, message: '设备IP地址不能为空' },
            { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确' }
          ]" label="IP：">
            <a-input v-model:value="addData.ip" size="large">
              <template #prefix><global-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>

      <!-- RTSP流 -->
      <template v-if="addData.device_type==3">
        <a-col :span="16">
          <a-form-item :name="['rtsp_url']" :rules="[{ required: true, message: '该项为必填项' }]" label="RTSP流地址：">
            <a-input v-model:value="addData.rtsp_url" size="large">
              <template #prefix><api-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>
    </a-row>

    <!-- 认证信息 -->
    <template v-if="addData.device_type!==3 && addData.device_type!==2">
      <a-divider orientation="left" style="margin-top: 36px">
        <safety-outlined /> 认证信息
      </a-divider>

      <a-row :gutter="32" style="margin-bottom: 24px">
        <a-col :span="8">
          <a-form-item :name="['user_name']" :rules="[{ required: true, message: '该项为必填项' }]" label="用户名：">
            <a-input placeholder="请输入摄像头用户名" v-model:value="addData.user_name" size="large">
              <template #prefix><user-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['password']" :rules="[{ required: true, message: '该项为必填项' }]" label="密码：">
            <a-input placeholder="请输入摄像头密码" type="password" v-model:value="addData.password" size="large">
              <template #prefix><lock-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </template>

    <!-- 高级设置 -->
    <template v-if="addData.device_type!==3 && addData.device_type!==2">
      <a-divider orientation="left" style="margin-top: 36px">
        <setting-outlined /> 高级设置
      </a-divider>

      <a-row :gutter="32" style="margin-bottom: 24px">
        <a-col :span="8" v-if="!addData.device_type">
          <a-form-item label="通道：" :name="['channel']" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="addData.channel" size="large">
              <template #prefix><branches-outlined /></template>
              <a-select-option v-for="option in channel" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['stream']" label="码流：" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="addData.stream" :disabled="addData.firm" size="large">
              <template #prefix><column-width-outlined /></template>
              <a-select-option v-for="option in stream" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['old_device']" label="出厂日期：" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="addData.old_device" size="large">
              <template #prefix><calendar-outlined /></template>
              <a-select-option v-for="option in years" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </template>

    <!-- 操作按钮 -->
    <a-row justify="center" style="margin-top: 48px">
      <a-space size="large">
        <a-button type="primary" html-type="submit" v-if="AuthUi.视频播放.statusW" size="large">
          <template #icon><save-outlined /></template>
          保存
        </a-button>
        <a-button @click="closeAddCamera" size="large">
          <template #icon><close-outlined /></template>
          取消
        </a-button>
      </a-space>
    </a-row>
  </a-form>
</a-modal>

<a-modal v-model:visible="editVisible" title="编辑摄像头" @ok="closeEdit" width="950px" :footer="null">
  <a-form :model="editData" name="nest-messages" layout="horizontal" @finish="editConfirm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">

    <!-- 基本信息 -->
    <a-divider orientation="left">
      <camera-outlined /> 基本信息
    </a-divider>

    <a-row :gutter="32" style="margin-bottom: 24px">
      <a-col :span="8">
        <a-form-item :name="['device_type']" :rules="[{ required: true, message: '该选项为必选项' }]" label="类型：">
          <a-select v-model:value="editData.device_type" size="large">
            <template #prefix><video-camera-outlined /></template>
            <a-select-option v-for="option in type" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item v-if="editData.device_type!==3" :name="['device_name']" :rules="[{ required: true, message: '摄像头名称不能为空' }]" label="名称：">
          <a-input v-model:value="editData.device_name" size="large">
            <template #prefix><tag-outlined /></template>
          </a-input>
        </a-form-item>
        <a-form-item v-if="editData.device_type==3" :name="['device_name']" :rules="[{ required: true, message: '名称不能为空' }]" label="名称：">
          <a-input v-model:value="editData.device_name" size="large">
            <template #prefix><video-outlined /></template>
          </a-input>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item :name="['description']" :rules="[{ required: true, message: '描述不能为空' }]" label="描述：" v-if="editData.device_type!==3">
          <a-input v-model:value="editData.description" size="large">
            <template #prefix><info-circle-outlined /></template>
          </a-input>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 连接信息 -->
    <a-divider orientation="left" style="margin-top: 36px">
      <link-outlined /> 连接信息
    </a-divider>

    <a-row :gutter="32" style="margin-bottom: 24px">
      <!-- 普通摄像头 -->
      <template v-if="editData.device_type!==3 && editData.device_type!==2">
        <a-col :span="8">
          <a-form-item :name="['firm']" :rules="[{ required: true, message: '该选项为必选项' }]" label="厂商：">
            <a-select v-model:value="editData.firm" size="large">
              <template #prefix><bank-outlined /></template>
              <a-select-option v-for="option in firm" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['ip']" :rules="[
            { required: true, message: '摄像头IP地址不能为空' },
            { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确' }
          ]" label="IP：">
            <a-input v-model:value="editData.ip" size="large">
              <template #prefix><global-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['port']" :rules="[
            { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: '请输入1-65535之间的有效端口号' }
          ]" label="RTSP端口：">
            <a-input placeholder="不填默认为554端口" v-model:value="editData.port" size="large">
              <template #prefix><number-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>

      <!-- 设备类型2 -->
      <template v-if="editData.device_type==2">
        <a-col :span="12">
          <a-form-item :name="['ip']" :rules="[
            { required: true, message: '设备IP地址不能为空' },
            { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确' }
          ]" label="IP：">
            <a-input v-model:value="editData.ip" size="large">
              <template #prefix><global-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>

      <!-- RTSP流 -->
      <template v-if="editData.device_type==3">
        <a-col :span="16">
          <a-form-item :name="['rtsp_url']" :rules="[{ required: true, message: '该项为必填项' }]" label="RTSP流地址：">
            <a-input v-model:value="editData.rtsp_url" size="large">
              <template #prefix><api-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </template>
    </a-row>

    <!-- 认证信息 -->
    <template v-if="editData.device_type!==3 && editData.device_type!==2">
      <a-divider orientation="left" style="margin-top: 36px">
        <safety-outlined /> 认证信息
      </a-divider>

      <a-row :gutter="32" style="margin-bottom: 24px">
        <a-col :span="8">
          <a-form-item :name="['user_name']" :rules="[{ required: true, message: '该项为必填项' }]" label="用户名：">
            <a-input placeholder="请输入摄像头用户名" v-model:value="editData.user_name" size="large">
              <template #prefix><user-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['password']" :rules="[{ required: true, message: '该项为必填项' }]" label="密码：">
            <a-input placeholder="请输入摄像头密码" type="password" v-model:value="editData.password" size="large">
              <template #prefix><lock-outlined /></template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </template>

    <!-- 高级设置 -->
    <template v-if="editData.device_type!==3 && editData.device_type!==2">
      <a-divider orientation="left" style="margin-top: 36px">
        <setting-outlined /> 高级设置
      </a-divider>

      <a-row :gutter="32" style="margin-bottom: 24px">
        <a-col :span="8" v-if="!editData.device_type">
          <a-form-item label="通道：" :name="['channel']" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="editData.channel" size="large">
              <template #prefix><branches-outlined /></template>
              <a-select-option v-for="option in channel" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['stream']" label="码流：" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="editData.stream" :disabled="editData.firm" size="large">
              <template #prefix><column-width-outlined /></template>
              <a-select-option v-for="option in stream" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item :name="['old_device']" label="出厂日期：" :rules="[{ required: true, message: '该选项为必选选项' }]">
            <a-select v-model:value="editData.old_device" size="large">
              <template #prefix><calendar-outlined /></template>
              <a-select-option v-for="option in years" v-bind:key="option.value" :value="option.value">{{ option.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </template>

    <!-- 操作按钮 -->
    <a-row justify="center" style="margin-top: 48px">
      <a-space size="large">
        <a-button type="primary" html-type="submit" v-if="AuthUi.视频播放.statusW" size="large">
          <template #icon><save-outlined /></template>
          保存
        </a-button>
        <a-button @click="closeEdit" size="large">
          <template #icon><close-outlined /></template>
          取消
        </a-button>
      </a-space>
    </a-row>
  </a-form>
</a-modal>

      <a-list v-show="showSearch" item-layout="vertical" size="large" :pagination="pagination" :data-source="listData">
        <template #emptyText>
          <empty-table />
        </template>
        <template #renderItem="{ item }">
          <a-list-item key="item.title">
            <template #actions>
              <span>
                <a-button @click="showPlayOne(item)">
                  <template #icon>
                    <PlayCircleOutlined />
                  </template>
                  播放
                </a-button>
                <a-button @click="showEdit(item)" style="margin-left:5px" v-if="AuthUi.视频播放.statusW">
                  <template #icon>
                    <EditOutlined />
                  </template>
                  编辑
                </a-button>
                <a-button @click="delConfirm(item)" style="margin-left:5px" danger v-if="AuthUi.视频播放.statusW">
                  删除</a-button>
              </span>
            </template>
            <a-list-item-meta :description="item.description">
              <template #title>
                <a-row :span="24">
                  <a-col :span="18">
                    <label>名称：</label>
                    <label>{{ item.title }}</label>
                  </a-col>
                  <a-col :span="6" align="start">
                    <label>ID:</label>
                    <label>{{ item.key}}</label>
                  </a-col>
                </a-row>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-drawer>

    <a-row justify="center" :span="24">
      <div class="main-video-container">
        <a-row justify="end" :span="24" :gutter="[20, 20]">
          <a-col :span="actions.span" v-for="(n) in actions.number" :key="n.id">
            <!-- 控制按钮代码 - 当前隐藏 -->
            <a-row justify="center">
              <div class="rec">
                <a-row>
                  <a-col>
                    <div style="width:20px;height:20px;border-radius:50%;background-color:red;">
                    </div>
                  </a-col>
                  <a-col>
                    <div style="color:#FFFF;">录制中</div>
                  </a-col>
                </a-row>
              </div>
              <div
                class="video-container"
                :id="'container-'+n"
                @click="clickVideo(n)"
                :class="{ 'selected-video': selectedVideo === n }"
                :style="{
                  border: selectedVideo === n ? '3px solid #1effda' : '1px solid rgba(30, 144, 255, 0.2)',
                  boxShadow: selectedVideo === n ? '0 0 15px rgba(30, 255, 218, 0.7)' : '0 5px 15px rgba(0, 0, 0, 0.3)',
                  margin: selectedVideo === n ? '5px' : '0px',
                  zIndex: selectedVideo === n ? '10' : '1',
                  transform: selectedVideo === n ? 'scale(1.03)' : 'scale(1)',
                  transition: 'all 0.3s ease'
                }"
              >
                <video :class="actions.style" :poster="null" :id="n" @loadeddata="videoLoadedHandler(n)" muted></video>
                <div class="video-placeholder" v-if="!videoLoaded[n-1]">
                  <div class="placeholder-icon">
                    <div class="placeholder-camera">
                      <div class="camera-body"></div>
                      <div class="camera-lens"></div>
                      <div class="camera-flash"></div>
                    </div>
                  </div>
                  <div class="placeholder-text">等待视频连接...</div>
                </div>
                <!-- 视频控制器 -->
                <div class="video-controls" v-if="videoLoaded[n-1]">
                  <a-button class="fullscreen-btn" type="primary" shape="circle" @click.stop="toggleFullscreen(n)">
                    <template #icon><FullscreenOutlined v-if="!isFullscreen" /><FullscreenExitOutlined v-else /></template>
                  </a-button>
                </div>
              </div>
            </a-row>
          </a-col>
        </a-row>
      </div>
    </a-row>
    <!-- 其他控制按钮 - 当前隐藏
      <a-row justify="space-around" style="margin-top:2%">
      <a-button id="record">开始录制</a-button>
      <a-button id="recplay">播放</a-button>
      <a-button id="download">下载</a-button>
      </a-row>
    -->
  </a-card>

</template>
<script>
import { onMounted, defineComponent, createVNode, reactive, ref, nextTick, onUnmounted } from 'vue'
import { Modal, message, notification } from 'ant-design-vue'
import {
  // getServerAddr,
  ServerAddr
} from '@/views/deviceinfo/views/video/videoConfig'
import {
  QuestionOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  //   PauseOutlined,
  MenuFoldOutlined,
  SearchOutlined,
  PlayCircleOutlined,
  PlusCircleOutlined,
  //   DeleteOutlined,
  RedoOutlined,
  EditOutlined,
  //   SoundOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  LeftOutlined
} from '@ant-design/icons-vue'
import { CameraRequestAction } from '@/views/deviceinfo/action/cameraAction'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import { CameraModel } from '@/views/deviceinfo/model/CameraModel'
import { BaseParam } from '@/common/baseParam'
import { BaseRequestData } from '@/common/baseRequestData'
import { LabelCaption } from '@/views/deviceinfo/constant/lables'
import { ErrorInfo } from '@/common/errorInfo'
import { DeviceinfoColumns } from '@/views/deviceinfo/constant/columns'
import Pattern from '@/common/pattern'
import { WebRtcStreamer } from '@/views/deviceinfo/views/video/webrtcstreamer'
import {
  Plays,
  channel,
  firm,
  type,
  stream,
  years
} from '@/views/deviceinfo/constant/options'

import { AuthUi } from '@/views/deviceinfo/constant/authui'

import EmptyTable from '@/components/EmptyTable.vue'

export default defineComponent({
  components: {
    MenuFoldOutlined,
    // PauseOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    SearchOutlined,
    PlayCircleOutlined,
    // DeleteOutlined,
    RedoOutlined,
    EditOutlined,
    LeftOutlined,
    CloseCircleOutlined,
    DownloadOutlined,
    EmptyTable,
    PlusCircleOutlined
  },
  setup () {
    // 采集的Blob格式的视频数据
    let blob
    // 当前选中的视频窗口
    const selectedVideo = ref(null)

    // MediaRecorder对象
    // let mediaRecorder
    // MediaRecorder对象数组, 用于保存MediaRecorder对象从而保存视频录制状态（多路录制时候用）
    const mediaRecorders = []
    // stream对象数组，用于保存stream对象（多路录制的时候用）
    const streams = []
    // 视频加载状态跟踪数组
    const videoLoaded = ref([])
    // 全屏状态
    const isFullscreen = ref(false)
    // 当前全屏元素
    let fullscreenVideo = null

    // 切换全屏状态
    const toggleFullscreen = (videoId) => {
      const videoElement = document.getElementById(videoId.toString())

      if (!document.fullscreenElement) {
        // 进入全屏
        if (videoElement.requestFullscreen) {
          videoElement.requestFullscreen()
        } else if (videoElement.webkitRequestFullscreen) { /* Safari */
          videoElement.webkitRequestFullscreen()
        } else if (videoElement.msRequestFullscreen) { /* IE11 */
          videoElement.msRequestFullscreen()
        }
        isFullscreen.value = true
        fullscreenVideo = videoElement
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen()
        }
        isFullscreen.value = false
        fullscreenVideo = null
      }
    }

    // 监听全屏变化事件
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement
      if (!document.fullscreenElement) {
        fullscreenVideo = null
      }
    }

    // 视频加载完成处理函数
    const videoLoadedHandler = (index) => {
      // 设置对应索引的视频为已加载
      if (videoLoaded.value.length >= index) {
        videoLoaded.value[index - 1] = true
      }
    }

    let oStart = document.getElementById('start')
    let oStop = document.getElementById('stop')
    let oSave = document.getElementById('save')
    let video // vidoe播放窗口的Dom模型
    // // 选择的视频窗口
    // let choseVideo = document.getElementById('1')
    // 文件名
    let fileName
    // 用户登录状态
    let json_obj
    // 录像提示标志
    const rec = document.getElementsByClassName('rec')
    // // 播放窗口的编号
    let windowId = null
    // 摄像头录制窗口状态变量
    const videoStatuses = []
    // 摄像头录制状态变量
    let videoStatus = false
    // 是否可以选择播放路数
    const canSelect = ref(false)

    /**
     * 点击播放
     */
    const play = (i) => {
      document.getElementById(i).play()
      document.getElementById('play').style.display = 'none'// 隐藏选择的元素
      document.getElementById('pause').style.display = 'block'
    }
    /**
     * 点击暂停
     */
    const pause = (i) => {
      document.getElementById(i).pause()
      document.getElementById('pause').style.display = 'none'
      document.getElementById('play').style.display = 'block'
    }
    /* 隐藏所有的录制UI提示 */
    const concealREC = () => {
      for (let i = 0; i < rec.length; i++) {
        rec[i].style.visibility = 'hidden'
      }
    }

    /**
         * 在切换播放路数之前会判断是否有视频正在录制
         * 如果有视频还在继续录制的话，则应当给出提示。要求首先停止视频录制，然后再进行切换
         * 此用于预防内存泄漏
         */
    // 遍历每个窗口录制状态
    const checkedVideoStatus = () => {
      for (let i = 0; i < videoStatuses.length; i++) {
        if (videoStatuses[i] === true) {
          videoStatus = true
          break
        } else {
          videoStatus = false
        }
      }
      if (videoStatus === true) {
        // 有窗口在录制
        canSelect.value = true
      } else {
        canSelect.value = false
      }
    }

    /** 获取系统时间 */
    const getNowDate = () => {
      const date = new Date()
      const year = date.getFullYear() // 年
      let month = date.getMonth() + 1 // 月
      let day = date.getDate() // 日
      let hour = date.getHours() // 时
      let minutes = date.getMinutes() // 分
      let seconds = date.getSeconds() // 秒
      //  const weekArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天']
      //   const week = weekArr[date.getDay()] 周几
      // 给一位数的数据前面加 "0"
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (day >= 0 && day <= 9) {
        day = '0' + day
      }
      if (hour >= 0 && hour <= 9) {
        hour = '0' + hour
      }
      if (minutes >= 0 && minutes <= 9) {
        minutes = '0' + minutes
      }
      if (seconds >= 0 && seconds <= 9) {
        seconds = '0' + seconds
      }
      return year + '年' + month + '月' + day + '日' + hour + '时' + minutes + '分' + seconds + '秒'
    }
    /* 隐藏一路录制UI提示 */
    const concealREC_One = (i) => {
      rec[i - 1].style.visibility = 'hidden'
    }
    /** 显示录制的UI提示 */
    const showREC = () => {
      rec[windowId - 1].style.visibility = 'visible'// 显示元素
    }
    // 点击窗口回调
    const clickVideo = (i) => {
      // 更新选中的视频
      selectedVideo.value = i

      // 清除之前的边框样式
      for (let c = 1; c <= actions.number; c++) {
        const container = document.getElementById('container-' + c.toString())
        if (container) {
          container.classList.remove('border')
          container.classList.remove('video-selected')
          container.style.border = 'none'
          container.style.boxShadow = 'none'
        }
      }
      // 为点击的视频窗口添加新的样式
      const container = document.getElementById('container-' + i.toString())
      if (container) {
        container.classList.add('video-selected')
      }
      video = document.getElementById(i.toString())
      oStart = document.getElementById('start')
      oStop = document.getElementById('stop')
      oSave = document.getElementById('save')
      // 当点击视频的时候就创建好对应mediaRecorder与stream对象，方便保存
      // 保存stream变量
      if (streams[i - 1] === null) {
        // 只有在该stream为空的时候进行添加
        streams[i - 1] = video.captureStream()
      }
      // 保存mediaRecorder对象
      if (mediaRecorders[i - 1] === null) {
        // 在此为空的时候进行添加
        mediaRecorders[i - 1] = new MediaRecorder(streams[i - 1], { // 创建MediaStream 对象用来进行视频录制操作，有两个参数第一个是stream音视频流，第二个是配置参数
          audioBitsPerSecond: 128000, // 音频码率
          videoBitsPerSecond: 1000000, // 视频码率
          mimeType: 'video/webm;codecs=h264' // 编码格式
        })
      }
      //   // 当视频在播放的时候进行操作,待加入限定条件
      //   mediaRecorder = new MediaRecorder(stream, { // 创建MediaStream 对象用来进行视频录制操作，有两个参数第一个是stream音视频流，第二个是配置参数
      //     audioBitsPerSecond: 128000, // 音频码率
      //     videoBitsPerSecond: 1000000, // 视频码率
      //     mimeType: 'video/webm;codecs=h264' // 编码格式
      //   })
      oStart.onclick = function () { // 录像控制按钮
        // 判断选择窗口是否可以播放，如果不可以播放则弹出提示框
        if (videoStatuses[windowId - 1] === false) { // 当前窗口在停止录制状态，点击按钮为开始录制
          if (!(video.paused || video.ended || video.seeking)) {
            mediaRecorders[i - 1].start()
            // 显示录制提示
            showREC()
            // 将对应窗口的录制状态设置为录制中
            videoStatuses[windowId - 1] = true
            oStart.innerHTML = '停止录制'
            notification.success({
              message: '开始录制',
              description:
                '摄像头' + (windowId - 1) + '正在录制',
              duration: 2
            })
            // 对录制状态进行改变
            canSelect.value = true
          } else {
            // 选择的视频窗口不可以进行播放，弹出对应的提示
            alert('视频没有在播放')
          }
        } else { // 当前窗口在录制状态，点击按钮为停止录制
          // 停止对应的录像
          mediaRecorders[i - 1].stop()
          // 清除录制ui
          concealREC_One(i)
          // 将对应窗口的录制状态设置为停止录制
          videoStatuses[i - 1] = false
          oStart.innerHTML = '开始录制'
          notification.success({
            message: '录制完成',
            description:
              '摄像头' + (windowId - 1) + '已经录制完成',
            duration: 2
          })
          // 停止录制的时候对录制状态进行检查
          checkedVideoStatus()
        }
      }

      //   oStop.onclick = function () { // 停止录像(全部停止)
      //     mediaRecorders[i - 1].stop()
      //     // 清除录制ui
      //     concealREC()
      //     // 将全部的窗口的录制状态设置为停止录制
      //     for (let i = 0; i < videoStatuses.length; i++) {
      //       videoStatuses[i] = false
      //     }
      //   }

      oStop.onclick = function () { // 停止录像(单个停止)
        // 停止对应的录像
        mediaRecorders[i - 1].stop()
        // 清除录制ui
        concealREC_One(i)
        // 将对应窗口的录制状态设置为停止录制
        videoStatuses[i - 1] = false
      }

      oSave.onclick = function () { // 保存视频
        // 下载视频
        const a = document.createElement('a') // 创建<a>标签
        a.href = URL.createObjectURL(blob) // 将视频数据的地址赋予href属性
        a.download = sessionStorage.getItem('login_state') // 将视频数据保存在当地，文件名为"test.mp4"
        a.click()
      }

      // 事件,开始录像时捕获数据，结束录像时将捕获的数据，传递到BLOB中，当此动作完成后，触发ondataavailable
      mediaRecorders[i - 1].ondataavailable = function (e) {
        blob = new Blob([e.data], { type: 'video/mp4' })
        // 下载视频
        const a = document.createElement('a') // 创建<a>标签
        a.href = URL.createObjectURL(blob) // 将视频数据的地址赋予href属性
        fileName = sessionStorage.getItem('login_state')
        if (fileName != null) {
          json_obj = '用户名:' + JSON.parse(fileName).user + '时间:' + getNowDate()
        }
        a.download = json_obj// 将视频数据保存在当地，文件名为"用户名+时间.mp4"
        a.click()
      }
      // 记录点击的窗口id
      windowId = i

      // 调用录制判断函数
      recJudge()
    }
    // const showEdit = (i) => {
    //   editVisible.value = true
    //   editData.value = model[i.key]
    // }
    const img_url = require('@/assets/img/break.png')
    // 视频列表数据
    const listData = ref([])
    // 穿梭框数据
    const mockData = ref([])
    // 播放列表数据
    const playList = ref([])
    // 保存播放窗口上次选定值
    const selectVideo = []

    const options = 'rtptransport=tcp'

    const getRowSelection = ({
      disabled,
      selectedKeys,
      onItemSelectAll,
      onItemSelect
    }) => {
      return {
        getCheckboxProps: item => ({
          disabled: disabled || item.disabled
        }),

        onSelectAll (selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({
            key
          }) => key)
          onItemSelectAll(treeSelectedKeys, selected)
        },

        onSelect ({
          key
        }, selected) {
          onItemSelect(key, selected)
        },

        selectedRowKeys: selectedKeys
      }
    }

    // 是否显示设定播放列表弹窗标志
    const visiblePlay = ref(false)
    const pagination = {
      onChange: page => {
      },
      // 页面显示容量
      pageSize: 5
    }
    // 播放窗口样式
    const actions = {
      number: 1,
      span: 24,
      style: 'play_one'
    }

    // 视频列表抽屉
    const placement = ref('right')
    const visible = ref(false)
    // 选择设备框使能变量，在设备预览界面，为false
    const choseDeviceShow = ref(true)
    // 添加摄像头弹窗控制变量
    const addCameraVisible = ref(false)
    // 修改摄像头弹窗控制变量
    const editVisible = ref(false)
    // 编辑摄像头model数据
    const editData = ref([])
    // 添加摄像头model数据
    const addData = ref([])

    // 打开添加摄像头弹窗
    const openAddCamera = () => {
      addCameraVisible.value = true
      addData.value = new CameraModel()
    }
    // 关闭添加摄像头弹窗
    const closeAddCamera = () => {
      addCameraVisible.value = false
    }

    const mouseover = () => {
    }
    const mouseout = () => {
    }

    // 打开修改摄像头弹窗
    const showEdit = (i) => {
      editVisible.value = true
      editData.value = model[i.key]
    }

    // 关闭修改摄像头弹窗
    const closeEdit = () => {
      editVisible.value = false
    }

    // 打开抽屉
    const showDrawer = () => {
      // 判断是否有视频在录制，如果有视频在录着则提示用户不可操作
      if (canSelect.value === false) {
        visible.value = true
      } else {
        notification.warning({
          message: '视频录制中...',
          description: '当前视频窗口有视频正在录制，请先结束录制后进行此操作。',
          duration: 2
        })
      }
    }
    // 关闭抽屉
    const onClose = () => {
      visible.value = false
    }

    // 返回多路播放模式
    const goPlays = () => {
      choseDeviceShow.value = true

      // 断开设备预览窗口的连接
      // 对资源进行释放，断开数组中的连接
      for (let i = 0; i < webRtc_one.length; i++) {
        if (webRtc_one[i].isConnect === true) {
          webRtc_one[i].webRtcServer.disconnect()
          webRtc_one[i].isConnect = false
        }
      }
      // 返回用户进入预览界面前的播放路数
      addStyle(voidConfig.plays)
      // 刷新抽屉窗口重新渲染页面
      showDrawer()
      onClose()
      // 重新连接
      reConnect()
    }
    // 摄像头webRtc_one对象数组
    const webRtc_one = []
    // 摄像头播放路数中间变量
    const play_number = ref(0)
    // 摄像头预览资源释放
    const pushOneResource = () => {
      // 资源释放
      // 第一次加载不进行断流操作
      if (webRtc_one.length !== 0) {
        // 改变路数的时候断开所有的webrtc连接，用来防止内存泄漏
        for (let i = 0; i < webRtc_one.length; i++) {
          webRtc_one[i].webRtcServer.disconnect()
        }
        // 释放完资源清空数组
        webRtc_one.value = []
      }
    }
    // 打开单摄像头播放窗
    const showPlayOne = (t) => {
      onClose()
      // 单路样式
      addStyle(0)
      // 将播放路数更改为单路播放
      play_number.value = 0
      // 创建查找变量find用于标识数组中是否含有webrtc对象
      let find = false
      // 资源释放
      pushOneResource()
      // 创建webRtc连接，连接所选摄像头。
      // 判断数组中是否有webRtc对象，如果存在直接连接，不存在则创建对象并创建连接
      // 放在数组中的目的是为了当关闭窗口的时候进行资源释放，而不断掉其他摄像头webRtc连接。
      for (let i = 0; i < webRtc_one.length; i++) {
        // 如果找到了webRTC对象则直接创建连接
        if (webRtc_one[i].id === t.key) {
          // 存在该对象，创建连接
          webRtc_one[i].webRtcServer.connect(listData.value[t.key].rtsp, listData.value[t.key].rtsp, options)
          // 创建完连接则直接return结束循环，并将其循环变量改为true
          find = true
          webRtc_one[i].isConnect = true
        }
      }
      // 如果没有找到则创建对应的webRtc对象，并将其加入数组中，方便下次连接
      if (find === false) {
        // 创建webRtc对象
        const webRtcCrate = new WebRtcStreamer('1', ServerAddr.serverAddr)
        webRtcCrate.connect(listData.value[t.key].rtsp, listData.value[t.key].rtsp, options)
        webRtc_one.push({
          id: t.key,
          webRtcServer: webRtcCrate,
          isConnect: true
        })
      }
    }
    const showSearch = ref(true)
    const searchData = ref('')
    const searchList = ref([])
    // 展示搜索框
    const openSearch = () => {
      showSearch.value = true
      // 清空搜索列表数组
      searchList.value = []
    }

    // 隐藏搜索框
    const closeSearch = () => {
      showSearch.value = false
      // 查找对应名称的摄像头
      for (let i = 0; i < listData.value.length; i++) {
        if (searchData.value === listData.value[i].title) {
          searchList.value.push(listData.value[i])
        }
      }
    }
    // 展示播放列表
    const showModal = () => {
      visiblePlay.value = true
    }
    // 点击OK后的操作
    const handleOk = e => {
      visiblePlay.value = false
      //   showPlay.value = false
    }

    // const url = { video: 'rtsp://admin:Dtt123456@192.168.99.52:554/h265/ch2/main' }
    // const url2 = { video: 'rtsp://admin:Dtt123456@192.168.99.53:554/h265/ch2/main' }
    window.onunload = function () {
      this.webRtcServer.disconnect()
      console('切换窗口释放资源')
    }

    // window.onload = function () {
    //   this.webRtcServer = new WebRtcStreamer('video', 'http://192.168.99.148:8000')
    //   for (let i = 1; i < 3; i++) {
    //     new WebRtcStreamer('video' + (i + 1), 'http://192.168.99.148:8000').connect(url2.video, url2.video, options)
    //   }
    //   this.webRtcServer2 = new WebRtcStreamer('video2', 'http://192.168.99.148:8000')
    //   this.webRtcServer3 = new WebRtcStreamer('video3', 'http://192.168.99.148:8000')
    //   this.webRtcServer4 = new WebRtcStreamer('video4', 'http://192.168.99.148:8000')
    //   this.webRtcServer.connect(url.video, url.video, options)
    //   this.webRtcServer.connect(url2.video, url2.video, options)
    //   this.webRtcServer3.connect(url2.video, url2.video, options)
    //   this.webRtcServer4.connect(url2.video, url2.video, options)
    // }

    // 对应后台数据表
    let model = reactive(new CameraModel())

    // 保存查询的数据库数据
    const dataSource = reactive({})
    const baseParam = reactive(new BaseParam())
    let baseRequestData = reactive(new BaseRequestData(baseParam, model))
    // const requestAction = new RequestAction()
    const cameraRequestAction = new CameraRequestAction()
    const pageDirectAction = new PageDirectAction()

    // // 回调函数错误处理
    // const callbackError = () => {
    //   message.error('数据异常，请重试', 3)
    //   console.info(error)
    // }

    // 添加摄像头弹窗提示
    const addConfirm = () => {
      Modal.confirm({
        title: '新增摄像头',
        icon: createVNode(QuestionOutlined),
        content: '确认添加该摄像头？',
        okText: '确认',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            add()
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }
    // 删除摄像头弹窗提示
    const delConfirm = (i) => {
      Modal.confirm({
        title: '删除摄像头',
        icon: createVNode(QuestionOutlined),
        content: '确认删除该摄像头？',
        okText: '确认',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            del(i)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }
    // 修改摄像头弹窗提示
    const editConfirm = (i) => {
      Modal.confirm({
        title: '修改摄像头',
        icon: createVNode(QuestionOutlined),
        content: '确认保存此修改？',
        okText: '确认',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            edit(i)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    // 删除摄像头回调函数
    const delSuccess = (data) => {
      console.log('收到删除响应:', data)
      if (data.error_code === ErrorInfo.Success) {
        message.success('删除摄像头成功!')
        // 资源释放
        resourcePush()
        console.log('开始延时刷新列表...')
        // 添加延迟再刷新列表，确保后端数据已更新
        setTimeout(() => {
          console.log('执行延时刷新列表...')
          // 使用普通刷新，但通过getAccessMode已添加时间戳
          getAccessMode()
          // 再添加一个延迟后重新连接
          setTimeout(() => {
            console.log('重新连接摄像头...')
            reConnect()
          }, 300)
        }, 1000) // 1秒延迟
      } else {
        message.error(`删除摄像头失败!${data.error_code}`)
        console.error('删除失败，错误码:', data.error_code)
      }
    }

    // 添加摄像头回调函数
    const addSuccess = (data) => {
      console.log('收到添加响应:', data)
      if (data.error_code === ErrorInfo.Success) {
        message.success('添加摄像头成功!')
        // 添加摄像头成功后关闭弹窗并清空中间变量
        closeAddCamera()
        model = []
        // 资源释放
        resourcePush()
        console.log('开始延时刷新列表...')
        // 添加延迟再刷新列表，确保后端数据已更新
        setTimeout(() => {
          console.log('执行延时刷新列表...')
          // 使用普通刷新，但通过getAccessMode已添加时间戳
          getAccessMode()
          // 再添加一个延迟后重新连接
          setTimeout(() => {
            console.log('重新连接摄像头...')
            reConnect()
          }, 300)
        }, 1000) // 1秒延迟
      } else {
        message.error(`添加摄像头失败!${data.error_code}`)
        console.error('添加失败，错误码:', data.error_code)
      }
    }
    // 修改摄像头回调函数
    const editSuccess = (data) => {
      console.log('收到编辑响应:', data)
      if (data.error_code === ErrorInfo.Success) {
        message.success('修改成功!')
        // 资源释放
        resourcePush()
        console.log('开始延时刷新列表...')
        // 添加延迟再刷新列表，确保后端数据已更新
        setTimeout(() => {
          console.log('执行延时刷新列表...')
          // 使用普通刷新，但通过getAccessMode已添加时间戳
          getAccessMode()
          // 再添加一个延迟后重新连接
          setTimeout(() => {
            console.log('重新连接摄像头...')
            reConnect()
          }, 300)
        }, 1000) // 1秒延迟
      } else {
        message.error(`修改失败!${data.error_code}`)
        console.error('修改失败，错误码:', data.error_code)
      }
    }

    // 添加摄像头回调函数
    const add = () => {
      // 如果通道为空则使用默认值0（第一通道）
      if (addData.value.channel === null) {
        addData.value.channel = 0
      }
      // 如果码流为空则使用默认值1（主码流）
      if (addData.value.stream === null | addData.value.stream === '') {
        addData.value.stream = 1
      }
      // 如果端口为空则使用默认值554（默认端口）
      if (addData.value.port === null) {
        addData.value.port = 554
      }
      // 拼接RTSP地址
      const baseUrl = 'rtsp://' + addData.value.user_name + ':' +
        addData.value.password + '@' + addData.value.ip + ':' + addData.value.port
      let streamName = ''
      if (addData.value.stream === '1') {
        streamName = 'main'
      } else if (addData.value.stream === '2') {
        streamName = 'sub'
      } else if (addData.value.stream === '3') { streamName = 'main' }
      // 海康的地址
      if (addData.value.firm === 0 && addData.value.device_type !== 2) {
        // 2012年之后的设备
        if (addData.value.old_device === 1) {
          // 如果为网络摄像机
          if (addData.value.device_type === 1) {
            addData.value.rtsp_url = baseUrl + '/Streaming/Channels/10' + addData.value.stream
          } else if (addData.value.device_type === 0) {
            // 录像机
            addData.value.rtsp_url = baseUrl + '/Streaming/Channels/' + addData.value.channel + '0' + addData.value.stream
          }
        } else {
          // 2012年之前的设备
          // 如果为网络摄像机
          if (addData.value.device_type === 1) {
            addData.value.rtsp_url = baseUrl + '/h264/ch1/' + streamName + '/av_stream'
          } else if (addData.value.device_type === 0) {
            // 录像机
            addData.value.rtsp_url = baseUrl + '/h264/ch' + addData.value.channel + '/' + streamName + '/av_stream'
          }
        }
      } else if (addData.value.firm === 1 && addData.value.device_type !== 2) {
        addData.value.rtsp_url = baseUrl + '/cam/realmonitor?Channel=' + addData.value.channel + '&subtype=' + toString(addData.value.stream - 1)
      } else if (addData.value.device_type === 2) {
        addData.value.rtsp_url = 'rtsp://' + addData.value.ip + ':2000'
        addData.value.password = ''
        addData.value.firm = 0
        addData.value.user_name = ''
        addData.value.old_device = 1
      } else if (addData.value.device_type === 3) {
        addData.value.password = ''
        addData.value.ip = '127.0.0.1'
        addData.value.firm = 0
        addData.value.user_name = ''
        addData.value.old_device = 1
        addData.value.description = ''
      }
      model = addData
      baseRequestData = reactive(new BaseRequestData(baseParam, model))

      // 显示加载信息
      message.loading({
        content: '正在添加摄像头...',
        duration: 1.5
      })

      cameraRequestAction.set(
        baseRequestData,
        addSuccess,
        (error) => {
          console.error('添加摄像头失败:', error)
          message.error('添加失败，请检查网络连接')
        },
        () => {
          console.log('添加摄像头请求已完成')
        }
      )
    }

    // 删除摄像头
    const del = (t) => {
      // 在model中找到对应的摄像头
      const targetModel = { ...model[(t.key)] }
      baseRequestData = reactive(new BaseRequestData(baseParam, targetModel))

      cameraRequestAction.delete(
        baseRequestData,
        delSuccess,
        null,
        null
      )

      // 前端列表中暂时删除对应的设备
      // 但后端成功删除后会通过 delSuccess 回调重新获取列表
      for (let i = 0; i < listData.value.length; i++) {
        if (listData.value[i].key === t.key) {
          // 删除对应下标元素
          listData.value.splice(i, 1)
          break
        }
      }

      // 在搜索列表中也删除
      for (let i = 0; i < searchList.value.length; i++) {
        if (searchList.value[i].key === t.key) {
          // 删除对应下标元素
          searchList.value.splice(i, 1)
          break
        }
      }
    }

    // 修改摄像头
    const edit = (t) => {
      // 在model中找到对应的摄像头
      if (editData.value.port === '') {
        editData.value.port = 554
      }

      let streamName = ''
      if (editData.value.stream === '1') {
        streamName = 'main'
      } else if (editData.value.stream === '2') {
        streamName = 'sub'
      } else if (editData.value.stream === '3') { streamName = 'main' }

      // 对rtsp进行重新赋值
      // 拼接RTSP地址
      const baseUrl = 'rtsp://' + editData.value.user_name + ':' +
        editData.value.password + '@' + editData.value.ip + ':' + editData.value.port

      // 海康的地址
      if (editData.value.firm === 0) {
        // 2012年之后的设备
        if (editData.value.old_device === 1) {
          // 如果为网络摄像机
          if (editData.value.device_type === 1) {
            editData.value.rtsp_url = baseUrl + '/Streaming/Channels/10' + editData.value.stream
          } else if (editData.value.device_type === 0) {
            // 录像机
            editData.value.rtsp_url = baseUrl + '/Streaming/Channels/' + editData.value.channel + '0' + editData.value.stream
          }
        } else {
          // 2012年之前的设备
          // 如果为网络摄像机
          if (editData.value.device_type === 1) {
            editData.value.rtsp_url = baseUrl + '/h264/ch1/' + streamName + '/av_stream'
          } else if (editData.value.device_type === 0) {
            // 录像机
            editData.value.rtsp_url = baseUrl + '/h264/ch' + editData.value.channel + '/' + streamName + '/av_stream'
          }
        }
      } else if (editData.value.firm === 1) {
        editData.value.rtsp_url = baseUrl + '/cam/realmonitor?Channel=' + editData.value.channel + '&subtype=' + toString(editData.value.stream - 1)
      } else if (editData.value.device_type === 2) {
        editData.value.rtsp_url = 'rtsp://' + editData.value.ip + ':2000'
      } else if (editData.value.device_type === 3) { // 设备类型为3即为rtsp流播放
        editData.value.device_type = 3
      }
      model = editData
      model.id = t.key
      baseRequestData = reactive(new BaseRequestData(baseParam, model))

      // 显示加载信息
      message.loading({
        content: '正在保存修改...',
        duration: 0.8
      })

      // 发送请求
      cameraRequestAction.set(
        baseRequestData,
        editSuccess,
        (error) => {
          console.error('修改摄像头失败:', error)
          message.error('修改失败，请检查网络连接')
        },
        () => {
          console.log('修改摄像头请求已完成')
        }
      )

      // 设置完成后隐藏弹窗
      closeEdit()
    }

    const confirm = (record) => {
      Modal.confirm({
        title: '修改设备信息',
        icon: createVNode(QuestionOutlined),
        content: '确认修改设备信息吗？',
        okText: '保存',
        cancelText: '取消',
        onOk () {
          return new Promise((resolve, reject) => {
            resolve()
            set(record)
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }

    const setSuccess = (data) => {
      if (data.error_code === ErrorInfo.Success) {
        message.success('接入模式设置成功!')
      } else {
        message.success(`接入模式设置失败!${data.error_code}`)
      }
      setAllData()
    }

    // 同步更新全局参数
    const setAllData = () => {
    }

    const set = () => {
      //  model.configData.Enabled === true ? 1 : 0
      cameraRequestAction.set(
        baseRequestData,
        setSuccess,
        null,
        null
      )
    }
    // ****************** 根据主键查询数据 *******************

    // ****************** 设备当前接入模式参数 *******************
    const getAccessMode = () => {
      // 显示加载消息
      message.loading({
        content: '正在刷新摄像头列表...',
        duration: 0.8
      })

      console.log('开始获取摄像头列表')
      // 添加时间戳参数防止缓存
      const requestData = reactive(new BaseRequestData(baseParam, {
        _t: new Date().getTime() // 时间戳参数
      }))

      cameraRequestAction.query(
        requestData,
        getAccessModeSuccess,
        (error) => {
          console.error('获取摄像头列表失败:', error)
          message.error('获取摄像头列表失败，请重试')
        },
        () => {
          console.log('获取摄像头列表请求已完成')
        }
      )
    }

    const getAccessModeSuccess = (data) => {
      console.log('获取摄像头列表成功:', data)
      // 清空列表数据，确保每次获取都是最新的
      listData.value = []
      mockData.value = []

      if (data.data && data.data.length > 0) {
        model.play_number = data.data[0].play_number
        // 对页面播放窗口数量进行初始化
        change()
        // model 数据源
        model = data.data

        // 重新构建视频列表数据
        for (let i = 0; i < model.length; i++) {
          listData.value.push({
            title: model[i].device_name,
            description: model[i].description,
            key: model[i].id - 1,
            rtsp: model[i].rtsp_url
          })
          mockData.value[i] = listData.value[i]
        }
        console.log('摄像头列表数据已更新，共', listData.value.length, '个摄像头')
      } else {
        // 如果没有数据，确保列表为空
        listData.value = []
        mockData.value = []
        searchList.value = []
        playList.value = []
        console.log('未获取到摄像头数据')
      }
    }
    // ****************** 当前接入模式参数 *******************

    onMounted(() => {
      console.log(ServerAddr.serverAddr)
      const config_str = sessionStorage.getItem('mesh_Config')
      let json_obj = {}
      if (config_str != null) {
        json_obj = JSON.parse(config_str)
        if (json_obj.map_server == null) {
          console.log('视频播放地址无效！')
          return
        }
      }
      ServerAddr.serverAddr = json_obj.video_server
      console.log(ServerAddr.serverAddr)

      // 初始化视频加载状态数组
      videoLoaded.value = new Array(actions.number).fill(false)

      // 在页面加载的时候对播放状态变量进行初始化,
      for (let i = 0; i < actions.number; i++) {
        videoStatuses[i] = false
      }
      model.id = pageDirectAction.getCurrentRouteValue()
      getAccessMode()
      /**
       * 视频播放事件处理,主要监听视频中断播放
       */
      const video = document.getElementById('1')
      video.addEventListener('loadeddata', function (e) {
      })
      /* 隐藏所有的录制UI提示 */
      concealREC()

      // 初始化容器高度
      nextTick(() => {
        adjustContainerHeight()
      })

      // 添加窗口大小变化监听
      window.addEventListener('resize', adjustContainerHeight)

      // 添加全屏变化事件监听
      document.addEventListener('fullscreenchange', handleFullscreenChange)
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.addEventListener('mozfullscreenchange', handleFullscreenChange)
      document.addEventListener('MSFullscreenChange', handleFullscreenChange)
    })

    // 组件卸载时移除监听器
    onUnmounted(() => {
      // 组件销毁时释放已分配的资源
      resourcePush()

      // 移除窗口大小变化监听
      window.removeEventListener('resize', adjustContainerHeight)

      // 移除全屏变化事件监听
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    })

    const onFinish = (values) => {
    }

    // 对播放页面添加样式
    // 对分屏进行添加样式
    const addStyle = (value) => {
      if (value === 0) {
        actions.number = 1
        actions.span = 24
        actions.style = 'play_one'
      } else if (value === 1) {
        actions.number = 4
        actions.span = 12
        actions.style = 'play_four'
      } else if (value === 2) {
        actions.number = 9
        actions.span = 8
        actions.style = 'play_nine'
      } else if (value === 3) {
        actions.number = 16
        actions.span = 6
        actions.style = 'play_16'
      }

      // 通知Vue在下一个渲染周期调整容器高度
      nextTick(() => {
        adjustContainerHeight()
      })
    }

    // 调整容器高度以适应多路播放
    const adjustContainerHeight = () => {
      const mainContainer = document.querySelector('.main-video-container')
      if (!mainContainer) return

      // 根据播放路数设置容器高度
      if (actions.number === 1) {
        // 单路播放 - 使用较大高度
        mainContainer.style.height = 'calc(100vh - 240px)'
      } else if (actions.number === 4) {
        // 四路播放
        mainContainer.style.height = 'calc(100vh - 220px)'
      } else if (actions.number === 9) {
        // 九路播放
        mainContainer.style.height = 'calc(100vh - 200px)'
      } else if (actions.number === 16) {
        // 十六路播放 - 稍微压缩高度以确保所有窗口可见
        mainContainer.style.height = 'calc(100vh - 180px)'
      }
    }

    // 播放路数下拉框change回调
    const change = async value => {
      if (videoStatus === true) {
        // 弹出提示框，首先停止所有视频的录制
      } else {
        // 切换播放路数
        // 保存播放路数
        voidConfig.plays = value
        // 添加样式
        addStyle(value)
        // 初始化视频加载状态数组
        videoLoaded.value = new Array(actions.number).fill(false)
        // 资源释放
        resourcePush()
        /**
       * 等待tick更新，当更改响应式状态时候最终的DOM更新不是同步生效
       * vue将它缓存在一个队列中，等待下一个tick才一起进行执行
       * 当用户点击下拉框的时候将对应的响应式进行改变后此时页面DOM还没有更新
       * 使用nextTick函数将不会等待tick,会进行直接更改，这样后面的reConnect()才能获取到DOM元素
       * ************************************************************************
       * *** 使用nextTick的时候必须和await同时使用并且当前方法必须加async进行异步 ***
       * ************************************************************************
       */
        await nextTick()
        // 重新连接
        reConnect()
        // 清除所有的录制UI
        concealREC()

        /**
         * 当用户完成切换播放路数的时候，按照播放路数自动初始化mediaRecorders数组容量，并进行赋值
         * 与下面的for循环尽量分开，不要合并。便于程序理解（后期维护时注意！！！）
         */
        // 这里使用actions.number作为循环容量，是因为在此之前就已经对其数量初始化
        for (let i = 0; i < actions.number; i++) {
          // 默认为空值，因为这里初始化的时候用的null 在做条件的时候需要特别注意
          mediaRecorders[i] = null
          streams[i] = null
        }
        // 初始化记录录制状态的数组
        for (let i = 0; i < actions.number; i++) {
          videoStatuses[i] = false
        }
      }
    }

    // 创建数组保存webRTC对象
    let RTC = []

    // 选择播放路数后的资源释放
    const resourcePush = () => {
      // 释放预览资源
      pushOneResource()
      // 第一次加载不进行断流操作
      if (RTC.length !== 0) {
        // 改变路数的时候断开所有的webrtc连接，用来防止内存泄漏
        for (let i = 0; i < RTC.length; i++) {
          RTC[i].disconnect()
        }
        // 释放完资源清空数组
        RTC = []
      }
    }

    /**
     * 判断选中窗口的录制状态，如果在录制就将按钮上的文字修改为结束录制
     * 如果不在录制，就将按钮上的文字修改为开始录制.
     * 调用时机：当用户点击播放窗口的时候进行调用
     */
    const recJudge = () => {
      const buttonObj = document.getElementById('start')
      // 判断用户点击的窗口播放状态
      if (videoStatuses[windowId - 1] === true) {
        // ；录制状态将按钮的对应状态进行改变，文字改为结束录制
        // 获取到按钮对象
        buttonObj.innerHTML = '停止录制'
      } else {
        // 停止录制状态，将按钮上的文字显示为开始录制
        // 获取到按钮对象
        buttonObj.innerHTML = '开始录制'
      }
    }
    // 重新创建webrtc连接
    const reConnect = () => {
      // 改变完成路数后根据路数进行重新连接，用于保证画面稳定
      // 如果选择的页面小于或等于数组容量，则恢复播放路数数量的画面
      if (actions.number <= listData.value.length) {
        for (let i = 0; i < actions.number; i++) {
          RTC.push(new WebRtcStreamer(
            (i + 1).toString(), ServerAddr.serverAddr
          ))
          RTC[i].connect(listData.value[i].rtsp, listData.value[i].rtsp, options)
        }
      } else {
        // 否则恢复数组容量数量的画面
        for (let i = 0; i < listData.value.length; i++) {
          RTC.push(new WebRtcStreamer(
            (i + 1).toString(), ServerAddr.serverAddr
          ))
          RTC[i].connect(listData.value[i].rtsp, listData.value[i].rtsp, options)
        }
      }
    }

    // 定义变量rtsp 暂存rtsp地址,与video窗口id,暂存播放路数
    const voidConfig = {
      rtsp: '',
      id: '',
      plays: 1
    }
    // 创建变量保存webRTC对象用于释放资源
    const webRtc = []
    // 选择设备(摄像头)下拉框回调
    const selectDeviceChange = value => {
      // 在保存用户上次的视频下拉框选定的值的时候对数组进行排空，没有选定的使用占位
      selectVideo[voidConfig.id] = value
      voidConfig.plays = value
      // 拿到对应设备的rtsp地址
      for (let i = 0; i < playList.value.length; i++) {
        if (playList.value[i].key === value) {
          voidConfig.rtsp = playList.value[i].rtsp
        }
      }
      // 找到当前video的webrtc连接
      for (let n = 0; n < webRtc.length; n++) {
        if (webRtc[n].id === voidConfig.id) {
          // 如果当前窗口已经产生连接则首先断开连接
          if (webRtc[n].isConnect === true) {
            webRtc[n].webRtcServer.disconnect()
            // 将连接状态置为false
            webRtc[n].isConnect = false
            // 创建新的连接替换数组中老的连接
            // 断开当前连接后继续创建新的连接
            webRtc[n].webRtcServer = new WebRtcStreamer(
              voidConfig.id, ServerAddr.serverAddr
            )
            webRtc[n].webRtcServer.connect(
              voidConfig.rtsp, voidConfig.rtsp, options
            )
            webRtc[n].rtsp = voidConfig.rtsp
            // 将连接状态置为true
            webRtc[n].isConnect = true
          } else {
            // 没有连接则直接创建连接,并保存在数组中
            webRtc[n].webRtcServer = new WebRtcStreamer(
              voidConfig.id, ServerAddr.serverAddr
            )
            webRtc[n].webRtcServer.connect(
              voidConfig.rtsp, voidConfig.rtsp, options
            )
            webRtc[n].rtsp = voidConfig.rtsp
            // 将连接状态置为true
            webRtc[n].isConnect = true
          }
          return
        }
      }
    }

    // 点击选择设备下拉框的回调函数，当用户点击后将对应video id 下发
    const clickDeviceChange = (n) => {
      voidConfig.id = n.toString()

      // 如果webRtc数组长度为0 则进行创建数组
      if (webRtc.length === 0) {
        webRtc.push({
          id: n.toString(),
          webRtcServer: null,
          isConnect: false,
          rtsp: null
        })
      } else {
        // 长度不为零，则进行遍历.如果存在直接结束，否则不存在则进行添加
        for (let i = 0; i < webRtc.length; i++) {
          if (webRtc[i].id === n.toString()) {
            return
          }
        }
        // 不存在则进行追加
        webRtc.push({
          id: n.toString(),
          webRtcServer: null,
          isConnect: false,
          rtsp: null
        })
      }
    }

    // 点击刷新按钮的处理函数
    const handleRefresh = () => {
      // 直接调用getAccessMode函数，该函数已添加时间戳防止缓存
      getAccessMode()
      // 添加延迟后重新连接摄像头
      setTimeout(() => {
        reConnect()
      }, 300)
    }

    return {
      img_url,
      mockData,
      addCameraVisible,
      AuthUi,
      clickVideo,
      editConfirm,
      openAddCamera,
      closeAddCamera,
      addConfirm,
      showPlayOne,
      selectDeviceChange,
      getRowSelection,
      clickDeviceChange,
      delConfirm,
      showEdit,
      closeEdit,
      openSearch,
      closeSearch,
      mouseover,
      mouseout,
      play_number,
      showSearch,
      addData,
      editData,
      editVisible,
      choseDeviceShow,
      model,
      baseRequestData,
      dataSource,
      baseParam,
      LabelCaption,
      DeviceinfoColumns,
      pageDirectAction,
      visiblePlay,
      selectVideo,
      channel,
      firm,
      type,
      stream,
      years,
      searchList,
      searchData,
      videoStatus,
      play,
      pause,
      confirm,
      Pattern,
      onFinish,
      getAccessMode,
      showModal,
      handleOk,
      canSelect,
      playList,
      placement,
      visible,
      showDrawer,
      onClose,
      change,
      goPlays,
      Plays,
      listData,
      pagination,
      actions,
      handleRefresh,
      videoLoaded,
      videoLoadedHandler,
      adjustContainerHeight,
      isFullscreen,
      fullscreenVideo,
      toggleFullscreen,
      handleFullscreenChange,
      selectedVideo
    }
  }
})
</script>

 <style scoped>
/* 主视频容器样式 */
.main-video-container {
  padding: 15px;
  width: 100%;
}

.main-video-container .ant-row {
  margin: -10px !important;
}

.main-video-container .ant-col {
  padding: 10px !important;
}

/* 视频控制器样式 */
.video-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-controls {
  opacity: 1;
}

.fullscreen-btn {
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.fullscreen-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  color: #1890ff;
}

/* 单路播放样式 */
.play_one {
  object-fit: fill;
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 260px);
  border: none;
  transition: all 0.3s ease;
}

.play_four {
  object-fit: fill;
  width: 100%;
  height: 100%;
  max-height: calc((100vh - 300px) / 2);
  border: none;
  transition: all 0.3s ease;
}

.play_nine {
  object-fit: fill;
  width: 100%;
  height: 100%;
  max-height: calc((100vh - 320px) / 3);
  border: none;
  transition: all 0.3s ease;
}

.play_16 {
  object-fit: fill;
  width: 100%;
  height: 100%;
  max-height: calc((100vh - 340px) / 4);
  border: 1px solid #1296db;
}
/* 设备播放预览 */
.one {
  object-fit: fill;
  width: 100%;
  height: 100%;
}
/* 点击窗口后的添加样式 */
.border {
  border: 3px solid #1effda;
}
/* 录像开始提示样式 */
.rec {
  width: auto;
  padding: 1%;
  background-color: #0000005e;
  position: absolute;
  z-index: 999;
  margin-left: 2%;
  margin-top: 2%;
}

/* 视频容器样式 */
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  background: linear-gradient(145deg, #1a2a3a, #0d1620);
  border: 1px solid rgba(30, 144, 255, 0.2);
}

/* 科技感边框 */
.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
  pointer-events: none;
  z-index: 2;
}

/* 科技感角落 */
.video-container::after {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  border-top: 2px solid rgba(30, 144, 255, 0.5);
  border-left: 2px solid rgba(30, 144, 255, 0.5);
  top: 5px;
  left: 5px;
  border-radius: 2px 0 0 0;
  z-index: 3;
}

/* 添加四个角落装饰 */
.video-container .corner-tl,
.video-container .corner-tr,
.video-container .corner-bl,
.video-container .corner-br {
  position: absolute;
  width: 15px;
  height: 15px;
  z-index: 3;
}

.video-container .corner-tl {
  top: 5px;
  left: 5px;
  border-top: 2px solid rgba(30, 144, 255, 0.5);
  border-left: 2px solid rgba(30, 144, 255, 0.5);
  border-radius: 2px 0 0 0;
}

.video-container .corner-tr {
  top: 5px;
  right: 5px;
  border-top: 2px solid rgba(30, 144, 255, 0.5);
  border-right: 2px solid rgba(30, 144, 255, 0.5);
  border-radius: 0 2px 0 0;
}

.video-container .corner-bl {
  bottom: 5px;
  left: 5px;
  border-bottom: 2px solid rgba(30, 144, 255, 0.5);
  border-left: 2px solid rgba(30, 144, 255, 0.5);
  border-radius: 0 0 0 2px;
}

.video-container .corner-br {
  bottom: 5px;
  right: 5px;
  border-bottom: 2px solid rgba(30, 144, 255, 0.5);
  border-right: 2px solid rgba(30, 144, 255, 0.5);
  border-radius: 0 0 2px 0;
}

.video-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* 选中效果 */
.border {
  /* 移除这个属性，它会覆盖原本的边框 */
  /* border: none !important; */
}

.border video {
  filter: contrast(1.05) brightness(1.05);
}

.border .video-container {
  box-shadow: 0 0 0 3px rgba(30, 255, 218, 0.7), 0 0 20px rgba(30, 255, 218, 0.5) !important;
  animation: pulse 2s infinite;
  border: 3px solid #1effda !important;
}

.border .corner-tl,
.border .corner-tr,
.border .corner-bl,
.border .corner-br {
  border-color: rgba(30, 255, 218, 0.7);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 2px rgba(30, 255, 218, 0.7), 0 0 20px rgba(30, 255, 218, 0.5);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(30, 255, 218, 0.7), 0 0 25px rgba(30, 255, 218, 0.7);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(30, 255, 218, 0.7), 0 0 20px rgba(30, 255, 218, 0.5);
  }
}

/* 视频占位样式 */
.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #1a2a3a 0%, #0d1620 100%);
  z-index: 5;
}

.placeholder-icon {
  margin-bottom: 20px;
}

.placeholder-camera {
  position: relative;
  width: 80px;
  height: 60px;
}

.camera-body {
  position: absolute;
  width: 80px;
  height: 40px;
  background-color: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.camera-lens {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: radial-gradient(circle, #0f2027 0%, #203a43 50%, #2c5364 100%);
  border: 3px solid #34495e;
  top: 5px;
  left: 25px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8);
}

.camera-flash {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #3498db;
  top: 5px;
  right: 10px;
  box-shadow: 0 0 10px #3498db;
  animation: flash 2s infinite;
}

.placeholder-text {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes flash {
  0%, 100% {
    opacity: 0.5;
    box-shadow: 0 0 5px #3498db;
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 15px #3498db, 0 0 30px #3498db;
  }
}

/* 视频选中边框样式 */
.video-selected {
  border: 3px solid #1effda !important;
  box-shadow: 0 0 15px rgba(30, 255, 218, 0.7) !important;
  transform: scale(1.01) !important;
  transition: all 0.3s ease !important;
}

/* 选中视频的脉动效果 */
.selected-video {
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(30, 255, 218, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(30, 255, 218, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(30, 255, 218, 0);
  }
}
</style>
