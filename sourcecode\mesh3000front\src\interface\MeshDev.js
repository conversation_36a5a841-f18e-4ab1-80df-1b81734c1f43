// Mesh设备的添加、设置、修改和删除

const axios = require('axios').default
// let Utils = require('./common/utils').Utils;

class MeshDev {
  getMeshDevList (argc, callback) {
    console.info('getMeshDevList')
    axios.post('http://127.0.0.1/getMeshDevList', argc)
      .then(response => {
        if (response.data.code !== 200) {
          console.info('getMeshDevList:' + response)
        } else {
          callback(response.data)
        }
      }).catch(error => {
        console.warn(error)
      })
  }

  getMeshDev (argc, callback) {
    console.info('getMeshDev')
    axios.post('http://127.0.0.1/getMeshDev', argc)
      .then(response => {
        if (response.data.code !== 200) {
          console.info('getMeshDev:' + response)
        } else {
          callback(response.data)
        }
      }).catch(error => {
        console.warn(error)
      })
  }

  addMeshDev (argc, callback) {
    console.info('addMeshDev')
    axios.post('http://127.0.0.1/addMeshDev', argc)
      .then(response => {
        if (response.data.code !== 200) {
          console.info('addMeshDev:' + response)
        } else {
          callback(response.data)
        }
      }).catch(error => {
        console.warn(error)
      })
  }

  setMeshDev (argc, callback) {
    axios.post('http://127.0.0.1/setMeshDev', argc)
      .then(response => {
        if (response.data.code !== 200) {
          console.info('setMeshDev:' + response)
        } else {
          callback(response.data)
        }
      }).catch(error => {
        console.warn(error)
      })
  }

  delMeshDev (argc, callback) {
    axios.post('http://127.0.0.1/delMeshDev', argc)
      .then(response => {
        if (response.data.code !== 200) {
          console.info('delMeshDev:' + response)
        } else {
          callback(response.data)
        }
      }).catch(error => {
        console.warn(error)
      })
  }
}
export { MeshDev }
