<template>
    <v-card class="ma-2">
      <v-container>
        <v-layout>
          正在进入......
          <div id="meet" style="position:absolute; width:86%; height:89%"></div>
          <!-- missing -->
        </v-layout>
      </v-container>
    </v-card>
</template>

<script>

import { onMounted, defineComponent } from 'vue'
import { PageDirectAction } from '@/views/deviceinfo/action/pageDirectAction'
import Router from '@/router/index'

export default defineComponent({
  components: {
  },
  setup () {
    const pageDirectAction = new PageDirectAction()
    onMounted(() => {
      let str = ''
      str = pageDirectAction.getCurrentRouteValue()
      if (str === 'joinMeetingByRoute') {
        Router.push({
          path: 'meetindex',
          query: {
            id: 'joinMeetingByRoute'
          }
        })
      }
    })
  }

})
</script>
