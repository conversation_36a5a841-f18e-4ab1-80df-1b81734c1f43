'use strict'

// 前端项目端口号
const port = process.env.port || process.env.npm_config_port || 80 // dev port
// const { getThemeVariables } = require('ant-design-vue/dist/theme')
const os = require('os')
let needHost = '0.0.0.0'
try {
  const network = os.networkInterfaces()
  needHost = network[Object.keys(network)[0]][1].address// 获取当前IP地址
} catch {
}

const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    port: port,
    proxy: {

      '/api': {
        // 本机后端程序
        // target: 'http://localhost:8989/',
        // 服务器后端程序
        // target: 'http://***************:8989/',
        // 调试端后端程序
        // target: 'http://***************:8989/',
        target: 'http://***************:8989/',
        // target: 'http://***************:8989/',
        // 叶师1
        //  target: 'http://***************:8989/',
        // 叶师2
        // target: 'http://***************:8989/',
        // 小张
        // target: 'http://***************:8989/',
        changeOrigin: true,
        pathRewrite: { '^/api': '/api' }
      }
    },
    https: true
  },

  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: 'MESH3000-FRONT'
  },

  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            'primary-color': '#00ffdaad', // 主样式颜色

            'heading-color': '#aafff4d7', // 标题颜色
            'component-background': '#162130ce', // 组件背景色
            // 'dropdown-selected-color': '#162130ce',
            'table-header-bg': '#162130', // 表格表头颜色
            'table-footer-bg': '#162130', // 表格页脚颜色
            'table-footer-color': '#162130',
            'layout-body-background': '#162130',
            'table-row-hover-bg': '#00ffdd5d',
            // 'dropdown-menu-bg': '#00ffdd5d',
            // 'select-item-active-bg ': '#00ffdd5d',
            // 'item-active-bg': '#00ffdd5d',
            'table-selected-row-bg': '#00ffdd5d',
            'select-item-selected-bg ': '#00ffdd5d',
            'select-item-active-bg ': '#00ffdd5d',
            'menu-item-active-bg': '#00ffdd5d',
            'tree-bg': '#FFFFFF00',
            'tree-node-selected-bg': '#00ffdaff',
            // 'dropdown-menu-bg': 'red',
            // dropdown-menu-bg: @component-background;
            'divider-color': '#FFFFFF',
            'link-color': '#4169E1', // 链接字体大小
            'font-size-base': ' 18px', // 主字体大小
            'text-color': '#FFFFFF', // 字体颜色
            'border-radius-base': '2px', // 边框圆角半径
            'border-color-base': '#aafff4d7', // 边框色
            'box-shadow-base': '0 20px 8px rgba(0, 0, 0, 0.18)', // 浮层阴影
            'switch-color': '#aafff4d7', // switch颜色
            'switch-bg': '#aafff4d7', // 背景
            'icon-color': '#FFFFFF',
            'input-icon-color': 'red',
            'select-disabled-background': '#00ffaa',
            'text-color-secondary': 'fade(#ffffff, 45%)',
            'item-hover-bg': '#00958c',
            'item-active-bg': '#00716c',
            'picker-basic-cell-active-with-range-color': '#00716c'
          },
          // 需要做大量的调整
          //   modifyVars: getThemeVariables({
          //     dark: true // 开启暗黑模式
          //   }),
          javascriptEnabled: true
        }
      }
    }
  }
})
